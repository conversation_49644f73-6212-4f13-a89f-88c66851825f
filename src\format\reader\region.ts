import Document from '../../model/core/Document';
// tslint:disable-next-line: max-line-length
import { INewControlProperty, rtNode, IModeFonts, IParseXmlNode, FILE_HEADER_VERSION2, IReadDocOption } from '../../common/commonDefines';
import { Region } from '../../model/core/Region';
import { IContentControlDescObj, IInsertFileContent, IUniqueImageProps } from './reader';
import { ParagraphReader } from './paragraph';
import DocumentContentElementBase from 'src/model/core/DocumentContentElementBase';
import { readCustomProperties } from '../miscellaneous';
import { TableReader } from './table';
import { readerRegionProps } from './commont';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';

export class RegionReader {

  public static traverseRegion(node: any, document: Document, ContentControlDescObj: IContentControlDescObj,
                               uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                               paras: DocumentContentElementBase[], bNoEndPara: boolean = false, parent: Region = null,
                               depth: number, forceIgnoreRegion: boolean = false): void {
    let ignoreRegion = false;
    // when insert a file containing region(s) into a region, ignore the region(s)
    if (bInsertFile === true && forceIgnoreRegion === true) {
      ignoreRegion = true;
    }

    const regionProps: INewControlProperty = {
      showTitle: false,
    } as any;
    const region = new Region(document, document, regionProps);

    // rg attrs(ignore type, mode for now)
    const nameAttr = node.attributes.getNamedItem('name');
    if (nameAttr) {
      regionProps.newControlName = nameAttr.nodeValue;
    }

    const regionChildren = node.children;
    // for (let i = 0; i < regionChildren.length; i++) {
    for (const regionChild of regionChildren) {

      // const regionChild = regionChildren[i];

      if (regionChild.nodeName === 'rgPr') {

        if (ignoreRegion === true) {
          continue;
        }

        let rgPropChild = regionChild.children[0];

        while (rgPropChild) {

          // dont overuse .children!  <aaa>1</aaa> has no children!
          const textNode = rgPropChild.childNodes[0]; // usually just text node, but may exist childnodes
          switch (rgPropChild.nodeName) {
            case 'deleteProtect': {
              if (textNode) {
                regionProps.isNewControlCanntDelete = textNode.nodeValue === '1' ? true : false;
              }
              break;
            }

            case 'editProtect': {
              if (textNode) {
                regionProps.isNewControlCanntEdit = textNode.nodeValue === '1' ? true : false;
              }
              break;
            }

            case 'hidden': {
              if (textNode) {
                regionProps.isNewControlHidden = textNode.nodeValue === '1' ? true : false;
              }
              break;
            }

            case 'editReverse': {
              if (textNode) {
                regionProps.isNewControlReverseEdit = textNode.nodeValue === '1' ? true : false;
              }
              break;
            }

            case 'title': {
              if (textNode != null) {
                regionProps.newControlTitle = textNode.nodeValue;
              }
              break;
            }

            case 'showTitle': {
              if (textNode != null) {
                regionProps.showTitle = textNode.nodeValue === '1' ? true : false;
              }
              break;
            }

            case 'beginPos': {
              //
              break;
            }

            case 'endPos': {
              //
              break;
            }

            case 'serialNumber': {
              if (textNode != null) {
                regionProps.newControlSerialNumber = textNode.nodeValue;
              }
              break;
            }

            case 'maxLength': {
              if (textNode != null) {
                regionProps.newControlMaxLength = textNode.nodeValue;
              }
              break;
            }

            case 'customProperty': {
              const customProperties = rgPropChild.children;
              if (customProperties.length > 0) { // usually if in this block, length must be > 0

                const customPropertyArr = readCustomProperties(customProperties);
                // console.log(customPropertyArr)
                regionProps.customProperty = customPropertyArr;
              }
              break;
            }

            default: {
              break;
            }
          }
          rgPropChild = rgPropChild.nextElementSibling;
        }
        // console.log(regionProps)
        region.setProperty(regionProps, true);

      } else if (regionChild.nodeName === 'w:p') {
        let container = region;
        if (ignoreRegion === true) {
          container = parent;
        }
        ParagraphReader.traverseParagraph(regionChild, document, ContentControlDescObj,
          uniqueImagelist, bInsertFile, paras, bNoEndPara, container);

      } else if (regionChild.nodeName === 'rg') {
        // recursive
        depth++;
        let container = region;
        if (ignoreRegion === true && depth >= 2) {
          container = parent;
        }
        this.traverseRegion(regionChild, document, ContentControlDescObj,
          uniqueImagelist, bInsertFile, paras, bNoEndPara, container, depth, forceIgnoreRegion);
        depth--;
      } else if (regionChild.nodeName === 'w:tbl') {
        // table in region
        TableReader.traverseTable(regionChild, document, ContentControlDescObj, uniqueImagelist,
          bInsertFile, paras, bNoEndPara, null, region);
      } else {
        // none
      }
    }

    if (ignoreRegion === true) {
      return ;
    }
    // console.log(depth, region)

    const regionManager = document.getRegionManager();
    // max 2 potential recursive
    if (depth === 2 && parent != null) {
      // just need to come in once: the inner recursive one
      // parent may have already been added by other inner recursive region
      const parentName = parent.getName();
      if (regionManager.getRegionByName(parentName) == null) {
        if (!bInsertFile) { // open file
          document.addToContent(document.content.length, parent, true);
        } else {
          this.addRegionToParas(paras, parent, document);
        }
        regionManager.addRegion(parent, undefined);
      }
      parent.addToContent(parent.getContent().length, region);
      regionManager.addLeaf(parentName, region);

    } else if (depth === 1) {
      // only one case to add: one layer region, no parent
      // if depth 1 which has child region (thus already added), ignore it
      if (regionManager.getRegionByName(region.getName()) == null) {
        if (!bInsertFile) { // open file

          document.addToContent(document.content.length, region, true);
        } else {
          // document.addPasteContent(paras, false);
          this.addRegionToParas(paras, region, document);
        }

        regionManager.addRegion(region, undefined);
      } else {
        // insert file, dup name, but ignore 'already added'
        if (bInsertFile) {
          // dup name shouldn't exist in paras(okay if whole document has dup with any item in paras)
          const name = region.getName();
          let addFlag = true;
          for (const para of paras) {
            if (para instanceof Region) {
              if (name === para.getName()) {
                addFlag = false;
                break;
              }
            }
          }
          if (addFlag === true) {
            this.addRegionToParas(paras, region, document);
            regionManager.addRegion(region, undefined);
          }
        }
      }
    }

  } // traverseRegion

  public static tTraverseRegion(node: rtNode, document: Document, ContentControlDescObj: IContentControlDescObj,
                                uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                                paras: DocumentContentElementBase[], bNoEndPara: boolean = false, parent: Region = null,
                                depth: number, forceIgnoreRegion: boolean = false,
                                index: number, nodes: (string | number | rtNode)[]): void {
    let ignoreRegion = false;
    // when insert a file containing region(s) into a region, ignore the region(s)
    if (bInsertFile === true && forceIgnoreRegion === true) {
      ignoreRegion = true;
    }

    const documentVersion = document != null ? document.getDocumentVersion() : 0;

    const regionProps: INewControlProperty = {
      showTitle: false,
    } as any;
    const region = new Region(document, document, regionProps);

    // rg attrs(ignore type, mode for now)
    const nameAttr = node.attributes['name'];
    if (nameAttr) {
      regionProps.newControlName = nameAttr;
    }

    const regionChildren = node.children;
    // for (let i = 0; i < regionChildren.length; i++) {
    for (const regionChild of regionChildren) {

      // const regionChild = regionChildren[i];
      if (typeof regionChild === 'object') {
        if (regionChild.tagName === 'rgPr') {

          if (ignoreRegion === true) {
            continue;
          }

          const rgPropChildren = regionChild.children;

          for (const rgPropChild of rgPropChildren) {
            if (typeof rgPropChild === 'object') {
              const textNode = rgPropChild.children[0]; // usually just text node, but may exist childnodes
              readerRegionProps(regionProps, rgPropChild, textNode, documentVersion);
              // switch (rgPropChild.tagName) {
              //   case 'deleteProtect': {
              //     if (textNode) {
              //       regionProps.isNewControlCanntDelete = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'editProtect': {
              //     if (textNode) {
              //       regionProps.isNewControlCanntEdit = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'hidden': {
              //     if (textNode) {
              //       regionProps.isNewControlHidden = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'editReverse': {
              //     if (textNode) {
              //       regionProps.isNewControlReverseEdit = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'title': {
              //     if (textNode != null && typeof textNode === 'string') {
              //       regionProps.newControlTitle = safeDecodeURIComponent(textNode, documentVersion);
              //     }
              //     break;
              //   }
              //   case 'tip': {
              //     if (textNode != null && typeof textNode === 'string') {
              //       regionProps.newControlInfo = safeDecodeURIComponent(textNode, documentVersion);
              //     }
              //     break;
              //   }

              //   case 'showTitle': {
              //     if (textNode != null) {
              //       regionProps.showTitle = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'beginPos': {
              //     //
              //     break;
              //   }

              //   case 'endPos': {
              //     //
              //     break;
              //   }

              //   case 'serialNumber': {
              //     if (textNode != null && typeof textNode === 'string') {
              //       regionProps.newControlSerialNumber = textNode;
              //     }
              //     break;
              //   }

              //   case 'maxLength': {
              //     if (textNode != null && typeof textNode === 'string') {
              //       regionProps.newControlMaxLength = parseInt(textNode);
              //     }
              //     break;
              //   }

              //   case 'customProperty': {
              //     const customProperties = rgPropChild.children;
              //     if (customProperties.length > 0) { // usually if in this block, length must be > 0

              //       const customPropertyArr = tReadCustomProperties(customProperties);
              //       // console.log(customPropertyArr)
              //       regionProps.customProperty = customPropertyArr;
              //     }
              //     break;
              //   }

              //   default: {
              //     break;
              //   }
              // }

            }

          }
          // console.log(regionProps)
          region.setProperty(regionProps, true);

        } else if (regionChild.tagName === 'w:p') {
          let container = region;
          if (ignoreRegion === true) {
            container = parent;
          }
          ParagraphReader.tTraverseParagraph(regionChild, document, ContentControlDescObj,
            uniqueImagelist, bInsertFile, paras, bNoEndPara, container, null, index, nodes);

        } else if (regionChild.tagName === 'rg') {
          // recursive
          depth++;
          let container = region;
          if (ignoreRegion === true && depth >= 2) {
            container = parent;
          }
          this.tTraverseRegion(regionChild, document, ContentControlDescObj,
            uniqueImagelist, bInsertFile, paras, bNoEndPara, container, depth, forceIgnoreRegion,
            index, nodes);
          depth--;
        } else if (regionChild.tagName === 'w:tbl') {
          // table in region
          TableReader.tTraverseTable(regionChild, document, ContentControlDescObj, uniqueImagelist,
            bInsertFile, paras, bNoEndPara, null, region, index, nodes);
        } else {
          // none
        }
      }

    }

    if (ignoreRegion === true) {
      return;
    }
    // console.log(depth, region)

    const regionManager = document.getRegionManager();
    // max 2 potential recursive
    if (depth === 2 && parent != null) {
      // just need to come in once: the inner recursive one
      // parent may have already been added by other inner recursive region
      const parentName = parent.getName();
      if (regionManager.getRegionByName(parentName) == null) {
        if (!bInsertFile) { // open file
          document.addToContent(document.content.length, parent, true);
        } else {
          this.addRegionToParas(paras, parent, document);
        }
        regionManager.addRegion(parent, undefined);
      }
      parent.addToContent(parent.getContent().length, region);
      regionManager.addLeaf(parentName, region);

    } else if (depth === 1) {
      // only one case to add: one layer region, no parent
      // if depth 1 which has child region (thus already added), ignore it
      if (regionManager.getRegionByName(region.getName()) == null) {
        if (!bInsertFile) { // open file

          document.addToContent(document.content.length, region, true);
        } else {
          // document.addPasteContent(paras, false);
          this.addRegionToParas(paras, region, document);
        }

        regionManager.addRegion(region, undefined);
      } else {
        // insert file, dup name, but ignore 'already added'
        if (bInsertFile) {
          // dup name shouldn't exist in paras(okay if whole document has dup with any item in paras)
          const name = region.getName();
          let addFlag = true;
          for (const para of paras) {
            if (para instanceof Region) {
              if (name === para.getName()) {
                addFlag = false;
                break;
              }
            }
          }
          if (addFlag === true) {
            this.addRegionToParas(paras, region, document);
            regionManager.addRegion(region, undefined);
          }
        }
      }
    }

  } // traverseRegion

  public static tTraverseRegion2(node: rtNode, document: Document, ContentControlDescObj: IContentControlDescObj,
                                 uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                                 paras: DocumentContentElementBase[],
                                 parent: Region = null, depth: number, forceIgnoreRegion: boolean = false,
                                 modeFonts: IModeFonts = null, sectionEnds: any[] = [],
                                 bInsertFromRegion: boolean = false, insertRegionNames: string[] = [], options: IReadDocOption = {}): void {
    let ignoreRegion = false;
    // when insert a file containing region(s) into a region, ignore the region(s)
    if (bInsertFile === true && forceIgnoreRegion === true) {
      ignoreRegion = true;
    }

    const documentVersion = document != null ? document.getDocumentVersion() : 0;

    const regionProps: INewControlProperty = {
      showTitle: false,
    } as any;
    const region = new Region(document, document, regionProps);

    // rg attrs(ignore type, mode for now)
    const nameAttr = node.attributes['name'];
    if (nameAttr) {
      regionProps.newControlName = nameAttr;
    }

    const regionChildren = node.children;
    const regionManager = document.getRegionManager();
    const bNewVersion = FILE_HEADER_VERSION2 === documentVersion;

    // for (let i = 0; i < regionChildren.length; i++) {
    for (const regionChild of regionChildren) {

      // const regionChild = regionChildren[i];
      if (typeof regionChild === 'object') {
        if (regionChild.tagName === 'rgPr') {

          if (ignoreRegion === true) {
            continue;
          }

          const rgPropChildren = regionChild.children;

          for (const rgPropChild of rgPropChildren) {
            if (typeof rgPropChild === 'object') {
              const textNode = rgPropChild.children[0]; // usually just text node, but may exist childnodes
              readerRegionProps(regionProps, rgPropChild, textNode, documentVersion);
            }

          }
          // console.log(regionProps)
          region.setProperty(regionProps, true);
          this.checkDuplicateRegionName(region, document, insertRegionNames);

          // check if modeFonts has regiontitletextprop
          // if (modeFonts != null) {
          //   const {defaultFont, regionTitleFont} = modeFonts;
          //   if (regionTitleFont != null) {
          //     region.setTitlePortionTextProperty(regionTitleFont);
          //   }
          //   // else if (defaultFont != null) {
          //   //   region.setTitlePortionTextProperty(defaultFont);
          //   // }
          // }

        } else if (regionChild.tagName === 'w:p') {
          let container = region;
          if (ignoreRegion === true) {
            container = parent;
          }
          let bAddRegionToParas = true;
          if (bInsertFromRegion === false && depth === 2) {
            // bAddRegionToParas: if not inserted from region, and the para is in depth 2 region -> false
            bAddRegionToParas = false;
          }
          // console.log(regionChild, regionChildren)
          // let bLastParaInRegion = false;
          // if (i === regionChildren.length - 1) {
          //   bLastParaInRegion = true;
          // }

          if (bNewVersion) {
            ParagraphReader.tTraverseParagraphForSec(regionChild, document, ContentControlDescObj,
              uniqueImagelist, bInsertFile, paras, container, null, modeFonts, bAddRegionToParas);
          } else {
            ParagraphReader.tTraverseParagraph2(regionChild, document, ContentControlDescObj,
              uniqueImagelist, bInsertFile, paras, container, null, modeFonts,
              sectionEnds, bAddRegionToParas, insertRegionNames);
          }

        } else if (regionChild.tagName === 'rg') {
          // recursive
          depth++;
          let container = region;
          // if (ignoreRegion === true && depth >= 2) {
          if (depth > 2) {
            forceIgnoreRegion = true;
          }
          if (ignoreRegion === true) {
            container = parent;
          }
          this.tTraverseRegion2(regionChild, document, ContentControlDescObj,
            uniqueImagelist, bInsertFile, paras, container, depth, forceIgnoreRegion,
            modeFonts, sectionEnds, bInsertFromRegion, insertRegionNames);
          depth--;
        } else if (regionChild.tagName === 'w:tbl') {
          // table in region
          TableReader.tTraverseTable2(regionChild, document, ContentControlDescObj, uniqueImagelist,
            bInsertFile, paras, null, region, modeFonts, sectionEnds);
        } else {
          // none
        }
      }

    }

    // check if modeFonts has regiontitletextprop
    if (modeFonts != null) {
      const {defaultFont, regionTitleFont} = modeFonts;
      if (regionTitleFont != null) {
        region.setTitlePortionTextProperty(regionTitleFont);
      }
      // else if (defaultFont != null) {
      //   region.setTitlePortionTextProperty(defaultFont);
      // }
    }

    if (ignoreRegion === true) {
      return;
    }
    // console.log(depth, region)
    if (depth === 1 && options?.bLoadCache === true) {
      region.setLoadCache(options.bLoadCache);
    }
    // max 2 potential recursive
    if (depth === 2 && parent != null) {
      // just need to come in once: the inner recursive one
      // parent may have already been added by other inner recursive region
      const parentName = parent.getName();
      if (regionManager.getRegionByName(parentName) == null) {
        if (!bInsertFile) { // open file
          document.addToContent(document.content.length, parent, true);
          regionManager.addRegion(parent, regionManager.getRegionNames().length);

        } else {
            let regionAddToParas = true;
            for (const para of paras) {
              if (para instanceof Region) {
                if (para.getName() === parent.getName()) {
                  regionAddToParas = false;
                  break;
                }
              }
            }
            if (regionAddToParas === true) {
              // paras.push(container);
              this.addRegionToParas(paras, parent, document);
            }

            this.checkDuplicateRegionName(region, document, insertRegionNames);
            regionManager.addPasteRegion(region);
            // paste paras to content if THIS REGION is the last node
            // this.pasteParasToContentIfNeeded(index, nodes, document, bNoEndPara, paras);

            if (bInsertFromRegion === true) {
              return;
            }
            // TODO: cleanup bInsertFile case regionManager
            // if (bInsertFile === true) {
            //   return ;
            // }
        }
        // regionManager.addRegion(parent, undefined);
      } else {
        // parentName already exists

        if (bInsertFile) {
          // a doc with region inserted to a doc's region where its depth is 1
          let regionAddToParas = true;
          for (const para of paras) {
            if (para instanceof Region) {
              if (para.getName() === region.getName()) {
                regionAddToParas = false;
                break;
              }
            }
          }
          if (regionAddToParas === true) {
            this.addRegionToParas(paras, region, document);
          }
          // if (regionManager && regionManager.getRegionByName(region.getName()) == null) {
          //   this.addRegionToParas(paras, region, document); // this also include duplicate name handler
          // }
          // console.log(region.getName()) // name is changed if duplicate
          // regionManager.addRegion(region, undefined); // would be added in addLeaf

          this.checkDuplicateRegionName(region, document, insertRegionNames);
          regionManager.addPasteRegion(region, parent);
          // // paste paras to content if THIS REGION is the last node
          // this.pasteParasToContentIfNeeded(index, nodes, document, bNoEndPara, paras);

          if (bInsertFromRegion === true) {
            // insert shouldn't go this route
            // regionManager.addLeaf(parentName, region);

            return;
          }
          // TODO: cleanup bInsertFile case regionManager
          // if (bInsertFile === true) {
          //   return ;
          // }
        }
      }
      parent.addToContent(parent.getContent().length, region);
      regionManager.addLeaf(parentName, region);

    } else if (depth === 1 && 0 === regionManager.getChilds(region).length) {
      // only one case to add: one layer region, no parent
      // if depth 1 which has child region (thus already added), ignore it
      if (regionManager.getRegionByName(region.getName()) == null) {
        if (!bInsertFile) { // open file

          document.addToContent(document.content.length, region, true);
          regionManager.addRegion(region, regionManager.getRegionNames().length);
        } else {
          // document.addPasteContent(paras, false);

          // already taken care of when adding para in region
          // this.addRegionToParas(paras, region, document);

          // paste paras to content if THIS REGION is the last node
          // this.pasteParasToContentIfNeeded(index, nodes, document, bNoEndPara, paras);

          this.checkDuplicateRegionName(region, document, insertRegionNames);
          regionManager.addPasteRegion(region);
          paras.push(region);
        }
        // regionManager.addRegion(region, undefined);

      } else {
        // insert file, dup name, but ignore 'already added'
        if (bInsertFile) {
          // dup name shouldn't exist in paras(okay if whole document has dup with any item in paras)
          const name = region.getName();
          let addFlag = true;
          for (const para of paras) {
            if (para instanceof Region) {
              if (name === para.getName()) {
                addFlag = false;
                break;
              }
            }
          }
          if (addFlag === true) {
            this.addRegionToParas(paras, region, document);
            if ((node as IParseXmlNode).type === undefined) {
              // regionManager.addRegion(region, undefined);
              this.checkDuplicateRegionName(region, document, insertRegionNames);
              regionManager.addPasteRegion(region);
            }
          }
          // paste paras to content if THIS REGION is the last node
          // this.pasteParasToContentIfNeeded(index, nodes, document, bNoEndPara, paras);
        } else {
          this.checkDuplicateRegionName(region, document, insertRegionNames);
          document.addToContent(document.content.length, region, true);
          regionManager.addRegion(region, regionManager.getRegionNames().length);
        }
      }
    }

  } // traverseRegion

  public static traverseRegionForInsertFile3(
    node: rtNode, inserFileContent: IInsertFileContent, ContentControlDescObj: IContentControlDescObj,
    uniqueImagelist: Map<string, IUniqueImageProps>,
    parent: Region = null, depth: number,
    modeFonts: IModeFonts = null, sectionEnds: any[] = [],
    bInsertFromRegion: boolean = false, insertRegionNames: string[] = []
  ): void {
    // let ignoreRegion = false;
    // when insert a file containing region(s) into a region, ignore the region(s)
    // if (bInsertFile === true && forceIgnoreRegion === true) {
    //   ignoreRegion = true;
    // }

    const regionProps: INewControlProperty = {
      showTitle: false,
    } as any;
    const region = new Region(null, inserFileContent.logicDocument, regionProps);

    // rg attrs(ignore type, mode for now)
    const nameAttr = node.attributes['name'];
    if (nameAttr) {
      regionProps.newControlName = nameAttr;
    }

    const regionChildren = node.children;
    const bNewVersion = FILE_HEADER_VERSION2 === inserFileContent.documentVersion;
    // for (let i = 0; i < regionChildren.length; i++) {
    for (const regionChild of regionChildren) {

      // const regionChild = regionChildren[i];
      if (typeof regionChild === 'object') {
        if (regionChild.tagName === 'rgPr') {

          const rgPropChildren = regionChild.children;

          for (const rgPropChild of rgPropChildren) {
            if (typeof rgPropChild === 'object') {
              const textNode = rgPropChild.children[0]; // usually just text node, but may exist childnodes
              const res = readerRegionProps(regionProps, rgPropChild, textNode, inserFileContent.documentVersion,
                {bRemoveHiddenRegion: inserFileContent.bRemoveHiddenRegion});
              if (res === false) {
                return;
              }
              // switch (rgPropChild.tagName) {
              //   case 'deleteProtect': {
              //     if (textNode) {
              //       regionProps.isNewControlCanntDelete = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'editProtect': {
              //     if (textNode) {
              //       regionProps.isNewControlCanntEdit = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'hidden': {
              //     if (textNode) {
              //       regionProps.isNewControlHidden = textNode === '1' ? true : false;
              //       if ( inserFileContent && regionProps.isNewControlHidden && inserFileContent.bRemoveHiddenRegion ) {
              //         return ;
              //       }
              //     }
              //     break;
              //   }

              //   case 'editReverse': {
              //     if (textNode) {
              //       regionProps.isNewControlReverseEdit = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'title': {
              //     if (textNode != null && typeof textNode === 'string') {
              //       regionProps.newControlTitle = safeDecodeURIComponent(textNode, inserFileContent.documentVersion);
              //     }
              //     break;
              //   }
              //   case 'tip': {
              //     if (textNode != null && typeof textNode === 'string') {
              //       regionProps.newControlInfo = safeDecodeURIComponent(textNode, inserFileContent.documentVersion);
              //     }
              //     break;
              //   }

              //   case 'showTitle': {
              //     if (textNode != null) {
              //       regionProps.showTitle = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'beginPos': {
              //     //
              //     break;
              //   }

              //   case 'endPos': {
              //     //
              //     break;
              //   }

              //   case 'serialNumber': {
              //     if (textNode != null && typeof textNode === 'string') {
              //       regionProps.newControlSerialNumber = textNode;
              //     }
              //     break;
              //   }

              //   case 'maxLength': {
              //     if (textNode != null && typeof textNode === 'string') {
              //       regionProps.newControlMaxLength = parseInt(textNode);
              //     }
              //     break;
              //   }

              //   case 'customProperty': {
              //     const customProperties = rgPropChild.children;
              //     if (customProperties.length > 0) { // usually if in this block, length must be > 0

              //       const customPropertyArr = tReadCustomProperties(customProperties, inserFileContent.documentVersion);
              //       // console.log(customPropertyArr)
              //       regionProps.customProperty = customPropertyArr;
              //     }
              //     break;
              //   }

              //   default: {
              //     break;
              //   }
              // }
            }
          }
          // console.log(regionProps)
          region.setProperty(regionProps, true);
          this.checkDuplicateRegionNameForInsertFile(region, insertRegionNames, inserFileContent);

        } else if (regionChild.tagName === 'w:p') {
          let container = region;
          // if (ignoreRegion === true) {
          //   container = parent;
          // }
          let bAddRegionToParas = true;
          if (bInsertFromRegion === false && depth === 2) {
            // bAddRegionToParas: if not inserted from region, and the para is in depth 2 region -> false
            bAddRegionToParas = false;
          }
          // console.log(regionChild, regionChildren)
          // let bLastParaInRegion = false;
          // if (i === regionChildren.length - 1) {
          //   bLastParaInRegion = true;
          // }

          if (bNewVersion) {
            ParagraphReader.traverseParagraphForInsertFileSec(regionChild, inserFileContent, ContentControlDescObj,
              uniqueImagelist, container);
          } else {
            ParagraphReader.traverseParagraphForInsertFile3(regionChild, inserFileContent, ContentControlDescObj,
              uniqueImagelist, container, null, sectionEnds);
          }

        } else if (regionChild.tagName === 'rg') {
          // recursive
          depth++;
          let container = region;
          // if (ignoreRegion === true && depth >= 2) {
          // if (depth > 2) {
          //   forceIgnoreRegion = true;
          // }
          // if (ignoreRegion === true) {
          //   container = parent;
          // }
          this.traverseRegionForInsertFile3(regionChild, inserFileContent, ContentControlDescObj,
            uniqueImagelist, container, depth,
            modeFonts, sectionEnds, bInsertFromRegion, insertRegionNames);
          depth--;
        } else if (regionChild.tagName === 'w:tbl') {
          // table in region
          TableReader.traverseTableForInsertFile3(regionChild, inserFileContent, ContentControlDescObj, uniqueImagelist,
              null, region, sectionEnds);
        } else {
          // none
        }
      }

    }

    const regionManager = inserFileContent.regionManager;
    // max 2 potential recursive
    if (depth === 2 && parent != null) {
      // just need to come in once: the inner recursive one
      // parent may have already been added by other inner recursive region
      // const parentName = parent.getName();
      // inserFileContent.content.push(parent);

      // this.checkDuplicateRegionName(region, null, insertRegionNames, inserFileContent);
      regionManager.addInsertRegion(region, parent);

      parent.addToContent(parent.getContent().length, region);
      // regionManager.addLeaf(parentName, region);

    } else if (depth === 1) {
      // only one case to add: one layer region, no parent
      // if depth 1 which has child region (thus already added), ignore it
      // if (regionManager.getRegionByName(region.getName()) == null) {
        // if (!bInsertFile) { // open file

        inserFileContent.content.push(region);
        // this.checkDuplicateRegionName(region, null, insertRegionNames, inserFileContent);
        regionManager.addInsertRegion(region);
    }

  } // traverseRegion

  public static traverseRegionForInsertFile2(
                  node: rtNode, inserFileContent: IInsertFileContent, ContentControlDescObj: IContentControlDescObj,
                  uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                  parent: Region = null, depth: number, insertRegionNames: string[] = []
  ): void {

    const regionProps: INewControlProperty = {
      showTitle: false,
    } as any;
    const region = new Region(null, inserFileContent.logicDocument, regionProps);

    // rg attrs(ignore type, mode for now)
    const nameAttr = node.attributes['name'];
    if (nameAttr) {
      regionProps.newControlName = nameAttr;
    }

    const regionChildren = node.children;
    const bNewVersion = FILE_HEADER_VERSION2 === inserFileContent.documentVersion;
    // for (let i = 0; i < regionChildren.length; i++) {
    for (const regionChild of regionChildren) {

      // const regionChild = regionChildren[i];
      if (typeof regionChild === 'object') {
        if (regionChild.tagName === 'rgPr') {

          const rgPropChildren = regionChild.children;

          for (const rgPropChild of rgPropChildren) {
            if (typeof rgPropChild === 'object') {
              const textNode = rgPropChild.children[0]; // usually just text node, but may exist childnodes
              readerRegionProps(regionProps, rgPropChild, textNode, inserFileContent.documentVersion);
              // switch (rgPropChild.tagName) {
              //   case 'deleteProtect': {
              //     if (textNode) {
              //       regionProps.isNewControlCanntDelete = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'editProtect': {
              //     if (textNode) {
              //       regionProps.isNewControlCanntEdit = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'hidden': {
              //     if (textNode) {
              //       regionProps.isNewControlHidden = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'editReverse': {
              //     if (textNode) {
              //       regionProps.isNewControlReverseEdit = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'title': {
              //     if (textNode != null && typeof textNode === 'string') {
              //       regionProps.newControlTitle = safeDecodeURIComponent(textNode, inserFileContent.documentVersion);
              //     }
              //     break;
              //   }
              //   case 'tip': {
              //     if (textNode != null && typeof textNode === 'string') {
              //       regionProps.newControlInfo = safeDecodeURIComponent(textNode, inserFileContent.documentVersion);
              //     }
              //     break;
              //   }

              //   case 'showTitle': {
              //     if (textNode != null) {
              //       regionProps.showTitle = textNode === '1' ? true : false;
              //     }
              //     break;
              //   }

              //   case 'beginPos': {
              //     //
              //     break;
              //   }

              //   case 'endPos': {
              //     //
              //     break;
              //   }

              //   case 'serialNumber': {
              //     if (textNode != null && typeof textNode === 'string') {
              //       regionProps.newControlSerialNumber = textNode;
              //     }
              //     break;
              //   }

              //   case 'maxLength': {
              //     if (textNode != null && typeof textNode === 'string') {
              //       regionProps.newControlMaxLength = parseInt(textNode);
              //     }
              //     break;
              //   }

              //   case 'customProperty': {
              //     const customProperties = rgPropChild.children;
              //     if (customProperties.length > 0) { // usually if in this block, length must be > 0

              //       const customPropertyArr = tReadCustomProperties(customProperties, inserFileContent.documentVersion);
              //       // console.log(customPropertyArr)
              //       regionProps.customProperty = customPropertyArr;
              //     }
              //     break;
              //   }

              //   default: {
              //     break;
              //   }
              // }

            }

          }
          // console.log(regionProps)
          region.setProperty(regionProps, true);
          this.checkDuplicateRegionNameForInsertFile(region, insertRegionNames, inserFileContent);

        } else if (regionChild.tagName === 'w:p') {
          // let container = region;

          if (bNewVersion) {
            ParagraphReader.traverseParagraphForInsertFileSec(regionChild, inserFileContent, ContentControlDescObj,
              uniqueImagelist, region);
          } else {
            ParagraphReader.traverseParagraphForInsertFile2(regionChild, inserFileContent, ContentControlDescObj,
              uniqueImagelist, bInsertFile, region);
          }

        } else if (regionChild.tagName === 'rg') {
          // recursive
          depth++;
          let container = region;
          // if (ignoreRegion === true && depth >= 2) {
          //   container = parent;
          // }
          this.traverseRegionForInsertFile2(regionChild, inserFileContent, ContentControlDescObj,
            uniqueImagelist, bInsertFile, container, depth, insertRegionNames);
          depth--;
        } else if (regionChild.tagName === 'w:tbl') {
          // table in region
          TableReader.traverseTableForInsertFile2(regionChild, inserFileContent, ContentControlDescObj,
            uniqueImagelist, bInsertFile, region);
        } else {
          // none
        }
      }

    }

    // console.log(depth, region)
    const regionManager = inserFileContent.regionManager;
    // max 2 potential recursive
    if (depth === 2 && parent != null) {
      // just need to come in once: the inner recursive one
      // parent may have already been added by other inner recursive region
      // const parentName = parent.getName();
      // inserFileContent.content.push(parent);

      // this.checkDuplicateRegionName(region, null, insertRegionNames, inserFileContent);
      regionManager.addInsertRegion(region, parent);

      parent.addToContent(parent.getContent().length, region);
      // regionManager.addLeaf(parentName, region);

    } else if (depth === 1) {
      // only one case to add: one layer region, no parent
      // if depth 1 which has child region (thus already added), ignore it
      // if (regionManager.getRegionByName(region.getName()) == null) {
        // if (!bInsertFile) { // open file

        inserFileContent.content.push(region);
        // this.checkDuplicateRegionName(region, null, insertRegionNames, inserFileContent);
        regionManager.addInsertRegion(region);
    }

  } // traverseRegion

  private static addRegionToParas(paras: DocumentContentElementBase[], region: Region, document: Document): void {
    // check duplicate name
    const regionManager = document.getRegionManager();
    if (!regionManager.checkRegionName(region.getName())) {
      region.setNewControlName(regionManager.makeUniqueName());
    }

    paras.push(region);
    // console.log(paras)
    // regionManager.addRegion(region, undefined);
  }

  // /**
  //  * when inserting file, if the last node before w:sectPr is region, paste paras to document
  //  * @param root node index (aka current region node index in root-lvl nodes{region must be root-lvl node, along with para, table})
  //  * @param nodes root nodes
  //  * @param document /
  //  * @param bNoEndPara /
  //  * @param paras /
  //  */
  // private static pasteParasToContentIfNeeded(index: number, nodes: (string | number | rtNode)[], document: Document,
  //                                            bNoEndPara: boolean = false, paras: DocumentContentElementBase[]): void {
  //   // TODO: the better solution should be modify getSelectedContentToSave()!
  //   const nodesLen = nodes.length;
  //   // console.log(index, nodesLen)
  //   if (nodesLen > 0) {
  //     const lastRootNode = nodes[nodesLen - 1];
  //     if (typeof lastRootNode === 'object' && lastRootNode.tagName === 'w:sectPr' &&
  //       index === nodesLen - 2) {
  //       // TODO: always same lvl as sectpr? no! headerfooter insert file with region
  //       // add empty paras when required
  //       if (!bNoEndPara) {
  //         for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA + 1; i++) {
  //           paras.push(new Paragraph(document, document));
  //         }
  //       }

  //       // console.log(paras)
  //       // tslint:disable-next-line: no-console
  //       console.log('content pasted in region')
  //       console.log(paras)
  //       document.addPasteContent(paras, false);
  //     }
  //   }
  // }

  /**
   * only serves to check duplicate names during insertion
   * @param region /
   * @param document /
   * @param insertRegionNames /
   */
  private static checkDuplicateRegionName(region: Region, document: Document, insertRegionNames: any[] = [],
                                          inserFileContent?: IInsertFileContent): void {
    const regionManager = (document ? document.getRegionManager() :
                                      (inserFileContent ? inserFileContent.regionManager : null) );
    const regionName = region.getName();
    // console.log(insertRegionNames);
    if (regionManager && !regionManager.checkRegionName(regionName, insertRegionNames)) {
      let uniqueName = regionManager.makeUniqueName();
      let safeCount = 0;
      while (insertRegionNames.includes(uniqueName)) {
        uniqueName += '1';
        safeCount++;
        if (safeCount > 1000) {
          // tslint:disable-next-line: no-console
          console.warn('cannot set unique region name in insertion');
          break;
        }
      }
      region.setNewControlName(uniqueName);
      insertRegionNames.push(uniqueName);
    }
  }

  /**
   * only serves to check duplicate names during insertion
   * @param region /
   * @param document /
   * @param insertRegionNames /
   */
  private static checkDuplicateRegionNameForInsertFile(
                    region: Region, insertRegionNames: any[] = [], inserFileContent?: IInsertFileContent): void {
    const regionManager = (inserFileContent ? inserFileContent.regionManager : null);
    const regionName = region.getName();

    if (regionManager && !regionManager.checkRegionName(regionName, insertRegionNames)) {
      let uniqueName = regionManager.makeUniqueName();
      let safeCount = 0;

      while (!regionManager.checkRegionName(uniqueName, insertRegionNames)) {
        uniqueName += '1';
        safeCount++;
        if (safeCount > 10000) {
          // tslint:disable-next-line: no-console
          console.warn('region name in insertion');
          // break;
        }
      }
      region.setNewControlName(uniqueName);
      insertRegionNames.push(uniqueName);
    }
  }

}
