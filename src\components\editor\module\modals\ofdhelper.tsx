export default class OfdFileHandler {
    private html2pdfUrl: string;
    private pdf2ofdUrl: string;
    private ofd2htmlUrl: string;
  
    // Set PDF URL
    public setPdfUrl(url: string): void {
      if (!url.endsWith('/')) {
        url += '/';
      }
      this.html2pdfUrl = url + 'editorSvr/v1/printToPdf';
    }
  
    // Set OFD URLs
    public setOfdUrl(url: string): void {
      if (!url.endsWith('/')) {
        url += '/';
      }
      this.pdf2ofdUrl = url + 'editorSvr/v1/pdf2ofd';
      this.ofd2htmlUrl = url + 'editorSvr/v1/odf2html';
    }
  
    // HTML to OFD conversion
    public async html2ofd(html: Blob) {
      try {
        // Construct FormData with HTML data
        const formData = new FormData();
        formData.append('file', html);
  
        // Send POST request to convert HTML to PDF
        const pdfResponse = await fetch(this.html2pdfUrl, {
          method: 'POST',
          body: formData
        });
  
        const pdfBlob = await pdfResponse.blob();
  
        // Send POST request to convert PDF to OFD
        const pdfFormData = new FormData();
        pdfFormData.append('file', pdfBlob);
        const ofdResponse = await fetch(this.pdf2ofdUrl, {
          method: 'POST',
          body: pdfFormData
        });
  
        const ofdBlob = await ofdResponse.blob();
  
        // Create a Blob URL for the OFD file
        const blobUrl = URL.createObjectURL(ofdBlob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = 'converted_html.ofd';
        link.click();
        URL.revokeObjectURL(blobUrl);
      } catch (error) {
        console.error('Error in HTML to OFD conversion:', error);
      }
    }
  
    // HTML to PDF conversion
    public async html2Pdf(htmlBlob: Blob) {
      try {
        // Construct FormData with HTML data
        const formData = new FormData();
        formData.append('file', htmlBlob);
  
        // Send POST request to convert HTML to PDF
        const response = await fetch(this.html2pdfUrl, {
          method: 'POST',
          body: formData
        });
  
        const blobData = await response.blob();
  
        // Create a Blob URL for the PDF file
        const blobUrl = URL.createObjectURL(blobData);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = 'converted_pdf.pdf';
        link.click();
        URL.revokeObjectURL(blobUrl);
      } catch (error) {
        console.error('Error in HTML to PDF conversion:', error);
      }
    }
  
    // Read OFD file
    public readOfdFile(file: File): Promise<ArrayBuffer> {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target) {
            const contents = event.target.result as ArrayBuffer;
            resolve(contents);
          } else {
            reject(new Error('Failed to read the file.'));
          }
        };
        reader.readAsArrayBuffer(file);
      });
    }
  
    // Open file selector and convert OFD to HTML
    public ofd2htmlWithOpenFileSelector(): void {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.ofd'; // Only accept OFD files
  
      input.addEventListener('change', async (event) => {
        const target = event.target as HTMLInputElement;
        if (target.files && target.files.length > 0) {
          const file = target.files[0];
          try {
            const content = await this.readOfdFile(file);
            const blob = new Blob([content], { type: 'application/octet-stream' });
            const formData = new FormData();
            formData.append('file', blob);
  
            const response = await fetch(this.ofd2htmlUrl, {
              method: 'POST',
              body: formData
            });
  
            const blobData = await response.blob();
            const blobUrl = URL.createObjectURL(blobData);
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = 'converted_html.html';
            link.click();
            URL.revokeObjectURL(blobUrl);
          } catch (error) {
            console.error('Error reading file:', error);
          }
        }
      });
  
      // Trigger file selection dialog
      input.click();
    }
  }