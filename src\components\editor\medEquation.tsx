import * as React from 'react';

interface IMedEquationProps {
  refs?: any;
}

export default class MedEquation extends React.Component<IMedEquationProps, {}> {

  private ordEquationRef;
  private fractionEquationRef;
  private menEquationRef;

  constructor(props) {
    super(props);
    this.ordEquationRef = React.createRef();
    this.fractionEquationRef = React.createRef();
    this.menEquationRef = React.createRef();
  }


  public render() {
    const {refs} = this.props;
    return (
      <React.Fragment>
        <svg 
          className="medical-equation-wrapper" 
          ref={refs.ordEquationRef} 
          width="0" 
          height="0" 
          viewBox="0 0 300 300" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect width="100%" height="100%" fill="white"/>
          <g className="medical-equation">
            <line x1="0" y1="200" x2="300" y2="200" className="liner" style={{stroke: "black", strokeWidth: 5}}/>
            <line x1="150" y1="90" x2="150" y2="300" className="liner" style={{stroke: "black", strokeWidth: 5}}/>
            <text x="75" y="160" textAnchor="middle" style={{fontSize: "80px"}}>
              <tspan className="equation-text">1</tspan>
            </text>
            <text x="225" y="160" textAnchor="middle" style={{fontSize: "80px"}}>
              <tspan className="equation-text">1</tspan>
            </text>
            <text x="75" y="290" textAnchor="middle" style={{fontSize: "80px"}}>
              <tspan className="equation-text">1</tspan>
            </text>
            <text x="225" y="290" textAnchor="middle" style={{fontSize: "80px"}}>
              <tspan className="equation-text">1</tspan>
            </text>
          </g>
        </svg>

        <svg 
          className="medical-equation-wrapper" 
          ref={refs.fractionEquationRef} 
          width="0" 
          height="0" 
          viewBox="0 0 300 300" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect width="100%" height="100%" fill="white"/>
          <g className="medical-equation">
            <line x1="0" y1="200" x2="300" y2="200" className="liner" style={{stroke: "black", strokeWidth: 5}}/>
            <text x="150" y="160" textAnchor="middle" style={{fontSize: "80px"}}>
              <tspan className="equation-text">1</tspan>
            </text>
            <text x="150" y="290" textAnchor="middle" style={{fontSize: "80px"}}>
              <tspan className="equation-text">1</tspan>
            </text>
          </g>
        </svg>

        <svg 
          className="medical-equation-wrapper" 
          ref={refs.menEquationRef} 
          // width="106" 
          // height="50" 
          width="0" 
          height="0" 
          viewBox="0 0 636 300" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect width="100%" height="100%" fill="white"/>
          <g className="medical-equation">
            <g className="hidden-values unitEnabled" />
            {/* left part */}
            <text x="192" y="220" textAnchor="end" style={{fontSize: "80px"}}>
              <tspan className="equation-text">15岁</tspan>
            </text>

            {/* fraction */}
            <line x1="212" y1="200" x2="408" y2="200" className="liner" style={{stroke: "black", strokeWidth: 5}}/>
            <text x="310" y="160" textAnchor="middle" style={{fontSize: "80px"}}>
              <tspan className="equation-text">3天</tspan>
            </text>
            <text x="310" y="290" textAnchor="middle" style={{fontSize: "80px"}}>
              <tspan className="equation-text">30天</tspan>
            </text>

            {/* right part */}
            <text x="428" y="220" textAnchor="start" style={{fontSize: "80px"}}>
              <tspan className="equation-text">50岁</tspan>
            </text>
          </g>
        </svg>
      </React.Fragment>
    );
  }
}

// export default React.forwardRef((props, ref) => {
//   const {fractionEquationRef, ordEquationRef, menEquationRef} = ref;

//   return (<MedEquation ref={fractionEquationRef} />)
// });

