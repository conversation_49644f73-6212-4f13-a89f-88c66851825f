import TextProperty, { FontStyleType, FontWeightType, TextDecorationLineType } from '../TextProperty';
import { ParaElementBase } from './ParaElementBase';
import { ParaElementType } from './ParagraphContent';
import { ChangeParaTextPropertyFont } from './ParaTextPropertyChange';
import { ResultType } from '../../../common/commonDefines';
import History from '../History';

/**
 * 段落中的文本属性类：用于行尾的文本属性（可用于下一段落的文本属性继承）
 */
export class ParaTextProperty extends ParaElementBase {
    // pos: number[]; // 记录改变光标处文本属性时，光标所在portion，portionContent的索引
    public textProperty: TextProperty; // 记录文本属性
    public wavyUnderlineId?: string; // 波浪线ID
    // parent: any;
    // history: History;
    public type: ParaElementType;

    constructor( textProperty?: TextProperty ) {
        super();
        // this.pos = [-1 , -1];

        // this.parent = parent;
        // gHistory = history;
        this.textProperty = new TextProperty();
        this.type = ParaElementType.ParaTextPr;

        if ( textProperty ) {
            this.textProperty.setProperty(textProperty);
        }
    }

    public measure(textPr: TextProperty): number {
        return 0;
    }

    public copy(): ParaTextProperty {
        const news = new ParaTextProperty();
        return news;
    }

    public applyTextPr( textPr: TextProperty, history: History ): number {
        let flag = false;
        if ( undefined !== textPr.fontFamily ) {
            flag = this.setTextFontFamily(textPr.fontFamily, history) || flag;
        }

        if ( undefined !== textPr.fontStyle ) {
            flag = this.setTextFontStyle(textPr.fontStyle, history) || flag;
        }

        if ( undefined !== textPr.fontSize ) {
            flag = this.setTextFontSize(textPr.fontSize, history) || flag;
        }

        if ( undefined !== textPr.fontWeight ) {
            flag = this.setTextFontWeight(textPr.fontWeight, history) || flag;
        }

        if ( undefined !== textPr.backgroundColor ) {
            flag = this.setTextFontBackgroundColor(textPr.backgroundColor, history) || flag;
        }

        if ( undefined !== textPr.color ) {
            flag = this.setTextFontColor(textPr.color, history) || flag;
        }

        if ( undefined !== textPr.textDecorationLine ) {
            flag = this.setTextDecorationLine(textPr.textDecorationLine, history) || flag;
        }

        // if ( undefined !== textPr.superscript )
        // 	this.setTextDecorationLine(textPr.superscript);

        // if ( undefined !== textPr.superscript )
        // 	this.setTextDecorationLine(textPr.superscript);
        if (flag !== true) {
            return ResultType.UnEdited;
        }

        return ResultType.Success;
    }

    /**
     * 设置文本属性: 字体
     * @param prop
     */
    public setTextFontFamily( prop: string, history: History ): boolean {
        if ( prop !== this.textProperty.fontFamily ) {
            const old = this.textProperty.fontFamily;
            this.textProperty.fontFamily = prop;

            if ( history) {
                history.addChange(new ChangeParaTextPropertyFont(this, old, prop, null));
            }

            // this.recalcInfo.bMeasure = true;
            // this.recalcInfo.bRecalc = true;
            return true;
        }
    }

    public setTextFontStyle( prop: FontStyleType, history: History ): boolean {
        if ( prop !== this.textProperty.fontStyle ) {
            const old = this.textProperty.fontStyle;
            this.textProperty.fontStyle = prop;

            if ( history) {
                history.addChange(new ChangeParaTextPropertyFont(this, old, prop, null));
            }

            // this.recalcInfo.bMeasure = true;
            // this.recalcInfo.bRecalc = true;
            return true;
        }
    }

    public setTextFontSize( prop: number, history: History ): boolean {
        if ( prop !== this.textProperty.fontSize ) {
            const old = this.textProperty.fontSize;
            this.textProperty.fontSize = prop;

            if ( history) {
                history.addChange(new ChangeParaTextPropertyFont(this, old, prop, null));
            }

            // this.recalcInfo.bMeasure = true;
            // this.recalcInfo.bRecalc = true;
            return true;
        }
    }

    public setTextFontWeight( prop: FontWeightType, history: History ): boolean {
        if ( prop !== this.textProperty.fontWeight ) {
            const old = this.textProperty.fontWeight;
            this.textProperty.fontWeight = prop;

            if ( history) {
                history.addChange(new ChangeParaTextPropertyFont(this, old, prop, null));
            }

            // this.recalcInfo.bMeasure = true;
            // this.recalcInfo.bRecalc = true;
            return true;
        }
    }

    public setTextFontBackgroundColor( prop: string, history: History ): boolean {
        if ( prop !== this.textProperty.backgroundColor ) {
            const old = this.textProperty.backgroundColor;
            this.textProperty.backgroundColor = prop;

            if ( history) {
                history.addChange(new ChangeParaTextPropertyFont(this, old, prop, null));
            }

            // this.recalcInfo.bMeasure = true;
            // this.recalcInfo.bRecalc = true;
            return true;
        }
    }

    public setTextFontColor( prop: string, history: History ): boolean {
        if ( prop !== this.textProperty.color ) {
            const old = this.textProperty.color;
            this.textProperty.color = prop;

            if ( history) {
                history.addChange(new ChangeParaTextPropertyFont(this, old, prop, null));
            }

            // this.recalcInfo.bMeasure = true;
            // this.recalcInfo.bRecalc = true;
            return true;
        }
    }

    public setTextDecorationLine( prop: TextDecorationLineType, history: History ): boolean {
        if ( prop !== this.textProperty.textDecorationLine ) {
            const old = this.textProperty.textDecorationLine;
            this.textProperty.textDecorationLine = prop;

            if ( history) {
                history.addChange(new ChangeParaTextPropertyFont(this, old, prop, null));
            }

            // this.recalcInfo.bMeasure = true;
            // this.recalcInfo.bRecalc = true;
            return true;
        }
    }
}
