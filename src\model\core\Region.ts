import DocumentContentElementBase from './DocumentContentElementBase';
import { DocumentContent } from './DocumentContent';
import DocumentContentBase from './DocumentContentBase';
import Document, { RecalcResultType } from './Document';
import { INewControlProperty, DataType, ICustomProps, ResultType,
    REGION_MAX_DEPTH, EquationType, NewControlPropName, NewControlType, parseBoolean,
    IRevisionChange,
    RevisionChangeType,
    RegionBorderViewType,
    MessageType, CascadeActionType,
    CleanModeType,
    ICascade,
    ImageMediaType,
    ErrorTextColor} from '../../common/commonDefines';
import { ICursorProperty } from '../CursorProperty';
import { ICurrentCursorPos } from './Paragraph/ParagraphSearchPosXY';
import MouseEventHandler, { IMouseEvent } from '../../common/MouseEventHandler';
import { DocumentContentType } from './Style';
import { IParaProperty } from '../ParaProperty';
import TextProperty, { ITextProperty, FontWeightType } from './TextProperty';
import { ParagraphContentPos, ParaTextName } from './Paragraph/ParagraphContent';
import { IDrawSelectionBounds, IDrawNewControlBounds, IDrawTableNewBorder } from '../DocumentCore';
import { NewControl } from './NewControl/NewControl';
import DocumentFrameBounds from './FrameBounds';
import HeaderFooter from './HeaderFooter';
import { TableCell } from './Table/TableCell';
import ParaPortion from './Paragraph/ParaPortion';
import Paragraph from './Paragraph';
import ParaTextExtend from './Paragraph/ParaTextExtend';
import { DocumentElementState } from './HistoryState';
import { ChangeRegionName, ChangeRegionTitle, ChangeRegionTitleVisible, ChangeRegionHidden,
    ChangeRegionReverseEdit, ChangeRegionEditProtect, ChangeRegionDeleteProtect,
    ChangeRegionSerialNumber,
    ChangeRegionManagerRemoveRegions,
    ChangeRegionManagerRemoveRegionIndexs,
    ChangeRegionManagerAddRegions,
    ChangeRegionManagerAddRegionIndexs,
    ChangeRegionShowBackgroundColor} from './RegionChange';
import { HistoryDescriptionType, HistroyItemType } from './HistoryDescription';
import { ParaElementBase } from './Paragraph/ParaElementBase';
import { GlobalEvent as gEvent , GlobalEventName as gEventName } from '../../common/GlobalEvent';
import History, { IOperateResult } from './History';
import { Comment } from './Comment/Comment';

export class Region extends DocumentContentElementBase {
    public bBreakStartElement: boolean;
    public bResetStartElement: boolean;
    private content: DocumentContent;
    // private logicDocument: Document;
    private oldTitlePortion: ParaPortion;
    private titlePortion: ParaPortion;

    private bShowBackgroundColor: boolean;
    private name: string;
    private title: string;
    private serialNumber: string;
    private bShowTitle: boolean;
    private bReverseEdit: boolean;
    private bCanntEdit: boolean;
    private bCanntDelete: boolean;
    private bHidden: boolean;
    private bDirty: boolean;    // 文档脏标记
    private customProperty: Map<string, ICustomProps>;    // 用户自定义属性
    private bRefresh: boolean;
    private maxLength: number;
    private tipsContent: string;
    private identifier: string;
    private bAlwaysShow: boolean;
    private bLoadCache: boolean; // 缓存加载标志
    private placeholder: string; // 缓存加载显示占位符
    private relevanceId: number; // 缓存加载关联id
    private regExp: string;
    private bMoreThenMaxLength: boolean;
    private enableAISupport: boolean; // 是否启用AI支持

    // private reviewType: ReviewType;
    // private reviewInfo: ReviewInfo;

    constructor(parent: DocumentContentBase, logicDocument: Document, props: INewControlProperty = {} as any) {
        super(parent);
        this.content = new DocumentContent(this, logicDocument, 0, 0, 0, 0, false);
        this.content.content = [];
        this.logicDocument = logicDocument;
        this.customProperty = new Map<string, ICustomProps>();
        this.title = props.newControlTitle || '';
        this.serialNumber = props.newControlSerialNumber || '';
        this.bShowTitle = props.showTitle === undefined ? false : props.showTitle;
        this.name = props.newControlName;
        this.bReverseEdit = props.isNewControlReverseEdit === undefined ? false : props.isNewControlReverseEdit;
        this.bCanntDelete = props.isNewControlCanntDelete === undefined ? false : props.isNewControlCanntDelete;
        this.bCanntEdit = props.isNewControlCanntEdit === undefined ? false : props.isNewControlCanntEdit;
        this.bHidden = props.isNewControlHidden === undefined ? false : props.isNewControlHidden;
        this.bBreakStartElement = false;
        this.bResetStartElement = false;
        this.bDirty = false;
        this.regExp = props.regExp;
        this.identifier = props.identifier;
        this.tipsContent = props.newControlInfo || '';
        this.placeholder = props.newControlPlaceHolder;
        this.enableAISupport = props.enableAISupport === undefined ? false : props.enableAISupport;
        if (props.customProperty) {
            this.addCustomProps(props.customProperty);
        }

        this.maxLength = (null == props.newControlMaxLength ? 0 : props.newControlMaxLength);
        this.bAlwaysShow = false;

        // this.reviewInfo = new ReviewInfo(this.logicDocument);
        // this.reviewType = ReviewType.Common;

        // if ( this.logicDocument && this.logicDocument.isTrackRevisions() ) {
        //     this.reviewType = ReviewType.Add;
        //     this.reviewInfo.update();
        // }
    }

    public getOperateContent(): DocumentContent {
        return this.content;
    }

    public getDocumentParent(): Document {
        const doc: Document = this.logicDocument;
        if (doc) {
            return doc;
        }
        // let parent = this.parent;
        // while (parent) {
        //     if (parent.logicDocument) {
        //         this.logicDocument =
        //     }
        // }
    }

    public updateSplitTitlePortion(portion: ParaPortion): void {
        this.titlePortion = this.oldTitlePortion = portion;
    }

    public getParentPos(pos: ParagraphContentPos): void {
        pos.splice(0, 0, [this.index]);
        const parent = this.parent;
        if (parent instanceof DocumentContent) {
            parent.getParentPos(pos);
        }
    }

    public getCurParaPortion(): ParaPortion {
        return this.content.getCurParaPortion();
    }

    public getPosition(type: MessageType): {x: number, y: number, pageNum: number} {
        let text: ParaElementBase;
        let portion: ParaPortion;
        switch (type) {
            case MessageType.EditTitle:
            case MessageType.DeleteTitle: {
                if (!this.getTitle()) {
                    return;
                }
                portion = this.content.content[0].getContent()[0];
                text = portion.content[1];
                break;
            }
            case MessageType.UnDelete:
            case MessageType.UnEdited: {
                portion = this.getCurParaPortion();
                text = portion.getCurrentText();
                break;
            }
            default: {
                return;
            }
        }

        if (!text) {
            return;
        }

        const startPos = new ParagraphContentPos();
        startPos.clear();
        portion.getParentPos(startPos);
        const textIndex = portion.content.findIndex((item) => item === text);
        startPos.add(textIndex);
        const topIndex = this.parent.getDocumentSectionType();
        startPos.splice(0, 0, [topIndex]);
        const pageNum = portion.paragraph.getCurrentPageByPos(startPos);

        return {pageNum, x: text.positionX, y: text.positionY - portion.textHeight};
    }

    public getParentRegion(): Region {
        const parent = this.parent as any;
        if (!parent) {
            return;
        }

        return parent.parent;
    }

    public addComment(bSamePara: boolean, commentId: number, comments?: any[]): number {
        return this.content.addComment(bSamePara, commentId, comments);
    }

    public insertCommentPortion(comment: Comment, pos: ParagraphContentPos, bStart?: boolean): ParagraphContentPos {
        return this.content.insertCommentPortion(comment, pos, bStart);
    }

    /**
     * 文档是否被修改
     */
    public isDirty(): boolean {
        return this.bDirty;
    }

    /**
     * 设置文档脏标记
     * @param bDirty 默认为true：已被修改
     */
    public setDirty(bDirty: boolean = true): number {
        if (this.bDirty === bDirty) {
            return ResultType.UnEdited;
        }
        this.bDirty = bDirty;
        const parent = this.parent;
        if (parent && parent.isRegionContent()) {
            (parent as any).parent.setDirty();
        }
        return ResultType.Success;
    }

    /**
     * 设置校验表达式
     * @param regExp 正则表达式
     * @returns
     */
    public setRegExp(regExp: string): number {
        if (regExp == null || this.regExp === regExp) {
            return ResultType.UnEdited;
        }
        try {
            new RegExp(regExp);
        } catch (error) {
            console.warn(error);
            return ResultType.ParamError;
        }
        this.regExp = regExp;
        return ResultType.Success;
    }

    // 获取正则表达式
    public getRegExp(): string {
        return this.regExp;
    }

    public matchRegExp(): number {
        if (!this.regExp) {
            return ResultType.Success;
        }
        const contents = this.content.content;
        // let text = '';
        let res = ResultType.Success;
        for (let index = 0, len = contents.length; index < len; index++) {
            const item = contents[index];
            const tx = item.getSelectText(true);
            if (tx && !new RegExp(this.regExp).test(tx)) {
                res =  ResultType.Failure;
                break;
            }
        }
        return res;
    }

    public matchRegExp2(): string {
        if (!this.regExp) {
            return;
        }
        const contents = this.content.content;
        // let text = '';
        // let res = ResultType.Success;
        let text: string;
        for (let index = 0, len = contents.length; index < len; index++) {
            const item = contents[index];
            const tx = item.getSelectText(true);
            if (tx && !new RegExp(this.regExp).test(tx)) {
                text = tx;
                break;
            }
        }
        if (text) {
            if (new RegExp(this.regExp).test(text)) {
                return;
            }
            let match = /\(\?\!([\s\S]+?)\)/.exec(this.regExp);
            let text1;
            if (match) {
                match = new RegExp(`(${match[1]})`).exec(text);
                if (!match) {
                    return;
                }
                text = match[1];
                text1 = '不可输入限制字:';
                // return `"${match[1]}"`;
            } else {
                text = this.regExp;
                text1 = '必须包含:';
            }

            const res = {};
            const regType = {
                '\\w': '字符',
                '\\d': '数字',
                'a-z': '小写字母',
                'A-Z': '大写字母',
                '0-9': '数字',
            };
            this.getMatchData(this.regExp, regType, res);
            const keys = Object.keys(res);
            return text1 + `"${keys.join('或')}"`;
        }

        return;
    }

    public getMatchData(text: string, regType: {[key: string]: string}, result: any, dep: number = 0): boolean {
        if (!text) {
            return;
        }
        const reg = /(\[([\s\S]+?)\])|\(([\s\S]+?)\)|(\\[dw])|((-| |\w|[\u4e00-\u9fea\uff00-\uffef\u3000-\u303f])+)/g;
        let match;
        let key: string;
        let bChanged: boolean = false;
        // tslint:disable-next-line: no-conditional-assignment
        while (match = reg.exec(text)) {
            key = match[5] || match[4] || match[3] || match[2];
            let myChanged: boolean = false;
            key = key.replace(/(\\[dw])|(a-z|A-Z|0-9)/g, (all, a, b) => {
                const c = a || b;
                myChanged = true;
                if (regType[c]) {
                    result[regType[c]] = true;
                }
                return '';
            });
            key = key.replace(/((-| |\w|[\u4e00-\u9fea\uff00-\uffef\u3000-\u303f])+)/g, (all, a, b, c) => {
                result[a] = true;
                myChanged = true;
                return '';
            });
            if (myChanged === false) {
                result[key] = true;
            }
            bChanged = true;
        }

        if (dep === 0 && !bChanged) {
            result[text] = true;
        }

        return bChanged;
    }

    public setErrorTextBgColor(type: number = 1): void {
        this.setTextErrorBgColor(type);
    }

    /**
     * 超过最大长度，设置错误提示信息
     * @param type 1: 失去颜色，2：设置颜色
     * @return false: 不需要刷新，true: 需要刷新
     */
    public setTextErrorBgColor(type: number): boolean {
        if (type === 1) {
            if (this.bMoreThenMaxLength) {
                this.setMaxLengthBgColor();
                return true;
            }
            return false;
        }
        const bMore = this.matchRegExp() === ResultType.Failure;
        if (bMore) {
            this.setMaxLengthBgColor(ErrorTextColor.Default);
            return true;
        } else if (this.bMoreThenMaxLength) {
            this.setMaxLengthBgColor();
            return true;
        }
        return false;
    }

    public setMaxLengthBgColor(color?: string): boolean {
        // const contents = this.content.content;
        // contents.forEach((para))
        // const startPortion = this.getStartBorderPortion();
        // const endPortion = this.getEndBorderPortion();
        // const contents = startPortion.paragraph.content;
        // let bStart: boolean = false;
        // for (let index = 0, length = contents.length; index < length; index++) {
        //     const portion = contents[index];
        //     if (portion === startPortion) {
        //         bStart = true;
        //         continue;
        //     }
        //     if (portion === endPortion) {
        //         break;
        //     }
        //     if (bStart) {
        //         portion.textProperty.setMaxLengthBackgroundColor(color);
        //     }
        // }
        // this.bMoreThenMaxLength = color !== undefined;
        return true;
    }

    /**
     * 是否包含表格元素
     * @returns true/false
     */
    public isContainerTableOrTitle(): boolean {
        if (this.isShowTitle3()) {
            const {startPos, endPos} = this.content.getSelection();
            const pos = Math.min(startPos, endPos);
            if (pos === 0) {
                return true;
            }
        }

        return this.content.isContainerTableOrTitle();
    }

    /**
     * 插入区域判断（不包含嵌套区域）：是否包含区域标题
     * @returns
     */
    public isContainerTitle(): boolean {
        if (this.isShowTitle3()) {
            const {startPos, endPos} = this.content.getSelection();
            const pos = Math.min(startPos, endPos);
            if (pos === 0) {
                return true;
            }
        }

        return false;
    }

    public getSelectParas(): DocumentContentElementBase[] {
        if (this.isHidden()) {
            return;
        }

        return this.content.getSelectParas();
    }

    /** 获取当前区域是否常显 */
    public getAlwaysShow(): boolean {
        return this.bAlwaysShow;
    }

    /** 设置当前区域是否常显 */
    public setAlwaysShow(bShow: boolean): number {
        if (bShow === this.bAlwaysShow) {
            return ResultType.UnEdited;
        }
        this.bAlwaysShow = bShow;
        return ResultType.Success;
    }

    public getIdentifier(): string {
        return this.identifier;
    }

    public setIdentifier(identifier: string): number {
        if (identifier == null || this.identifier === identifier) {
            return ResultType.UnEdited;
        }

        this.identifier = identifier;
        return ResultType.Success;
    }

    public getTipsContent(): string {
        return this.tipsContent;
    }

    public setTipsContent( content: string ): number {
        if ( content === this.tipsContent || null == content ) {
            return ResultType.UnEdited;
        }

        this.tipsContent = content;
        this.setDirty();
        // 这里暂时需要更新document的dirty, 后期再删除、统一改
        if ( this.logicDocument ) {
            this.logicDocument.setDirty();
        }

        return ResultType.Success;
    }

    /**
     * 设置newcontrol属性
     * @param property
     */
    public setProperty(property: INewControlProperty, bReadFile: boolean = false): number {
        let res = ResultType.UnEdited;
        res = this.setNewControlName(property.newControlName) && res;
        res = this.setTitle(property.newControlTitle, property.showTitle) && res;
        this.setTitleVisible(property.showTitle);
        res = this.setSerialNumber(property.newControlSerialNumber) && res;
        res = this.setPlaceholder(property.newControlPlaceHolder) && res;
        res = this.setHidden(property.isNewControlHidden, !bReadFile) && res;
        res = this.setDeleteProtect(property.isNewControlCanntDelete) && res;
        res = this.setEditProtect(property.isNewControlCanntEdit) && res;
        res = this.setReverseEdit(property.isNewControlReverseEdit) && res;
        res = this.addCustomProps(property.customProperty, property.bClearItems) && res;
        res = this.setMaxlength(property.newControlMaxLength) && res;
        res = this.setTipsContent(property.newControlInfo) && res;
        res = this.setIdentifier(property.identifier) && res;
        res = this.setRegExp(property.regExp) && res;
        res = this.setShowBackgroundColor(property.showBackgroundColor) && res;
        if (bReadFile === true) {
            this.bRefresh = false;
        }
        if (this.bRefresh) {
            this.logicDocument.recalculate();
            this.logicDocument.updateCursorXY();
            this.bRefresh = false;
        }
        return res;
    }

    public setTitleVisible(visible: boolean): void {
        if ( visible === undefined) {
            return;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeRegionTitleVisible(this, this.bShowTitle, visible));
        }

        this.bShowTitle = visible;
    }

    public getParaContentByXY(mouseEvent: MouseEventHandler, pageIndex: number): any {
        return this.content.getParaContentByXY(mouseEvent, pageIndex);
    }

    public isShowTitle2(): boolean {
        return this.bShowTitle;
    }

    public isShowTitle3(): boolean {
        return this.bShowTitle && !!this.title;
    }

    public getTitlePortion(): ParaPortion {
        return this.titlePortion;
    }

    public setParagraphAlignment(alignment: number): boolean {
        return this.content.setParagraphAlignment(alignment);
    }

    public setParentPos(pos: ParagraphContentPos): void {
        pos.unShift(this.index);
        if (this.parent && 'setParentPos' in this.parent) {
            (this.parent as any).setParentPos(pos);
        }
    }

    public setRegionText(text: string, bRefresh: boolean = false): number {
        if (text == null) {
            return ResultType.UnEdited;
        }
        const res = this.logicDocument.removeRegion2(this, 2);
        if (res !== ResultType.Success) {
            return res;
        }
        // const para = this.content.content[0];
        let para: Paragraph;
        const titlePortion = this.titlePortion;
        let textProp: any;
        let index = 0;
        if (titlePortion) {
            para = titlePortion.paragraph;
            // textProp = titlePortion.textProperty.copy();
            index++;
        } else {
            para = this.content.content[0] as any;
            if (para.getType() !== DocumentContentType.Paragraph) {
                return ResultType.Failure;
            }
        }

        if ( this.logicDocument && this.logicDocument.getRegionContentDefaultFont() ) {
            textProp = this.logicDocument.getRegionContentDefaultFont()
                                            .copy();
        }

        const portion = new ParaPortion(para);
        if (textProp) {
            portion.textProperty = textProp;
        }
        portion.addText(text);
        para.addToContent(index, portion);
        if (bRefresh) {
            this.logicDocument.recalculate();
        }
        return ResultType.Success;
    }

    public updateTitle(): number {
        const titlePortion = this.titlePortion;
        if (this.bShowTitle && this.title && titlePortion) {
            let para = this.content.content[0];
            if (para.getType() !== DocumentContentType.Paragraph) {
                para = new Paragraph(this.content, this.logicDocument);
                this.content.addToContent(0, para);
            }
            this.setText(this.title, this.titlePortion);
            if (titlePortion.paragraph !== para) {
                para.addToContent(0, this.titlePortion);
            }
            const manager = this.logicDocument.getNumManager();
            manager.removeLvlByPara(this.titlePortion.paragraph);
            return ResultType.Success;
        }

        return ResultType.UnEdited;
    }

    public getName(): string {
        return this.name;
    }

    public getType(): DocumentContentType {
        return DocumentContentType.Region;
    }

    public getNewControlType(): number {
        return NewControlType.Region;
    }

    public getTableCellByXY(x: number, y: number, pageIndex: number): any {
        return this.content.getTableCellByXY(x, y, pageIndex);
    }

    public getSearchInfos(search: any): void {
        if (this.isHidden()) {
            return;
        }
        this.content.getSearchInfos(search);
    }

    public setNewControlName(name: string): number {
        if ( name === this.name || null == name ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeRegionName(this, this.name, name));
        }

        this.setDirty(true);
        this.name = name;
        return ResultType.Success;
    }

    public getNewControlName(): string {
        return this.name;
    }

    public addCustomPropItem(key: string, value: string, type: DataType = DataType.String, targetValue?: any): number {
        this.setDirty(true);
        this.customProperty.set(key, {name: key, value, type, targetValue: targetValue || value});
        return ResultType.Success;
    }

    public clearCustom(): void {
        this.customProperty.clear();
    }

    public addCustomProps(props: ICustomProps[], bClearCustom: boolean = true): number {
        if (!props) {
            return ResultType.UnEdited;
        }
        this.setDirty(true);
        if (bClearCustom) {
            this.clearCustom();
        }
        // this.customProperty.clear();
        props.forEach((prop) => {
            this.customProperty.set(prop.name, prop);
            switch (prop.type) {
                case DataType.Boolean:
                    prop.targetValue = prop.value ? parseBoolean(prop.value) : undefined;
                    break;
                case DataType.Number:
                    prop.targetValue = prop.value ? Number(prop.value) : undefined;
                    break;
                default:
                    prop.targetValue = prop.value;
            }
        });
        return ResultType.Success;
    }

    public getCustomByPropName(name: string): any {
        const prop = this.customProperty.get(name);
        if (prop == null) {
            return;
        }

        return prop.targetValue;
    }

    public getCustomProps(): ICustomProps[] {
        const arrs = [];
        for (const [name, prop] of this.customProperty) {
            arrs.push(prop);
        }

        return arrs;
    }

    public isInCustomProp(name: string, value: string): boolean {
        const customProps = this.customProperty;
        for (const [actName, prop] of customProps) {
            if (prop.name === name && prop.value === value) {
                return true;
            }
        }
        return false;
    }

    public getCurrentPageLastLine(pageIndex: number, result?: any): any {
        return this.content.getCurrentPageLastLine(pageIndex, result);
    }

    public getParaLines(pageIndex: number, result?: {paras: any[], tables: any[]}): any {
        this.content.getParaLines(pageIndex, result);
    }

    /**
     * 是否编辑保护
     */
    public isEditProtect(): boolean {
        return this.bCanntEdit;
    }

    public setEditProtect( bCanntEdit: boolean ): number {
        if ( bCanntEdit === this.bCanntEdit || null == bCanntEdit ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeRegionEditProtect(this, this.bCanntEdit, bCanntEdit));
        }

        this.setDirty(true);
        this.bCanntEdit = bCanntEdit;
        if ( this.getDocumentParent() ) {
            this.getDocumentParent()
                .setDirty();
        }
        return ResultType.Success;
        // this.content.setEditProtect(bCanntEdit, this.parent);
    }

    public isShowBackgroundColor(): boolean {
        return this.bShowBackgroundColor;
    }

    public setShowBackgroundColor(value: boolean): number {
        if (typeof value !== 'boolean' || value === this.bShowBackgroundColor) {
            return ResultType.UnEdited;
        }
        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeRegionShowBackgroundColor(this, this.bShowBackgroundColor, value));
        }

        this.setDirty(true);
        this.bShowBackgroundColor = value;
        if ( this.getDocumentParent() ) {
            this.getDocumentParent()
                .setDirty();
        }
        return ResultType.Success;
    }

    public deleteCurrentHideRegion(): boolean {
        const region = this;
        if (!region.isHidden()) {
            return false;
        }
        // 根据需求，假如隐藏元素有不可删除，也会被删除
        // if (region.isDeleteProtect()) {
        //     return false;
        // }
        const parent = region.parent;
        region.selectAll(1);
        const content = region.getOperateContent();
        const startContentPos = content.getStartRemoveSelectionPos();
        const endContentPos = content.getEndRemoveSelectionPos();
        const newControlManager = parent.getNewControlManager();
        newControlManager.remove(startContentPos, endContentPos);

        this.setDeleteFlag(true);
        parent.contentRemove(region.index, 1);
        return true;
    }

    public isDeleteProtect(): boolean {
        return this.bCanntDelete;
    }

    public isDeleteProtect2(): boolean {
        return this.bCanntDelete && !this.logicDocument.isAdminMode();
    }

    public setDeleteProtect( bCanntDelete: boolean ): number {
        if ( bCanntDelete === this.bCanntDelete || null == bCanntDelete ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeRegionDeleteProtect(this, this.bCanntDelete, bCanntDelete));
        }

        this.setDirty(true);
        this.bCanntDelete = bCanntDelete;
        if ( this.getDocumentParent() ) {
            this.getDocumentParent()
                .setDirty();
        }

        return ResultType.Success;
        // this.content.setDeleteProtect(bCanntDelete, this.parent);
    }

    public isReverseEdit(): boolean {
        return this.bReverseEdit;
    }

    public setReverseEdit( bReverseEdit: boolean ): number {
        if ( bReverseEdit === this.bReverseEdit || null == bReverseEdit ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeRegionReverseEdit(this, this.bReverseEdit, bReverseEdit));
        }

        this.setDirty(true);
        this.bReverseEdit = bReverseEdit;
        if ( this.getDocumentParent() ) {
            this.getDocumentParent()
                .setDirty();
        }

        return ResultType.Success;
    }

    public isHidden(bSource: boolean = false): boolean {
        if (bSource) {
            return this.isSourceHide();
        }
        return this.bHidden && !this.logicDocument.isDesignModel();
    }

    public isSourceHide(): boolean {
        return this.bHidden;
    }

    public parentHidden(bSource: boolean = false): boolean {
        if (this.isHidden(bSource)) {
            return true;
        }

        const parent: any = this.parent;
        if (parent.isRegionContent()) {
            return parent.parentHidden(bSource);
        }
        return false;
    }

    public setHidden( bHidden: boolean, bUpdateCurPos: boolean = true ): number {
        if ( bHidden === this.bHidden || null == bHidden ) {
            return ResultType.UnEdited;
        }
        const parent = this.parent;
        this.bHidden = bHidden;
        if (!parent) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeRegionHidden(this, this.bHidden, bHidden));
        }

        // this.bHidden = bHidden;
        // this.content.setHidden(bHidden);
        // const doc = this.getDocumentParent();
        // if (bUpdateCurPos === true && bHidden === true) {
        //     this.updateCursorXY(doc);
        // }
        // doc.setDirty();
        this.setDirty(true);
        this.bRefresh = true;
        let index = this.index;
        if (bHidden) {
            index = parent.getNextElementIndex(this.index, parent.content);
            if (index === this.index) {
                index = parent.getPrevElementIndex(index);
            }
        }
        if (parent.curPos.contentPos === index) {
            this.selectTopPos();
            parent.curPos.contentPos = index;
            if (bUpdateCurPos === true) {
                parent.content[index].moveCursorLeft();
            }
            this.logicDocument.removeSelection();
        }

        return ResultType.Success;
    }

    public addDocContentChild(options: any, cleanMode: CleanModeType): void {
        // const manager = this.logicDocument.getRegionManager();
        // const parent = this.parent; // = options.parent;
        // if (parent.isRegionContent()) {
        //     manager.addLeaf((parent as any).parent.getName(), this);
        // } else {
        //     manager.addRegion(this);
        // }
        // options = {...options};
        // this.content.setParent(this);
        // options.parent = this;
        // this.setLogicDocument(options.doc);
        this.content.addDocContentChild(options, cleanMode);
    }

    public getTitle(): string {
        return this.title;
    }

    public getSerialNumber(): string {
        return this.serialNumber;
    }

    public setSerialNumber(serialNum: string): number {
        if (serialNum == null || serialNum === this.serialNumber) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeRegionSerialNumber(this, this.serialNumber, serialNum));
        }
        this.serialNumber = serialNum;
        this.setDirty();

        return ResultType.Success;
    }

    public setMaxlength(length: number): number {
        if ( null == length || length === this.maxLength ) {
            return ResultType.UnEdited;
        } else if ( 0 > length ) {
            return ResultType.ParamError;
        }

        const history = this.getHistory();
        // history.addChange(new ChangeRegionSerialNumber(this, this.maxLength, length));
        this.maxLength = length;
        this.setDirty();

        return ResultType.Success;
    }

    public getMaxLength(): number {
        return this.maxLength;
    }

    public getTextLength(bSkipSomeCh: boolean = false): number {
        return this.content.getTextLength(0 < this.maxLength);
    }

    public getTopParent(): Region {
        let parent = this.parent;
        let region: Region;
        if (parent.isRegionContent()) {
            region = (parent as any).parent;
            parent = region.parent;
        }
        return region;
    }

    public copyCacheData(): Region {
        const region = new Region(null, this.logicDocument);
        region.bLoadCache = true;
        region.bCanntDelete = true;
        region.relevanceId = this.id;
        region.bAlwaysShow = true;
        region.bCanntEdit = true;
        region.name = this.name;
        // region.placeholder = this.placeholder;
        const para: Paragraph = new Paragraph(region.content, this.logicDocument);
        const portion = new ParaPortion(para);
        const prop = new TextProperty();
        prop.color = '#999';
        portion.addText('未加载：' + (this.placeholder || ''), prop, 0);
        para.addToContent(0, portion);
        region.addToContent(0, para);
        return region;
    }

    /**
     * 使用opendoc时使用，设置缓存加载，加快渲染速度
     * @param flag 暂时只能为true
     * @returns
     */
    public setLoadCache(flag: boolean): void {
        if (typeof flag !== 'boolean' || this.bHidden && this.bLoadCache === undefined) {
            return;
        }
        this.bLoadCache = flag;
        this.bHidden = flag;
        // this.bAlwaysShow = true;
        const region = this.copyCacheData();
        const doc = this.logicDocument;
        const contents: any[] = doc.content;
        const id = this.id;
        let index = contents.findIndex((item) => item.id === id);
        if (index === -1) {
            index = doc.content.length;
        }
        doc.addToContent(index, region, true);
    }

    public isLoadCache(): boolean {
        return this.bLoadCache;
    }

    public getRelevanceId(): number {
        return this.relevanceId;
    }

    /**
     * 保存时使用，判断当前区域是否缓存区域，这里还会进行隐藏恢复，否则出现保存跟需求的不一样
     * @returns
     */
    public isLoadCacheRegion(): boolean {
        const res: boolean = this.relevanceId !== undefined;
        if (res !== true && this.bLoadCache !== undefined) {
            const bHidden = this.bHidden;
            if (bHidden === true) {
                // 改变状态
                this.bHidden = false;
                // 还原状态
                Promise.resolve()
                .then(() => {
                    this.bHidden = bHidden;
                });
            }
        }

        return res;
    }

    public getTopElement(): DocumentContentElementBase {
        const parent = this.parent;
        if (parent.isRegionContent()) {
            return parent.getTopElement();
        }
        return this;
    }

    public getPlaceholder(): string {
        return this.placeholder;
    }

    /**
     * 获取是否启用AI支持
     * @returns 是否启用AI支持
     */
    public getEnableAISupport(): boolean {
        return this.enableAISupport;
    }

    /**
     * 设置是否启用AI支持
     * @param enable 是否启用AI支持
     * @returns 操作结果
     */
    public setEnableAISupport(enable: boolean): number {
        if (enable === this.enableAISupport) {
            return ResultType.UnEdited;
        }
        this.enableAISupport = enable;
        this.bRefresh = true;
        return ResultType.Success;
    }

    public setPlaceholder(placeholder: string): number {
        if (placeholder == null || placeholder === this.placeholder) {
            return ResultType.UnEdited;
        }
        this.placeholder = placeholder;
        if (this.bLoadCache === true) {
            const region = this.getRegionByRelevanceId(this.index, this.id, true);
            if (region) {
                const portion: ParaPortion = region.content.content[0].getContent()[0];
                portion.content.length = 0;
                portion.addText('未加载：' + placeholder);
            }
        }
        this.bRefresh = true;
        return ResultType.Success;
    }

    /**
     * 切换显示状态
     * @param flag 根据当前状态显示占位符或者正文
     * @returns 是否切换成功
     */
    public changeLoadCache(flag: boolean): number {
        if (typeof this.bLoadCache !== 'boolean') {
            return -1;
        }
        if (this.relevanceId === undefined && this.bHidden === flag) {
            return -2;
        }
        const region: Region = this.getRegionByRelevanceId(this.index, this.relevanceId || this.id, this.relevanceId === undefined);
        // if (this.relevanceId === undefined) {
        //     region = this.getRegionByRelevanceId(this.index, this.id, false);

        // } else {
        //     region = this.getRegionByRelevanceId(this.index, this.id, true);
        // }
        if (!region) {
            return -1;
        }

        this.bHidden = flag;
        region.bHidden = !flag;
        let index;
        if (flag) {
            region.moveCursorToStartPos();
            index = region.index;
        } else {
            this.moveCursorToStartPos();
            index = this.index;
        }
        return index;
    }

    public getRegionByRelevanceId(activeIndex: number, id: number, bPrev: boolean): Region {
        const contents = this.logicDocument.content;
        let content: Region;
        if (bPrev === true) {
            for (let index = activeIndex - 1; index > -1; index--) {
                content = contents[index];
                if (content.relevanceId === id) {
                    return content;
                }
            }
        } else {
            for (let index = activeIndex + 1, len = contents.length; index < len; index++) {
                content = contents[index];
                if (content.id === id) {
                    return content;
                }
            }
        }
    }

    public selectTopPos(): void {
        let parent: any;
        let region: Region = this;
        parent = region.parent;
        const selection = parent.selection;
        selection.bUse = true;
        parent.curPos.contentPos = selection.endPos = selection.startPos = region.index;
        if (!parent.isRegionContent()) {
            return;
        }
        region = parent.parent;
        region.selectTopPos();
    }

    public setContentCurPos(): void {
        let parent: any;
        let region: Region = this;
        do {
            parent = region.parent;
            parent.curPos.contentPos = region.index;
            if (!parent.isRegionContent()) {
                break;
            }
            region = parent.parent;
        }
        while (true);
    }

    public isShowTitle(): boolean {
        if (this.titlePortion) {
            return true;
        }
        return false;
    }

    public setTitle(title: string, visible: boolean): number {
        if ( null == title ) {
            return ResultType.UnEdited;
        }

        if (title === this.title && (visible === undefined || visible === this.bShowTitle)) {
            return ResultType.UnEdited;
        }

        if ( title !== this.title ) {
            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangeRegionTitle(this, this.title, title));
            }
            this.setDirty(true);
            this.title = title;
            //删除区域标题
            if (title.length === 0) {
                this.bShowTitle = false;
                visible = false;
            }
        }

        if (visible === false) {
            if (this.removeTitlePortion() === ResultType.UnEdited) {
                return ResultType.UnEdited;
            }
            this.setDirty(true);
            this.bRefresh = true;
            return ResultType.Success;
        }

        this.setDirty(true);
        if (!title) {
            if (this.removeTitlePortion() === ResultType.UnEdited) {
                return ResultType.UnEdited;
            }
            this.bRefresh = true;
            return ResultType.Success;
        }
        const portion = this.titlePortion;
        if (!portion) {
            return this.addTitlePortion(title);
        }

        const manager = this.logicDocument.getNumManager();
        manager.removeLvlByPara(this.titlePortion.paragraph);
        portion.removeFromContent(1, portion.content.length - 1, true);
        this.setText(title);

        this.bRefresh = true;
        return ResultType.Success;
    }

    public addTitlePortion(title: string): number {
        if (!title) {
            return ResultType.UnEdited;
        }
        const first: any = this.content.content[0];
        let para: Paragraph;
        if (!first || first.getType() !== DocumentContentType.Paragraph) {
            para = new Paragraph(this.content, this.logicDocument);
            this.content.addToContent(0, para);
        } else {
            para = first;
        }

        let portion: ParaPortion;
        if (this.oldTitlePortion) {
            portion = this.oldTitlePortion;
        } else {
            portion = new ParaPortion(para);
            portion.setTextBold(FontWeightType.Bold);
        }

        this.setText(title, portion);
        para.addToContent(0, portion);
        this.oldTitlePortion = this.titlePortion = portion;
        const manager = this.logicDocument.getNumManager();
        manager.removeLvlByPara(this.titlePortion.paragraph);

        this.bRefresh = true;
        return ResultType.Success;
    }

    public addPasteTitlePortion(): number {
        const title = this.title;
        if (!title || !this.bShowTitle) {
            return ResultType.UnEdited;
        }
        const para: any = this.content.content[0];
        if (!para || para.getType() !== DocumentContentType.Paragraph) {
            return this.addTitlePortion(title);
        }
        const portion: ParaPortion = para.content[0];
        if (!portion || !portion.content.length) {
            return this.addTitlePortion(title);
        }
        const contents = portion.content;
        if (contents.length !== title.length) {
            return this.addTitlePortion(title);
        }
        let allText = '';
        contents.forEach((item) => {
            allText += item.content;
        });
        if (allText !== title) {
            return this.addTitlePortion(title);
        }
        const text = new ParaTextExtend(title[0], ParaTextName.Region);
        portion.content.splice(0, 1, text);
        this.titlePortion = this.oldTitlePortion = portion;
        return ResultType.Success;
    }

    public getPageContentStartPos(pageNum: number): any {
        return this.parent.getPageContentStartPos(pageNum + this.content.startPage,  this.index);
    }

    public filterContentNodes(type: number): DocumentContentElementBase[] {
        return this.content.filterContentNodes(type);
    }

    public isSelectedOrCursorInNewControlTitle(): boolean {
        return this.content.isSelectedOrCursorInNewControlTitle();
    }

    // public getAbsolutePage(curPage: number): number {
    //     return this.parent.getAbsolutePage(curPage);
    // }

    // public getAbsoluteStartPage(): number {
    //     return; // this.parent.getAbsoluteStartPage();
    // }

    public getSectPr(): any {
        return; // this.content.getSectPr();
    }

    public clear(): void {
        // this.content.clear();
    }

    public isEmpty(): boolean {
        return this.content.isEmpty();
    }

    public isInline(): boolean {
        return true; // this.content.inline();
    }

    public startFromNewPage(): void {
        this.content.startFromNewPage();
    }

    public isContentOnFirstPage(): boolean {
        return this.content.isContentOnFirstPage();
    }

    public isStartFromNewPage(): boolean {
        if ( ( 1 < this.content.pages.length && true === this.isEmptyPage(0) ) || ( null === this.getDocumentPrev() )) {
            return true;
        }

        return false;
    }

    public getElementByPos(index: any, bCompositeInput: boolean = false ): any {
        return; // this.content.getElementByPos(index, bCompositeInput);
    }

    public getParaContentPos(bSelection?: boolean, bStart?: boolean): any {
        return; // this.content.getParaC;
    }

    public setImageSelection(startPos: ParagraphContentPos): void {
        return; // this.content.setImageSelection(startPos);
    }

    public getAllImagesName(): string[] {
        const images: string[] = [];
        // const contents = this.getContent();
        const drawings = this.logicDocument
                            ?.getDrawingObjects()
                            ?.getGraphicObject();
        if (drawings) {
            for (const [ name, draw ] of drawings) {
                let parent = draw.portion.paragraph;
                while (parent) {
                    if (parent instanceof Document) {
                        break;
                    }
                    if (parent instanceof Region) {
                        if (parent.getId() === this.id) {
                            images.push(draw.getDrawingName());
                            break;
                        }
                    }
                    if (parent instanceof TableCell) {
                        parent = parent.getRow()
                                       .getTable();
                    }
                    parent = parent.getParent?.();
                }
            }
            // for (const content of contents) {
            //     if (content instanceof Paragraph || content instanceof Table) {
            //         images = images.concat(content.getAllImagesName());
            //     }
            // }
        }
        return images;
    }

    public moveCursorRightWithSelectionFromStart(): void {
        this.content.moveCursorToStartPos(false, true);
    }

    public moveCursorLeftWithSelectionFromEnd(): void {
        this.content.moveCursorToEndPos(false, true);
    }

    public remove( direction: number, bOnlyText: boolean, bOnlySelection: boolean,
                   bAddText: boolean, bSelectParaEnd?: boolean ): IOperateResult {
        const res = this.content.remove( direction, bOnlyText, bOnlySelection, bAddText, bSelectParaEnd);
        if (res) {
            this.setDirty(true);
        }
        return res;
    }

    public getRegion(): Region {
        return this;
    }

    public getRegionManager(): RegionManager {
        if ( this.logicDocument ) {
            return this.logicDocument.getRegionManager();
        }

        return null;
    }

    public add(item: any): number {
        const res =  this.content.add(item);
        if (res === ResultType.Success) {
            this.setDirty(true);
        }
        return res;
    }

    public addToContent(nPos: number, item: DocumentContentElementBase, unEmptyPara?: boolean): void {
        this.content.addToContent(nPos, item, unEmptyPara);

        // if ( this.logicDocument && this.logicDocument.isTrackRevisions() ) {
        //     if ( ReviewType.Common === item.getReviewType() ) {
        //         item.setReviewTypeWithInfo(ReviewType.Add, item.getReviewInfo());
        //     }

        //     this.updateTrackRevisions();
        // }

        this.setDirty(true);
    }

    public addNewParagraph(): void {
        const res = this.content.addNewParagraph();
        if (res === ResultType.Success) {
            this.setDirty(true);
        }
    }

    public addSoftNewParagraph(): number {
        return this.content.addSoftNewParagraph();
    }

    public addNewControl(newControl: any, parentNewControl: any, property: any, text: string): number {
        const res = this.content.addNewControl(property, text);
        if (res) {
            this.setDirty(true);
        }

        return res;
    }

    public isCursorAtEnd(): boolean {
        return this.content.isCursorAtEnd();
    }

    public isCursorAtBegin(): boolean {
        return this.content.isCursorAtBegin();
    }

    public isCursorInNewControl(): boolean {
        return this.content.isCursorInNewControl();
    }

    /**
     * 获取当前光标位置所在的newControl元素
     */
    public getCursorInNewControl(): NewControl {
        return this.content.getCursorInNewControl();
    }

    public getNewControlParaBounds( newControlName: string, startPos: number,
                                    endPos: number ): any {
        return this.content.getNewControlParaBounds(newControlName, startPos, endPos).lines;
    }

    /**
     * 当前鼠标位置是否处于newControl元素内
     * @param pointX
     * @param pointY
     * @param pageIndex
     */
    public isFocusInNewControl(pointX: number, pointY: number, pageIndex: number): boolean {
        return this.content.isFocusInNewControl(pointX, pointY, pageIndex);
    }

    /**
     * 获取当前鼠标位置所在的newControl元素
     * @param pointX
     * @param pointY
     * @param pageIndex
     */
    public getFocusInNewControl(pointX: number, pointY: number, pageIndex: number): NewControl {
        return this.content.getFocusInNewControl(pointX, pointY, pageIndex);
    }

    public getNewControlBounds( newControl: NewControl, mouseEvent?: MouseEventHandler,
                                pageIndex?: number ): IDrawNewControlBounds {
        return this.content.getNewControlBounds(newControl);
    }

    /**
     * 根据当前插入newControl的光标位置，获取在documentContentBase中的相关位置信息
     * @param portionPos
     */
    // public getCurContentPosInDoc(bSelection?: boolean, bStart?: boolean, bDeep?: boolean): ParagraphContentPos {
    //     return this.content.getCurContentPosInDoc(bSelection, bStart);
    // }

    public getSelection(): any {
        return this.content.getSelection();
    }

    public isInCustomProp2(name: string, value: any): boolean {
        const customProps = this.customProperty;
        for (const [actName, prop] of customProps) {
            if (prop.name === name && prop.value === value) {
                return true;
            }
        }
        return false;
    }

    public isInCustomProp3(obj: object): boolean {
        const customProps = this.customProperty;
        const keys = Object.keys(obj);
        for (let index = 0, length = keys.length; index < length; index++) {
            const key = keys[index];
            const customProp = customProps.get(key);
            if (!customProp || customProp.value !== obj[key]) {
                return false;
            }
        }
        return true;
    }

    public isSelectionUse(): boolean {
        return this.content.isSelectionUse();
    }

    public getSelectionStartPos( direction: number ): any {
        return null;
    }

    /**
     * newcontrol左边框辅助自动选中
     */
    public setSelectionStart2(): void { return ; }

    public setSelectionStart3(): void { return ; }

    public getSelectedLines( selections: any, bParaEnd?: boolean ): any {
        return; // this.content.getSelectLines();
    }

    public checkPosInSelection( pageIndex: number, pointX: number, pointY: number ): boolean {
        return false;
    }

    public concat(element: any, bDelFromKeyDown?: boolean): void { return ; }

    public contentConcat(element: any): void { return ; }

    public setParagraphProperty(paraProperty: any): boolean {
        const res = this.content.setParagraphProperty(paraProperty);
        if (res) {
            this.setDirty(true);
        }

        return res;
    }

    public getParagraphProperty(): any {
        return this.content.getParagraphProperty();
    }

    public hasPageBreak(): boolean {
        return false;
    }

    public getLinesByPageId( pageIndex: number ): any {
        return null;
    }

    public getElementStartLineByPageId( pageIndex: number ): number {
        return null;
    }

    public getElementEndLineByPageId( pageIndex: number ): number {
        return null;
    }

    public getDocumentElementState(): DocumentElementState[][] {
        return this.content.getDocumentElementState();
    }

    public setDocumentElementState(state: any, stateIndex: number): void {
        this.content.setDocumentState(state, state.length - 1);
     }

    public shift(curPage: number, shiftDx: number, shiftDy: number): void { return ; }

    public preDelete(): void { return ; }

    public getCurPageLastNode(): any {
        return;
    }

    public getEmptyHeight(): number { return 0; }

    /**
     * 根据当前插入newControl的光标位置，获取在documentContentBase中的相关位置信息
     * @param portionPos
     */
    public getCurContentPosInDoc(bSelection?: boolean, bStart?: boolean, bDeep?: boolean): ParagraphContentPos {
        const pos = this.content.getCurContentPosInDoc(bSelection, bStart, bDeep);
        return pos;
    }

    /**
     * 当前选择区域是否有不可删除内容
     * 或者当前光标处不可删除
     */
    public isRegionValidDelete(bSameElementPos: boolean): boolean {
        if (this.bCanntDelete === true && !bSameElementPos && this.content.isSelectedAll()) {
            // message.error( '区域设置了保护属性，无法删除');
            gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.UnEdited);
            return false;
        }

        return this.content.isRegionValidDelete();
    }

    public selectAreaByPos(startPos: ParagraphContentPos, endPos: ParagraphContentPos, dir?: number): boolean {
        return this.content.selectAreaByPosBase(startPos, endPos, dir);
    }

    // public isEmptyPage(): boolean {
    //     return this.content.isEmptyPage();
    // }

    /**
     * 当前元素的开始页面
     */
    public getRelativeStartPage(): number {
        return this.pageNum;
    }

    public recalculateCurPos(bUpdateX: boolean = true, bUpdateY: boolean = true): ICurrentCursorPos {
        return this.content.recalculateCurPos(bUpdateX, bUpdateY);
    }

    public recalculateCurPosForContent(bUpdateX: boolean = true, bUpdateY: boolean = true): ICurrentCursorPos {
        return this.content.recalculateCurPos(bUpdateX, bUpdateY);
    }

    public getCurPosXY(): ICursorProperty {
        return this.content.getCursorPosXY();
    }

    public setCurPosXY(pointX: number, pointY: number): void {
        this.content.setCurPosXY(pointX, pointY);
    }

    public moveCursorToStartPosContent(): void {
        this.content.moveCursorToStartPos();
    }

    public moveCursorToXYForContent(pageIndex: number, pointX: number, pointY: number,
                                    bLine: boolean = false, bDontChangeRealPos: boolean = true): void {
        this.content.moveCursorToXY(pageIndex, pointX, pointY, bLine, bDontChangeRealPos);
    }

    public moveCursorUpToLastRow(pointX: number, pointY: number, bShiftLey: boolean = false): void {
        this.content.moveCursorUpToLastRow(pointX, pointY, bShiftLey);
    }

    public moveCursorDownToFirstRow(pointX: number, pointY: number, bShiftLey: boolean = false): void {
        this.content.moveCursorDownToFirstRow(pointX, pointY, bShiftLey);
    }

    public moveCursorToStartPos(bAddToSelect: boolean = false, bSelectFromStart: boolean = false): void {
        this.content.moveCursorToStartPos(bAddToSelect, bSelectFromStart);
    }

    public moveCursorToEndPos(bAddToSelect: boolean = false, bSelectFromStart: boolean = false): void {
        this.content.moveCursorToEndPos(bAddToSelect, bSelectFromStart);
    }

    public jumpOrDeleteTheHidden(direction: number = -1): number {
        if (this.bHidden !== true) {
            return;
        }

        // del向后删除
        if (direction === 1) {
            return this.jumpOrDeleteTheHidden1();
        }

        const parent = this.parent;
        const contents = this.parent.content;
        let index = this.index;
        let bDelete = false;
        for (; index >= 0; index--) {
            const content = contents[index];
            if (content.isHidden() !== true) {
                break;
            }

            if (content instanceof Region && content.bCanntDelete !== true) {
                parent.contentRemove(index, 1);
                bDelete = true;
            }
        }
        if (bDelete) {
            this.setDeleteFlag();
        }
        return this.index - index  + 1;
    }

    public recalculateMinmaxContentWidth(bRotated: boolean): {Min: number; Max: number} {
        if ( undefined === bRotated ) {
            bRotated = false;
        }

        // if ( true === this.isVerticalText() ) {
        //     bRotated = ( true === bRotated ) ? false : true;
        // }

        const result = this.content.recalculateMinmaxContentWidth(bRotated);

        return result;
    }

    /**
     * 设置选择开始位置
     * @param pointX
     * @param pointY
     * @param curPage
     * @param mouseEvent
     */
    public setSelectionStart(pointX: number, pointY: number, curPage: number, mouseEvent: IMouseEvent): void {
        this.content.setSelectionStart(pointX, pointY, curPage, mouseEvent);
    }

    /**
     * 设置选择结束位置
     * @param pointX
     * @param pointY
     * @param curPage
     * @param mouseEvent
     */
    public setSelectionEnd(pointX: number, pointY: number, curPage: number, mouseEvent: IMouseEvent): void {
        this.content.setSelectionEnd(pointX, pointY, curPage, mouseEvent);
    }

    public isReadOnly(bOnlySelectCells?: boolean): boolean {
        const bAdminMode = this.logicDocument.isAdminMode();
        if ( bAdminMode ) {
            return false;
        }

        if (this.bCanntEdit) {
            return true;
        }
        if (this.bReverseEdit) {
            return false;
        }
        let bReadOnly = false;
        let parent: any = this.parent;
        while (parent instanceof DocumentContent && parent.isRegionContent()) {
            parent = parent.parent;
            bReadOnly = parent.bCanntEdit;
            if (bReadOnly === true) {
                break;
            }
            parent = parent.parent;
        }

        if (bReadOnly === true && this.bReverseEdit) {
            return false;
        }

        if ( !bReadOnly && bOnlySelectCells ) {
            const contents = this.content;
            if ( this.isSelectionUse() ) {
                let startPos = contents.selection.startPos;
                let endPos = contents.selection.endPos;
                if ( startPos > endPos ) {
                    const temp = endPos;
                    endPos = startPos;
                    startPos = temp;
                }

                for (let pos = startPos; pos <= endPos; pos++) {
                    if (contents.content[pos].isReadOnly(bOnlySelectCells)) {
                        return true;
                    }
                }
            } else {
                const curPos = contents.getCurPos();
                const element = contents.content[curPos.contentPos];
                if ( element && (element.isTable() || element.isRegion()) ) {
                    return element.isReadOnly(bOnlySelectCells);
                }
            }
        }

        return bReadOnly;
    }

    /**
     * 光标处是否为只读状态
     */
    public isCursorReadOnly(): boolean {
        return false;
        // return ( this.isReadOnly() || this.content.isCursorReadOnly() );
    }

    public isCursorInRegion(): boolean { return true; }

    // public isPopWinNewControl(): boolean {
    //     return this.content.isPopWinNewControl();
    // }

    public canInput(): boolean {
        if ( true === this.isReadOnly() ) {
            return false;
        }

        if ( this.isSelectionUse() ) {
            return this.canDelete();
        } else if (!this.content.isNotDeleteRegionTitle(this, true)) {
            // alert('当前光标在元素标题中，无法输入');
            // message.error('当前光标在元素标题中，无法输入');
            gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.EditTitle);
            return false;
        }

        return true;
    }

    public canDeleteSelf(): boolean {
        if (this.logicDocument.isAdminMode()) {
            return true;
        }
        if (this.isDeleteProtect()) {
            return false;
        }
        // has son Region
        const regionManager = this.getRegionManager();
        if (regionManager) {
            const regions = regionManager.getRegionMap();
            for (const [ name, region ] of regions) {
                if (region) {
                    const regionParent = region.getParentRegion();
                    if (regionParent && regionParent.getId() === this.id && !region.canDeleteSelf()) {
                        return false;
                    }
                }
            }
        }
        // has disDeleted NewControl
        const newControls = this.content.getAllNewControls();
        for (const newControl of newControls) {
            if (newControl && newControl.isDeleteProtect()) {
                return false;
            }
        }
        // has disDeleted Table
        const tableManager = this.logicDocument.getTableManager();
        if (tableManager) {
            const tableMap = tableManager.getTableMap();
            for (const [ name, table ] of tableMap) {
                if (!table) {
                    continue;
                }
                let parent = table.getParent();
                if (parent instanceof DocumentContent) {
                    parent = parent.getParent();
                }
                if (parent instanceof Region &&
                    parent.getId() === this.id &&
                    !table.canDeleteSelf()) {
                    return false;
                }
            }
        }
        // has disDeleted Image
        const images = this.logicDocument?.getDrawingObjects()
                                        ?.getGraphicObject();
        if (images) {
            for (const [ name, draw ] of images) {
                if (!draw.portion) {
                    continue;
                }
                let parent = draw.portion.paragraph;
                while (parent) {
                    if (parent instanceof Document) {
                        break;
                    }
                    if (parent instanceof Region) {
                        if (parent.getId() === this.id && draw.isDeleteLocked()) {
                            return false;
                        }
                    }
                    if (parent instanceof TableCell) {
                        parent = parent.getRow()
                                       .getTable();
                    }
                    parent = parent.getParent?.();
                }
            }

        }
        return true;
    }

    public canDelete(): boolean {
        if ( this.isSelectedAll()) {
            const parent = this.parent;
            if ( parent.selection.bUse &&
                parent.selection.startPos !== parent.selection.endPos ) {
                if (this.isDeleteProtect()) {
                    // alert('区域设置了删除保护，无法删除');
                    const bAdminMode = this.logicDocument.isAdminMode();

                    if ( bAdminMode ) {
                        return true;
                    } else {
                        gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.UnDelete);
                        // message.error('区域设置了删除保护，无法删除');
                        return false;
                    }
                }
            } else if (this.isShowTitle()) {
                gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.DeleteTitle);
                // message.error('当前光标在元素标题中，无法删除');
                return false;
            }
        }

        const bDelete = this.content.canDelete();
        this.logicDocument.getRegionManager()
            .setDeleteFlag(bDelete);

        return bDelete;
    }

    public canInsertNewControl(): boolean {
        return this.content.canInsertNewControl();
    }

    // public setReadOnly(bReadOnly: boolean): void {
    //     this.content.setReadOnly(bReadOnly);
    // }

    public removePortion(): void {
        //
    }

    public copy(parent: DocumentContentBase, option?: any): DocumentContentElementBase {
        const region = new Region(parent, this.logicDocument);
        region.title = this.title;
        region.name = this.name;
        region.bReverseEdit = this.bReverseEdit;
        region.bCanntDelete = this.bCanntDelete;
        region.bCanntEdit = this.bCanntEdit;
        region.bHidden = this.isHidden();
        region.bShowTitle = this.bShowTitle;
        const custom = new Map<string, ICustomProps>();
        region.customProperty = custom;
        this.customProperty.forEach((value, key) => {
            custom.set(key, {...value});
        });
        region.content = new DocumentContent(region, this.getDocumentParent(), 0, 0, 0, 0, false);
        region.content.copy2(this.content, true, option);

        return region;
    }

    public getLastVisibleLine(clientY: number, bEnd: boolean): {x: number, y: number, xLimit: number} {
        const currentY = this.y;
        if (currentY > clientY) {
            return;
        }

        return this.content.getLastVisibleLine(clientY, bEnd);
    }

    public reset(x: number, y: number, xLimit: number, yLimit: number, pageAbs: number): void {
        this.content.reset(x, y, xLimit, yLimit);
        this.content.setStartPage(0);

        this.x            = x;
        this.y            = y;
        this.xLimit       = xLimit;
        this.yLimit       = yLimit;
        this.pageNum      = pageAbs;
    }

    public recalculatePage(curPage: number = 0, bStart: boolean = true): RecalcResultType {
        const parent = this.getParentRegion() ;
        const bHidden = this.isHidden() ? this.isHidden() :
                    ((parent && parent instanceof Region) ? parent.isHidden() : false);
        const recalcResult = this.content.recalculatePage(curPage, bStart,
                                this.bBreakStartElement, this.bResetStartElement, bHidden);

        if ( RecalcResultType.RecalcResult2End === recalcResult ) {
            return RecalcResultType.RecalResultNextElement;
        } else if ( RecalcResultType.RecalcResult2NextPage === recalcResult ) {
            return RecalcResultType.RecalResultNextPage;
        } else if ( RecalcResultType.RecalcResult2CurPage === recalcResult ) {
            return RecalcResultType.RecalResultCurPage;
        }
    }

    public getLineCount(): number {
        return; // this.content.getLineCount();
    }

    public getCursorPosXY(): ICursorProperty {
        return this.content.getCursorPosXY();
    }

    public moveCursorToXY(pageIndex: number, pointX: number, pointY: number,
                          bLine: boolean = false, bDontChangeRealPos: boolean = true): void {
        this.content.moveCursorToXY(pageIndex, pointX, pointY, bLine, bDontChangeRealPos);
    }

    public getSelectedParaPros(paraPro: IParaProperty): boolean {
        return this.content.getSelectedParaPros(paraPro, false);
    }

    public getSelectText(bSelectAll?: boolean, needPara: boolean = false): string {
        return this.content.getSelectText(bSelectAll, needPara);
    }

    public getSelectTextAI(bSelectAll?: boolean, needPara: boolean = false): string {
        return this.content.getSelectTextAI(bSelectAll, needPara);
    }

    public getSelectTextNoHidden(bSelectAll?: boolean): string {
        return this.content.getSelectTextNoHidden(bSelectAll);
    }

    public addInlineImage(width: number, height: number, src: string, name: string,
                          type: EquationType, svgElem?: any,
                          mediaType?: ImageMediaType, mediaSrc?: string, datas?: any): string {
        const res = this.content.addInlineImage(width, height, src, name, type,
                                        svgElem, mediaType, mediaSrc, datas);
        if (res) {
            this.setDirty(true);
        }

        return res;
    }

    /**
     * description: 获取选中字体的文本格式
     */
    public getSelectedTextProperty(textPros: ITextProperty): boolean {
        return this.content.getSelectedTextProperty(false, textPros);
    }

    public getSelectedContent(base: DocumentContentElementBase, names: string[], bKeepHalfStructBorder: boolean
        ,                     option?: any): any {
        base['bHidden'] = this.isHidden();
        return this.content.getSelectedContent(base, names, bKeepHalfStructBorder, option);
    }

    /**
     * 光标左移
     * @param bShiftLey
     */
    public moveCursorLeft(bShiftLey: boolean = false): boolean {
        return this.content.moveCursorLeft(bShiftLey);
    }

    /**
     * 键盘光标右移
     * @param bShiftLey
     */
    public moveCursorRight(bShiftLey: boolean = false): boolean {
        return this.content.moveCursorRight(bShiftLey);
    }

    /**
     * 键盘光标上移
     * @param bShiftLey
     */
    public moveCursorUp(bShiftLey: boolean = false): boolean {
        return this.content.moveCursorUp(bShiftLey);
    }

    /**
     * 键盘光标下移
     * @param bShiftLey
     */
    public moveCursorDown(bShiftLey: boolean = false): boolean {
        return this.content.moveCursorDown(bShiftLey);
    }

    public getSelectionNodePos(pos: ParagraphContentPos, bStart: boolean): void {
        this.content.getSelectionNodePos(pos, bStart, false);
    }

    /**
     * 在当前页面是否是空
     * @param curPage
     */
    public isEmptyPage(curPage: number = 0): boolean {
        if (this.content.content.length === 0) {
            return true;
        }

        return this.content.isEmptyPage(curPage);
    }

    public getPages(): any {
        return this.content.getPages();
    }

    public getLines(): any  {
        return; // this.content.getLines();
    }

    public getLineById(id: number): any {
        return; // this.content.getLineById(id);
    }

    public getContent(): DocumentContentElementBase[] {
        return this.content.content;
    }

    public getCurPos(): any {
        return this.content.getCurPos();
    }

    public getStartPos(pos: ParagraphContentPos): void {
        // pos.add(this.index);
        this.content.getStartPos(pos);
    }

    public getEndPos(bParaEnd: boolean, pos: ParagraphContentPos): void {
        // pos.add(this.index);
        this.content.getEndPos(bParaEnd, pos);
    }

    /**
     * 在当前光标位置开始选择
     */
    public startSelectionByCurPos(): void {
        this.content.startSelectionByCurPos();
    }

    public getCurrentPageByPos(pos: ParagraphContentPos): number {
        return this.content.getCurrentPageByPos(pos);
    }

    public removeSelection(): void {
        this.content.removeSelection();
    }

    /**
     * 选择区域是否为空
     * @param bCheckHidden
     */
    public isSelectionEmpty(bCheckHidden?: boolean): boolean {
        return this.content.isSelectionEmpty(bCheckHidden);
    }

    /**
     * 设置是否选择
     * @param bUse
     */
    public setSelectionUse(bUse: boolean): void {
        this.content.setSelectionUse(bUse);
    }

    /**
     * 设置选择的起始位置
     * @param bSelectStart
     * @param bEnd
     */
    public setSelectionBeginEnd(bSelectStart: boolean, bEnd: boolean): void {
        this.content.setSelectionBeginEnd(bSelectStart, bEnd);
    }

    /**
     * 区域全选
     * @param direction 选择方向
     */
    public selectAll(direction?: number): void {
        this.content.selectAll(direction);
    }

    public setApplyToAll(flag: boolean): void {
        this.content.setApplyToAll(flag);
    }

    /**
     * 是否选中整个区域
     * @param bEnd
     */
    public isSelectedAll(): boolean {
        return this.content.isSelectedAll();
    }

    /**
     * 获取选择区域
     */
    public getSelectionBounds(bStart: boolean, bMultis: boolean): IDrawSelectionBounds {
        return this.content.getSelectionBounds(bStart, bMultis);
    }

    /**
     * 获取当前光标处段落
     */
    public getCurrentParagraph(): DocumentContentElementBase {
        return this.content.getCurrentParagraph();
    }

    public getSelectionDirection(): number {
        return this.content.getSelectionDirection();
    }

    public getFoucsInRegion(pointX: number, pointY: number, elementIndex: number): Region {
        return this.content.getFoucsInRegion(pointX, pointY, elementIndex);
    }

    public getAllRevision(): IRevisionChange[] {
        return this.content.getAllRevision();
    }

    public getFocusRevision(pointX: number, pointY: number, pageIndex: number): IRevisionChange[] {
        return this.content.getFocusRevision(pointX, pointY, pageIndex);
    }

    public isRegionFirstOnNewPage(): boolean {
        if ( null != this.getDocumentPrev()
            || ( this.parent.isTableCellContent() && true !== this.parent.isTableFirstRowOnNewPage())
            || ( this.parent.isRegionContent() && true !== this.parent.isRegionFirstOnNewPage() )) {
            return false;
        }

        return true;
    }

    public isTableFirstRowOnNewPage(curRow: number): boolean {
        return this.parent.isTableFirstRowOnNewPage();
    }

    public getPageBounds(pageIndex: number): DocumentFrameBounds {
        return this.content.getPageBounds(pageIndex);
    }

    public getTopDocument(): DocumentContentBase {
        let parent = this.parent;

        while ( parent ) {
            if ( parent instanceof DocumentContent ) {
                if ( parent.parent instanceof Region ) {
                    parent = parent.parent.parent;
                } else if ( parent.parent instanceof HeaderFooter ) {
                    return parent;
                } else if ( parent.parent instanceof TableCell) {
                    break;
                } else {
                    parent = parent.parent;
                }
            } else {
                break;
            }
        }

        return parent;
    }

    public canAddNewParagraphByEnter(): boolean {
        return this.content.canAddNewParagraphByEnter();
    }

    public addNewParagraphByEnter(): void {
        return this.content.addNewParagraphByEnter();
    }

    /**
     * 判断当前光标，或当前选择区域是否在单元格内
     */
    public isInTableCell(): boolean {
        return this.content.isInTableCell();
    }

    public isInTable(): boolean {
        return this.content.isInTable();
    }

    public getCurrentTable(): any {
        return this.content.getCurrentTable();
    }

    public addTableRow(bBefore: boolean): boolean {
      return this.content.addTableRow(bBefore);
    }

    public removeTableRow(): boolean {
        return this.content.removeTableRow();
    }

    public removeTableColumn(): boolean {
        return this.content.removeTableColumn();
    }

    public isTableBorder(x: number, y: number, pageAbs: number, options?: any): any {
        return this.content.isTableBorder(x, y, pageAbs, options);
    }

    public updateCursorType(x: number, y: number, pageAbs: number): void {
        this.content.updateCursorType(x, y, pageAbs);
    }

    public setContentPos(pos: ParagraphContentPos): boolean {
        return this.content.setContentPos(pos);
    }

    public getMovingTableNewBorder(): IDrawTableNewBorder {
        return this.content.getMovingTableNewBorder();
    }

    public getCurrentRegion(): Region {
        const res = this.content.getCurrentRegion();

        if ( null == res ) {
            return this;
        }

        return res;
    }

    public getStartPosXY(): { x: number, y: number } {
        const pos = {
            x: this.x,
            y: this.y,
        };

        const pages = this.content.pages;
        if ( pages && 0 < pages.length ) {
            for (let index = 0; index < pages.length; index++) {
                if ( !this.isEmptyPage(index) ) {
                    pos.x = pages[index].x;
                    pos.y = pages[index].y;
                    break;
                }
            }
        }

        return pos;
    }

    // public getReviewType(): ReviewType {
    //     return this.reviewType;
    // }

    // public setReviewType(type: ReviewType): void {
    //     if ( type !== this.reviewType) {
    //         const oldReviewType = this.reviewType;
    //         const oldReviewInfo = this.reviewInfo.copy();

    //         this.reviewType = type;
    //         this.reviewInfo.update();

    //         const history = this.logicDocument.getHistory();
    //         history.addChange(new ChangeRegionReviewType(this,
    //             { reviewType: oldReviewType,
    //               reviewInfo: oldReviewInfo},
    //             { reviewType: this.reviewType,
    //               reviewInfo: this.reviewInfo.copy()}));

    //         this.updateTrackRevisions();
    //     }
    // }

    // public getReviewInfo(): ReviewInfo {
    //     return this.reviewInfo;
    // }

    // public setReviewTypeWithInfo(reviewType: ReviewType, reviewInfo: ReviewInfo): void {
    //     const history = this.logicDocument.getHistory();
    //     history.addChange(new ChangeRegionReviewType(this,
    //         { reviewType: this.reviewType,
    //           reviewInfo: this.reviewInfo ? this.reviewInfo.copy() : undefined},
    //         { reviewType,
    //           reviewInfo: reviewInfo ? reviewInfo.copy() : undefined}));

    //     this.reviewInfo = reviewInfo;
    //     this.reviewType = reviewType;

    //     this.updateTrackRevisions();
    // }

    // public updateTrackRevisions(): void {
    //     if ( this.parent && this.parent.getRevisionsManager() && this.parent.isTrackRevisions() ) {
    //         const revisionManager = this.parent.getRevisionsManager();
    //         revisionManager.checkElement(this);
    //     }
    // }

    // public checkRevisionsChanges(): void {
    //     const revisionManager = this.logicDocument.getRevisionsManager();

    //     if ( revisionManager && ReviewType.Common !== this.getReviewType() ) {
    //         const revision = new Revision(revisionManager.getCurrentUserName(), revisionManager.getCurrentUserId());
    //         revisionManager.addRevision(this.id, revision);

    //         if ( ReviewType.Add === this.getReviewType()) {
    //             this.content.content.forEach((item) => {
    //                 item.checkRevisionsChanges();
    //             });
    //         }
    //     }
    // }

    public acceptRevisionChanges(bAll: boolean, type?: RevisionChangeType): void {
        this.content.acceptRevisions(bAll);
    }

    public rejectRevisionChanges(bAll: boolean, type?: RevisionChangeType): void {
        this.content.rejectRevisions(bAll);
    }

    public getElementById(elementId: number): DocumentContentElementBase {
        return this.content.getElementById(elementId);
    }

    public getContentByPos(pos: any): any {
        return this.content.getContentByPos(pos);
    }

    public setTitlePortionTextProperty(textProperty: TextProperty): void {
        if (this.titlePortion != null) {
            const titleTextProp = this.titlePortion.textProperty;
            titleTextProp.fontFamily = textProperty.fontFamily;
            titleTextProp.fontSize = textProperty.fontSize;
            // titleTextProp = textProperty; // this is wrong!
        }
    }

    public setTitlePortionTextProperty2(textProperty: TextProperty): void {
        if (this.titlePortion && textProperty) {
            this.titlePortion.textProperty.setProperty(textProperty);
        }
    }

    public isCursorInRegionTitle(exceptEnd: boolean = true): boolean {
        const portion = this.getCurParaPortion();

        if ( portion && portion.isRegionTitle(exceptEnd) ) {
            return true;
        }

        return false;
    }

    public getHistory(): History {
        return this.logicDocument?.getHistory();
    }

    public getLastRowBorderSize(): number {
        const length = this.content.content.length;
        if (this.content.content[length - 1].isTable()) {
            return this.content.content[length - 1].getLastRowBorderSize();
        }

        return 0;
    }

    public resetRevisions(): void {
        this.content?.resetRevisions();
    }

    public refreshRecalData(data: any): void {
        let pos = 0;
        let bNeedRecalc = false;

        switch (data.type) {
            case HistroyItemType.RegionTitle:
            case HistroyItemType.RegionTitleVisible:
            case HistroyItemType.RegionHidden: {
                bNeedRecalc = true;
                pos = data.pos;
                break;
            }

            case HistroyItemType.RegionName:
            case HistroyItemType.RegionReverseEdit:
            case HistroyItemType.RegionDeleteProtect:
            case HistroyItemType.RegionEditProtect:
            case HistroyItemType.RegionReviewType:  {
                break;
            }

            default:
                break;
        }

        if (bNeedRecalc) {
            const history = this.getHistory();
            history.recalculateDataAdd(data);
            this.refreshRecalData2(pos, 0);
        }
    }

    public refreshRecalData2( index: number, pageIndex: number ): void {
        if (-1 !== index) {
            this.parent?.refreshRecalData2(this.index, pageIndex);
        }
    }

    public getContentText(): string {
        return this.content.getContentText();
    }

    private setText(title: string, paraPortion?: ParaPortion): void {
        const portion = paraPortion || this.titlePortion;
        const content = title.slice(1, title.length);
        const paraText = new ParaTextExtend(title.charAt(0), ParaTextName.Region);
        portion.content = [];
        portion.addToContent(0, paraText);
        portion.addText(content, undefined, 1);
        portion.portionContentPos = title.length;
    }

    private removeTitlePortion(): number {
        const portion = this.titlePortion;
        if (!portion) {
            return ResultType.UnEdited;
        }
        const para = portion.paragraph;
        para.removePortion(0, 1);
        this.titlePortion = null;
        return ResultType.Success;
    }

    /**
     * del删除
     */
    private jumpOrDeleteTheHidden1(): number {
        if (this.bHidden !== true) {
            return;
        }

        const parent = this.parent;
        const contents = this.parent.content;
        let index = this.index;
        const removeIndexs: number[] = [];
        for (const len = contents.length; index < len; index++) {
            const content = contents[index];
            if (content.isHidden() !== true) {
                break;
            }

            if (content instanceof Region && content.bCanntDelete !== true) {
                removeIndexs.unshift(index);
            }
        }
        removeIndexs.forEach((pos) => {
            parent.contentRemove(pos, 1);
        });
        if (removeIndexs.length) {
            this.setDeleteFlag();
        }
        return index - this.index + 1;
    }

    private setDeleteFlag(bDelete: boolean = true): void {
        this.logicDocument.getRegionManager()
            .setDeleteFlag(bDelete);
    }
}

interface IRegionNode {
    name: string;
    leafList: IRegionNode[];
}

interface IRegionInfo {
    depth: number;
    bActive: boolean;
    viewType: number;
}

interface IPasteRegionInfo {
    parent: string;
    region: Region;
}

export class RegionManager {
    private regionNames: IRegionNode[];
    private regionNameMap: Map<string, number[]>;
    private regionMap: Map<string, Region>;
    private activeName: string;
    private doc: Document;
    private bDelete: boolean;
    private pasteRegions: IPasteRegionInfo[];
    private borderViewType: RegionBorderViewType;
    constructor(doc: Document) {
        this.doc = doc;
        this.regionNames = [];
        this.regionMap = new Map<string, Region>();
        this.regionNameMap = new Map<string, number[]>();
        this.pasteRegions = [];
    }

    public clear(): void {
        this.regionNames = [];
        this.regionMap = new Map<string, Region>();
        this.regionNameMap = new Map<string, number[]>();
    }

    public isMaxDepthAtAll(): boolean {
        const nameMap = this.regionNameMap;
        for (const [name, indexs] of nameMap) {
            if (indexs.length === REGION_MAX_DEPTH) {
                return true;
            }
        }
        return false;
    }

    public getRegionMap(): Map<string, Region> {
        return this.regionMap;
    }

    public getRegionNameMap(): Map<string, number[]> {
        return this.regionNameMap;
    }

    public getChilds(region: Region): Region[] {
        const indexs = this.regionNameMap.get(region.getName());
        if (!indexs) {
            return [];
        }
        const node = this.getRegionNode(indexs);
        const childs = node.leafList;
        if (!childs || childs.length === 0) {
            return [];
        }
        const regionMap = this.regionMap;
        const regions = [];
        childs.forEach((child) => {
            regions.push(regionMap.get(child.name));
        });
        return regions;
    }

    public getBorderViewType(): RegionBorderViewType {
        return this.borderViewType;
    }

    public setBorderViewMode(nType: RegionBorderViewType): number {
        if (this.borderViewType === nType) {
            return ResultType.UnEdited;
        }
        this.borderViewType = nType;
        return ResultType.Success;
    }

    // public isValidDelete( startPos: ParagraphContentPos, endPos: ParagraphContentPos): boolean {
    //     const contents = this.doc.content;
    //     let bChange = false;
    //     const start = startPos.get(1);
    //     const end = endPos.get(1);
    //     const bSamePos = start === end;
    //     for (let index = start; index <= end; index++) {
    //         const element = contents[index] as Region;
    //         if (element.getType() === DocumentContentType.Region) {
    //             const flag = element.isRegionValidDelete(bSamePos);
    //             if (flag === false) {
    //                 return false;
    //             }
    //             bChange = true;
    //         }
    //     }
    //     this.bDelete = bChange;
    //     return true;
    // }

    public isValidDepth(name: string, maxDepth?: number, bOnly?: boolean): boolean {
        const indexs = this.regionNameMap.get(name);
        if (!indexs) {
            return true;
        }

        const indexLength = indexs.length;
        if (indexLength >= (maxDepth || REGION_MAX_DEPTH)) {
            return false;
        }

        if (bOnly === true) {
            return true;
        }

        const regionNode = this.getRegionNode(indexs);
        const length = this.getMaxDepth(regionNode.leafList);
        if (length > 0 && length + indexLength >= (maxDepth || REGION_MAX_DEPTH)) {
            return false;
        }

        return true;
    }

    public setActiveName(name: string = null): void {
        this.activeName = name;
    }

    public getRegionIndex(name: string): number {
        const indexs = this.regionNameMap.get(name);
        if (!indexs) {
            return 0;
        }

        return indexs[0];
    }

    public getRegionIndexs(name: string): number[] {
        const indexs = this.regionNameMap.get(name);

        return indexs;
    }

    public getRegionRenderInfo(name: string): IRegionInfo {
        const indexs = this.regionNameMap.get(name);
        if (!indexs) {
            return {
                depth: 1,
                bActive: false,
                viewType: this.borderViewType,
            };
        }

        return {
            depth: indexs.length,
            bActive: name === this.activeName,
            viewType: this.borderViewType,
        };
    }

    public getRegionDepth(name: string): number {
        const indexs = this.regionNameMap.get(name);
        if (!indexs) {
            return 1;
        }

        return indexs.length;
    }

    /**
     * 插入时，检查名称的合法性
     * @param newControlName
     */
    public checkRegionName(newControlName: string, insertRegionNames?: string[]): boolean {
        if (!newControlName ) {
            return false;
        }

        if (newControlName ) {
            if (this.regionMap.has(newControlName)) {
                return false;
            }
            const regions = this.pasteRegions || [];
            const region = regions.find((regionInfo) => regionInfo.region.getName() === newControlName);
            if (region) {
                return false;
            }

            if ( insertRegionNames && 0 < insertRegionNames.length ) {
                return undefined === (insertRegionNames.find((name) => name === newControlName));
            }
        }

        return true;
    }

    public makeUniqueName(name?: string): string {
        if ( null != name ) {
            if (true === this.checkRegionName(name)) {
                return name;
            }
        }

        let newControlName = 'region';
        const regions = this.regionMap;
        const pasteRegions = this.pasteRegions || [];
        for (let number = 1, length = Number.MAX_SAFE_INTEGER; number < length; number++) {
            const temp = newControlName + number;
            if ( false === regions.has(temp) ) {
                const region = pasteRegions.find((regionInfo) => regionInfo.region.getName() === temp);
                if (region) {
                    continue;
                }
                newControlName = temp;
                break;
            }
        }

        return newControlName;
    }

    public getRegionByName(name: string): Region {
        return this.regionMap.get(name);
    }

    public setProperty(property: INewControlProperty, oldName: string): number {
        const region = this.getRegionByName(oldName);
        if (!region) {
            return ResultType.Failure;
        }

        // const history = this.doc ? this.doc.getHistory() : null;
        // if ( history ) {
        //     history.createNewHistoryPoint(HistoryDescriptionType.DocumentSetRegionProperty);
        // }
        this.doc.startAction(HistoryDescriptionType.DocumentSetRegionProperty);

        const res = region.setProperty(property);
        this.updateName(property.newControlName, oldName);

        this.doc.endAction();
        return res === ResultType.Success ? ResultType.Success : ResultType.UnEdited;
    }

    public updateName(newName: string, oldName: string): number {
        if (newName === oldName || !newName) {
            return ResultType.UnEdited;
        }
        // if (this.checkRegionName(newName) === false) {
        //     return ResultType.Failure;
        // }
        const indexs = this.regionNameMap.get(oldName);
        const region = this.regionMap.get(oldName);
        if (!indexs || !region) {
            return ResultType.Failure;
        }

        this.regionNameMap.delete(oldName);
        this.regionNameMap.set(newName, indexs);
        this.updateName2(newName, indexs);
        this.regionMap.delete(oldName);
        this.regionMap.set(newName, region);
        // region.setNewControlName(newName);
        if (this.activeName === oldName) {
            this.activeName = newName;
        }

        return ResultType.Success;
    }

    public checkDeleted(): void {
        if (!this.bDelete) {
            return;
        }
        const regions = this.regionMap;
        for (const [name, region] of regions) {
            const contents = region.parent.content;
            if (contents[region.index] !== region) {
                this.delete(name);
            }
        }
        this.bDelete = true;
    }

    public delete(name: string): number {
        const indexs = this.regionNameMap.get(name);
        if (!indexs) {
            return ResultType.Failure;
        }
        this.deletef2(indexs);

        const history = this.doc.getHistory();
        if ( history ) {
            history.addChange(new ChangeRegionManagerRemoveRegionIndexs(this, name, indexs));
            history.addChange(new ChangeRegionManagerRemoveRegions(this, null, [this.regionMap.get(name)]));
        }
        this.regionMap.delete(name);
        this.regionNameMap.delete(name);
        return ResultType.Success;
    }

    public setDeleteFlag(bDelete: boolean): void {
        this.bDelete = bDelete;
    }

    public addRegion(region: Region, nPos?: number, childNames?: string[]): number {
        const name = region.getName();
        this.regionMap.set(name, region);
        const regionExt: IRegionNode = {leafList: [], name};
        const regionNames = this.regionNames;
        if (nPos === undefined) {
            nPos = this.getIndex(region);
        }

        const childs = [];
        if (childNames && childNames.length > 0) {
            const len = childNames.length;
            const nameMap = this.regionNameMap;
            childNames.reverse()
            .forEach((regionName) => {
                const indexs = nameMap.get(regionName);
                const actRegion = this.getRegionNode(indexs);
                if (actRegion) {
                    childs.push(actRegion);
                    this.deleteByIndexs(indexs);
                }
            });
        }

        const res = this.regionNames.splice(nPos, 0, regionExt);

        if (childs.length > 0) {
            regionExt.leafList = childs;
        } else {
            regionExt.leafList = res;
        }

        for (let index = nPos, len = regionNames.length; index < len; index++) {
            this.setDepth(regionNames[index], [index]);
        }

        const history = this.doc.getHistory();
        if ( history ) {
            history.addChange(new ChangeRegionManagerAddRegionIndexs(this, name, this.regionNameMap.get(name)));
            history.addChange(new ChangeRegionManagerAddRegions(this, null, [this.regionMap.get(name)]));
        }

        return ResultType.Success;
    }

    public addPasteRegion(region: Region, parentRegion?: Region): void {
        this.regionMap.set(region.getName(), region);
        let parent: string;
        if (parentRegion !== undefined) {
            parent = parentRegion.getName();
        }
        this.pasteRegions.push({parent, region});
    }

    public getAllPasteRegion(): IPasteRegionInfo[] {
        return this.pasteRegions;
    }

    public insertAllPasteRegion(para: Paragraph): void {
        const regions = this.pasteRegions;
        if (!regions.length) {
            return;
        }

        const parent = para.getRegion();
        const nPos = this.getIndex(regions[0].region);
        const parents = [];
        for (let curIndex = regions.length - 1; curIndex >= 0; curIndex--) {
            const item = regions[curIndex];
            // item.region.setLogicDocument(this.doc);
            // item.region.getOperateContent().setLogicDocument(this.doc);

            if (item.parent === undefined) {
                parents.push(item);
                regions.splice(curIndex, 1);
            }
            if ( !this.regionMap.has(item.region.getName()) ) {
                this.regionMap.set(item.region.getName(), item.region);
            }
        }

        let parentName: string;
        if (parent) {
            parentName = parent.getName();
        }

        for (let curIndex = 0, len = parents.length; curIndex < len; curIndex++) {
            const index = nPos + curIndex;
            const region = parents[curIndex].region;
            if (parentName) {
                this.addLeaf(parentName, region, index);
            } else {
                this.addRegion(region, index);
            }

            this.insertLeafs(regions, region.getName());
        }
        this.pasteRegions = [];
    }

    public resetPaste(): void {
        const pasteRegions = this.pasteRegions;
        if (!pasteRegions.length) {
            return;
        }
        const regionMap = this.regionMap;
        pasteRegions.forEach((item) => {
            regionMap.delete(item.region.getName());
        });
        this.pasteRegions = [];
    }

    public insertRegionCaches(para: Paragraph): void {
        const regions = this.pasteRegions;
        if (!regions || 0 === regions.length) {
            return;
        }

        const parent = para.getRegion();
        if ( parent ) {
            this.insertLeafs(regions, parent.getName());
        }
        this.insertAllPasteRegion(para);
    }

    public addInsertRegion(region: Region, parentRegion?: Region): void {
        let parent: string;
        if (parentRegion !== undefined) {
            parent = parentRegion.getName();
        }
        this.pasteRegions.push({parent, region});
    }

    public resetInsertRegionCaches(): void {
        this.pasteRegions = [];
    }

    public getAllNames(): string[] {
        const regions = this.regionMap;
        const names = [];
        for (const [name, region] of regions) {
            names.push(name);
        }
        return names;
    }

    public getRegionNames(): IRegionNode[] {
        return this.regionNames;
    }

    /**
     * 获取当前选中的区域，包括子区域
     * @param region 当前区域
     * @param type 1：选区前，2：选区中，3：选区终，4：前=终
     * @returns 区域名称
     */
    public getSelectedNames(region: Region, type: number): string[] {
        const children = this.getChilds(region);
        if (type === 2 || region.isSelectedAll()) {
            const names = children.map((child) => child.getName());
            names.unshift(region.getName());
            return names;
        }

        const childNames = [];
        let bSelected = false;
        if (type === 1) {
            children.forEach((child) => {
                if (bSelected) {
                    childNames.push(child.getName());
                    return;
                }
                if (child.isSelectedAll()) {
                    bSelected = true;
                    childNames.push(child.getName());
                }
            });
        } else if (type === 3) {
            for (let index = children.length - 1; index >= 0; index--) {
                const child = children[index];
                if (bSelected) {
                    childNames.unshift(child.getName());
                    continue;
                }
                if (child.isSelectedAll()) {
                    bSelected = true;
                    childNames.unshift(child.getName());
                }
            }
        } else {
            children.forEach((child) => {
                if (child.isSelectedAll()) {
                    childNames.push(child.getName());
                }
            });
        }

        return childNames;
    }

    public getFirstLevelNames(): string[] {
        const regions = this.regionNames;
        // const len = regions.length;
        // const result = [];
        // for (let i = len - 1; i >= 0; i--) {
        //     result.push(regions[i].name);
        // }
        // return result;

        // return regions.map((region) => region.name);
       // return regions.map((region, index) => {
       //     return regions[regions.length - 1 - index].name;
        //});
        return regions.map(region => region.name);
    }

    public getFirstLevelRegionsByDirty(bDirty: boolean): Region[] {
        const regions = this.regionNames;
        const regionMap = this.regionMap;
        const res = [];
        regions.forEach((regionNode) => {
            const region = regionMap.get(regionNode.name);
            if (region.isDirty() === bDirty) {
                res.push(region);
            }
        });

        return res;
    }

    public getAllRegionsByDirty(bDirty: boolean): Region[] {
        const regionMap = this.regionMap;
        const res = [];
        for (const [name, region] of regionMap) {
            if (region.isDirty() === bDirty) {
                res.push(region);
            }
        }

        return res;
    }

    //此函数需要先设置子区域的脏标记，然后再设置父区域的脏标记
    public setAllRegionsDirty(bDirty: boolean = false): number {
        const regionMap = this.regionMap;
        if (regionMap.size === 0) {
            return ResultType.UnEdited;
        }
    
        // 获取所有区域名称
        const allRegionNames = this.getAllNames();
        // 获取第一层区域名称
        const firstLevelNames = this.getFirstLevelNames();
        
        // 找出子区域名称（所有区域减去第一层区域）
        const childRegionNames = allRegionNames.filter(name => !firstLevelNames.includes(name));
        
        let res = ResultType.UnEdited;
        
        // 先设置子区域的脏标记
        for (const name of childRegionNames) {
            const region = regionMap.get(name);
            if (region) {
                res = region.setDirty(bDirty) && res;
            }
        }
        
        // 再设置第一层区域的脏标记
        for (const name of firstLevelNames) {
            const region = regionMap.get(name);
            if (region) {
                res = region.setDirty(bDirty) && res;
            }
        }
    
        return res;
    }

    public setReadOnly(name: string, flag: boolean, bLef: boolean): number {
        const indexs = this.regionNameMap.get(name);
        if (!indexs) {
            return ResultType.Failure;
        }

        if (indexs.length !== 1) {
            return ResultType.Failure;
        }
        const curIndex = indexs[0];
        const regionMap = this.regionMap;
        let region = regionMap.get(name);
        region.setEditProtect(flag);
        const regionNames = this.regionNames;
        for (let index = 0, len = regionNames.length; index < len; index++) {
            if (curIndex === index) {
                continue;
            }
            const regionNode = regionNames[index];
            region = regionMap.get(regionNode.name);
            region.setEditProtect(bLef);
        }
        return ResultType.Success;
    }

    public filterRegionByProp(region: Region, prop: string, value: any): Region[] {
        const indexs = this.regionNameMap.get(region.getName());
        if (!indexs) {
            return [];
        }
        const regionNode = this.getRegionNode(indexs);
        const leafList = regionNode.leafList;
        if (!leafList) {
            return [];
        }

        const regionMap = this.regionMap;
        const regions: Region[] = [];
        leafList.forEach((leaf) => {
            const actRegion = regionMap.get(leaf.name);
            const result = this.filterByProp(actRegion, prop, value);
            if ( result === true) {
                regions.push(actRegion);
            }
        });

        return regions;
    }

    /**
     * 仅删除当前区域，不删除子类
     * @param region 区域对象
     */
    public deleteRegion(region: Region): void {
        const name = region.getName();
        let indexs = this.regionNameMap.get(region.getName());
        if (!indexs) {
            return;
        }
        indexs = indexs.slice();
        const delRegion = this.getRegionNode(indexs);
        const nPos = indexs.pop();
        const leafList = delRegion.leafList;
        const regionNames = this.regionNames;
        if (leafList && leafList.length > 0) {
            regionNames.splice(nPos, 1, ...leafList);
        } else {
            regionNames.splice(nPos, 1);
        }

        const history = this.doc.getHistory();
        if ( history ) {
            history.addChange(new ChangeRegionManagerRemoveRegionIndexs(this, name, indexs));
            history.addChange(new ChangeRegionManagerRemoveRegions(this, null, [this.regionMap.get(name)]));
        }

        this.regionMap.delete(name);
        this.regionNameMap.delete(name);

        for (let index = nPos, len = regionNames.length; index < len; index++) {
            const parentIndexs = indexs.slice();
            parentIndexs.push(index);
            this.setDepth(regionNames[index], parentIndexs);
        }
    }

    public triggerCascade(map: Map<string, ICascade[]>): number {
        if (!map || map.size === 0) {
            return ResultType.UnEdited;
        }

        const names = Array.from(map.keys());
        const regions: Region[] = [];
        this.getSortRegion(this.regionNames, regions, names);
        if (regions.length === 0) {
            return ResultType.UnEdited;
        }

        let result: number = ResultType.UnEdited;
        regions.forEach((region) => {
            const name = region.getName();
            const objs = map.get(name);
            const obj = {
                text: false,
                hide: false,
            };
            objs.forEach((item: any) => {
                result = this.trigger(item, item.value, region, false, obj) && result;
            });
        });

        if (result === ResultType.Success) {
            this.doc.recalculate();
        }

        return result;
    }

    public getSortRegion(datas: IRegionNode[], regions: Region[], names: string[]): void {
        if (names.length === 0) {
            return;
        }
        const regionMap = this.regionMap;
        for (let index = 0, len = datas.length; index < len; index++) {
            const data = datas[index];
            const name = data.name;
            const curIndex = names.findIndex((item) => item === name);
            if (curIndex > -1) {
                names.splice(curIndex, 1);
                regions.unshift(regionMap.get(name));
                if (names.length === 0) {
                    return;
                }
            }
            const leafs = data.leafList;
            if (leafs && leafs.length > 0) {
                this.getSortRegion(leafs, regions, names);
            }

            if (names.length === 0) {
                return;
            }
        }
    }

    public getRegionIndexByName(name: string): number {
        const indexs = this.regionNameMap.get(name);
        if (!indexs) {
            return 0;
        }

        return indexs[indexs.length - 1];
    }

    public addLeaf(parentName: string, region: Region, nPos?: number): number {
        const parentIndexs = this.regionNameMap.get(parentName);
        if (!parentIndexs) {
            return ResultType.Failure;
        }

        const name = region.getName();
        this.regionMap.set(name, region);
        const parentNode = this.getRegionNode(parentIndexs);
        if (nPos === undefined) {
            nPos = parentNode.leafList.length;
        }
        const actRegion = {name, leafList: []};
        const regionNames = parentNode.leafList;
        regionNames.splice(nPos, 0, actRegion);

        for (let index = nPos, len = regionNames.length; index < len; index++) {
            const indexs = parentIndexs.slice();
            indexs.push(index);
            this.setDepth(regionNames[index], indexs);
        }

        const history = this.doc.getHistory();
        if ( history ) {
            history.addChange(new ChangeRegionManagerAddRegionIndexs(this, name, this.regionNameMap.get(name)));
            history.addChange(new ChangeRegionManagerAddRegions(this, null, [region]));
        }
    }

    /**
     * 添加子区域
     * @param parentName 父区域名称
     * @param region 当前区域
     * @param pos 当前区域插入的位置（当包含子区域时这个位置可不用）
     * @param childNames 包含的子区域
     */
    public addLeaf2(region: Region, info: IRegionParam): number {
        if (!info.parent) {
            return ResultType.UnEdited;
        }
        const parentName = info.parent.getName();
        const nameMap = this.regionNameMap;
        const parentIndexs = nameMap.get(parentName);
        if (!parentIndexs) {
            return ResultType.Failure;
        }

        const name = region.getName();
        this.regionMap.set(name, region);
        const parentNode = this.getRegionNode(parentIndexs);
        let startPos: number;
        let endPos: number;
        const childNames = info.childNames;
        if (childNames === undefined || childNames.length === 0) {
            endPos = startPos = this.getIndex(region);
            // endPos = startPos;
        } else {
            const len = childNames.length;
            const startRegion = nameMap.get(childNames[0]);
            startPos = startRegion[startRegion.length - 1];
            if (len === 1) {
                endPos = startPos + 1;
            } else {
                const endRegion = nameMap.get(childNames[childNames.length - 1]);
                endPos = endRegion[endRegion.length - 1] + 1;
            }
        }
        const actRegion = {name, leafList: []};
        const childs = parentNode.leafList.splice(startPos, endPos - startPos, actRegion);
        if (childs.length > 0) {
            actRegion.leafList = childs;
        }
        const regionNames = parentNode.leafList;

        for (let index = startPos, len = regionNames.length; index < len; index++) {
            const indexs = parentIndexs.slice();
            indexs.push(index);
            this.setDepth(regionNames[index], indexs);
        }

        const history = this.doc.getHistory();
        if ( history ) {
            const pos = this.regionNameMap.get(name);
            history.addChange(new ChangeRegionManagerAddRegionIndexs(this, name, pos));
            history.addChange(new ChangeRegionManagerAddRegions(this, null, [region]));
        }
    }

    public getNameMap(): Map<string, number[]> {
        return this.regionNameMap;
    }

    public updateRegionNode(regionName: string, pos: number[], bDelete: boolean): void {
        if ( this.regionMap.has(regionName) && pos ) {
            if ( 1 === pos.length || 1 < pos.length ) {
                if ( 1 === pos.length ) {
                    if ( true === bDelete ) {
                        this.regionNames.splice(pos[0], 1);
                    } else {
                        this.regionNames.splice(pos[0], 0, {name: regionName, leafList: []});
                    }
                } else {
                    const parentNode = this.regionNames[pos[0]];
                    if ( parentNode && parentNode.leafList ) {
                        if ( true === bDelete ) {
                            parentNode.leafList.splice(pos[1], 1);
                        } else {
                            parentNode.leafList.splice(pos[1], 0, {name: regionName, leafList: []});
                        }
                    }
                }

                for (let index = pos[0], len = this.regionNames.length; index < len; index++) {
                    this.setDepth(this.regionNames[index], [index]);
                }
            }
        }
    }

    private deleteByIndexs(indexs: number[]): void {
        const arrs = indexs.slice();
        const pos = arrs.pop();
        let parents: IRegionNode[];
        if (arrs.length === 0) {
            parents = this.regionNames;
        } else {
            const parent = this.getRegionNode(arrs);
            parents = parent.leafList;
        }
        parents.splice(pos, 1);
    }

    private getIndex(region: Region): number {
        if (this.regionNameMap.size === 0) {
            return 0;
        }
        const prev = region.getDocumentPrev();
        if (!prev) {
            return 0;
        }
        const contents = region.parent.content;
        for (let index = prev.index; index >= 0; index--) {
            const content = contents[index];
            if (content.getType() === DocumentContentType.Region) {
                const indexs = this.regionNameMap.get(content.getName());
                if (indexs) {
                    return indexs[indexs.length - 1] + 1;
                }
            }
        }
        return 0;
    }

    private filterByProp(region: Region, prop: string, value: any): boolean {
        switch (prop) {
            case NewControlPropName.ReverseEdit: {
                if (value === region.isReverseEdit()) {
                    return true;
                }
                break;
            }
            case NewControlPropName.EditProtect: {
                if (value === region.isEditProtect()) {
                    return true;
                }
                break;
            }
            case NewControlPropName.DeleteProtect: {
                if (value === region.isDeleteProtect()) {
                    return true;
                }
                break;
            }
            case NewControlPropName.Hidden: {
                if (value === region.isSourceHide()) {
                    return true;
                }
                break;
            }
            case NewControlPropName.Title: {
                if (value === region.getTitle()) {
                    return true;
                }
                break;
            }
            default: {
                const targetVal = region.getCustomByPropName(prop);
                if (targetVal === value) {
                    return true;
                }
            }
        }

        return false;
    }

    private getRegionNode(indexs: number[]): IRegionNode {
        if (indexs.length === 0) {
            return;
        }
        let index = 0;
        let pos = indexs[index++];
        let region = this.regionNames[pos];
        pos = indexs[index++];
        while (pos !== undefined) {
            const current = region.leafList[pos];
            if (!current) {
                break;
            }
            region = current;
            pos = indexs[index++];
        }

        return region;
    }

    private deletef2(arrs: number[]): void {
        const indexs = arrs.slice();
        const start = indexs.pop(); // 当前元素的索引
        let regions: IRegionNode[];
        if (indexs.length) {
            const parentRegion = this.getRegionNode(indexs); // 父元素
            regions = parentRegion.leafList; // 父元素的子节点
        } else {
            regions = this.regionNames;
        }

        const region = regions[start];
        this.delete3(region.leafList);
        // 更新后面的位置名称等
        regions.splice(start, 1);
        for (let curIndex = start, len = regions.length; curIndex < len; curIndex++) {
            const curRegion = regions[curIndex];
            const curIndexs = indexs.slice();
            curIndexs.push(curIndex);
            this.setDepth(curRegion, curIndexs);
        }
    }

    private delete3(leafs: IRegionNode[]): void {
        if (leafs.length === 0) {
            return;
        }
        const nameMap = this.regionNameMap;
        const regions = this.regionMap;

        const history = this.doc.getHistory();

        for (let index = leafs.length - 1; index >= 0; index--) {
            const leaf = leafs[index];
            const name = leaf.name;

            if ( history ) {
                history.addChange(new ChangeRegionManagerRemoveRegionIndexs(this, name, nameMap.get(name)));
                history.addChange(new ChangeRegionManagerRemoveRegions(this, null, [this.regionMap.get(name)]));
            }

            nameMap.delete(name);
            regions.delete(name);
            this.delete3(leaf.leafList);
        }
    }

    private updateName2(newName: string, indexs: number[]): void {
        const region: IRegionNode = this.getRegionNode(indexs);
        if (region) {
            region.name = newName;
        }
    }

    private setDepth(region: IRegionNode, indexs: number[]): void {
        this.setDepth2(region.leafList, indexs);
        this.regionNameMap.set(region.name, indexs);
    }

    private setDepth2(leafs: IRegionNode[], indexs: number[]): void {
        if (!leafs.length) {
            return;
        }

        const nameMap = this.regionNameMap;
        for (let index = 0, len = leafs.length; index < len; index++) {
            const arrs = indexs.slice();
            arrs.push(index);
            const leaf = leafs[index];
            this.setDepth2(leaf.leafList, arrs);
            nameMap.set(leaf.name, arrs);
        }
    }

    private getMaxDepth(leafList: IRegionNode[]): number {
        if (!leafList.length) {
            return 0;
        }
        let maxDepth = 0;
        leafList.forEach((leaf) => {
            const depth = this.getMaxDepth(leaf.leafList);
            if (depth > maxDepth) {
                maxDepth = depth;
            }
        });
        return maxDepth + 1;
    }

    private insertLeafs(regions: IPasteRegionInfo[], parentName: string): void {
        if (!regions.length) {
            return;
        }

        const arrs = [];
        for (let index = regions.length - 1; index >= 0; index--) {
            const item = regions[index];
            if (item.parent === parentName) {
                arrs.push(item);
                regions.splice(index, 1);
            }
        }

        if (arrs.length === 0) {
            return;
        }

        for (let index = 0; index < arrs.length; index++) {
            const region = arrs[index].region;
            this.addLeaf(parentName, region, index);
            this.insertLeafs(regions, region.getName());
        }
    }

    private trigger(data: ICascade, value: string, node: any, bRefresh: boolean, obj: any): number {
        let result: number = ResultType.UnEdited;
        switch (data.action) {
            case CascadeActionType.Show: {
                if (obj.hide === true) {
                    return ResultType.UnEdited;
                }
                obj.hide = true;
                result = node.setHidden(false);
                break;
            }
            case CascadeActionType.Hidden: {
                if (obj.hide === true) {
                    return ResultType.UnEdited;
                }
                obj.hide = true;
                result = node.setHidden(true);
                break;
            }
            case CascadeActionType.SyncText: {
                if (obj.text === true) {
                    return ResultType.UnEdited;
                }
                obj.text = true;
                result = node.setRegionText(value, bRefresh);
                break;
            }
            case CascadeActionType.SetText: {
                if (obj.text === true) {
                    return ResultType.UnEdited;
                }
                obj.text = true;
                result = node.setRegionText(data.actionText, bRefresh);
                break;
            }
        }

        return result;
    }

}

export interface IRegionParam {
    parent?: Region; // 当前区域的父区域
    pos?: number; // 当为子元素时，处在的位置
    childNames?: string[]; // 当包含子区域时，这些区域的名称
    newControlNames?: string[]; // 包含的结构化元素名称（改变父子结构需要）
    indexs?: number[]; // 当前区域包含的内容
    errorStr?: string; // 错误返回类型
}
