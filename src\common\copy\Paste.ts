import {VNode} from './ParseNode';
import {PasteContent, CopyContent} from './Content';
import {PasteType, ContentType, embedData} from './DataType';
import Paragraph from '../../model/core/Paragraph';
import {PasteParagraph} from './PasteParagraph';
import { decodeTag } from './DataConver';

export default class CopyParse {
    private accessReg: RegExp = embedData.Reg; // 权限控制id正则
    // private accessDelete = false; // 是否该删除新增进去的权限标识符
    private type: number;
    private content: [];
    private copyContent: CopyContent[];
    private isOwnerEditor: boolean;
    private id: string;
    private doc: any;
    private bInnerFormat: boolean;
    private bOuterFormat: boolean;
    private bFormat: boolean;
    constructor() {
        this.copyContent = [];
        this.isOwnerEditor = false;
    }
    /**
     * 方法说明：数据格式转化
     * @param {Array [object]} contents - 粘贴字符串(html、txt、word等文本)
     * @param {number} type - 数据类型
     * @param {boolean} isSimple - 是否带格式，true: 简单类型，不戴格式
     */
    public parse = async (contents: PasteContent[], type: PasteType): Promise<Paragraph []> => {
        // console.log(contents)
        let res: VNode;
        const pasteParagraph = new PasteParagraph(this.doc);
        this.bFormat = false;
        switch (type) {
            case PasteType.PasteClickCtrlShiftV:
            case PasteType.PasteCtrlShiftV:
                const data = contents.find((item) => item.type === ContentType.TextPlain)
                    || contents.find((item) => item.type === ContentType.TextHtml);
                res = this.unFormatText(data && data.content);
                break;
                // res = new HtmlParse().parse(content);
                // console.log(res)
                // break;
            case PasteType.PasteClickCtrlV:
                // 已经在外面进行拦截
                // let text;
                // 通过无格式字符比较判断，判断出是否来自本编辑器copy而来
                // if (this.isOwnerEditor) {
                //     res = pasteParagraph.copyContentParseVnode(this.copyContent);
                //     break;
                // }
            case PasteType.PasteCtrlV:
                // (type === PasteType.pasteCtrlV) && (this.copyContent = []);
                if (this.bOuterFormat !== false) {
                    res = await this.formatText(contents);
                } else {
                    res = await this.formatText2(contents);
                }
                 // this.formatText(contents);
                break;
        }

        if (!res) {
            return;
        }

        return pasteParagraph.createNode(res, this.bFormat);
    }

    public setFormatFlag(bInnerFormat: boolean, bOuterFormat: boolean): void {
        this.bInnerFormat = bInnerFormat;
        this.bOuterFormat = bOuterFormat;
    }

    public setOwnerFlag(flag: boolean): void {
        this.isOwnerEditor = flag;
    }

    public initCopyContent(contents: CopyContent[]): void {
        this.copyContent = contents;
    }

    public setId(id: string): void {
        this.id = id;
    }

    public setDocument(doc: any): void {
        this.doc = doc;
    }

    // copy = ({documentCore, selectIndexs, lines, position}, type?: CopyType): string => {
    //     let copy  = new copyOperate(documentCore, selectIndexs, lines, position)
    //     let html = copy.init() // 如果复制的内容来自编辑器内部，对他进行html保存
    //     this.copyContent = copy.copyContent.slice();
    //     return html;
    // }

    public getUnFormatContent = (content?: string): string => {
        let text = content === undefined ? this.copyContent.map((data) => data.content)
            .join('') : content;
        if (!text) {
            text = text.trim();
            if (!text) {
                return;
            }
        }
        return document.createTextNode(text).textContent
            .replace(/\s+/g, ' ');
    }


    private imageNode(data: PasteContent): Promise<VNode> {

        if (!data.image) {
            return;
        }

        const vnode = new VNode();
        vnode.type = 'root';
        vnode.tagName = 'root';
        const child = new VNode();
        child.type = 'element';
        child.tagName = 'img';

        // child.attrs = {
        //     src,
        // };
        // child.content = text;
        vnode.children = [child];
        // tslint:disable-next-line: no-unused-expression
        return new Promise((resolve, reject) => {
            
            const reads = new FileReader();
            reads.readAsDataURL(data.image);
            reads.onload = async (theFile) => {
                // window['uploadId'].src = this.result;
                const image = new Image();
                image.src = theFile.target.result as any;
                const size = await (new Promise((resolve)=>{
                    image.onload = function() {
                        resolve({w: (this as any).width, h: (this as any).height});
                    }}));
                child.attrs = {
                    // src,
                    href: reads.result,
                    width: (size as any)?.w || 50,
                    height: (size as any)?.h || 50,
                    type: 10,
                };
                resolve(vnode);

            };
        });
    }

    /**
     * 2021-6-30需求更改：只解释成两种格式，Apollo和纯文本
     * @param content 需要解释的内容
     * @returns VNode对象
     */
    private async formatText2(content: PasteContent[]): Promise<VNode>  {
        if (content.length === 0) {
            return;
        }
        const customer = content.find((item) => item.type === ContentType.TextApollo);
        let defaultObj;
        let res: VNode;
        // apollo
        if (customer) {
            // tslint:disable-next-line: no-shadowed-variable
            defaultObj = await import('./ParseApollo');
            const apolloParse = defaultObj.default;
            res = new apolloParse().parse(customer.content);
            this.bFormat = this.bInnerFormat;
            return res;
        }
        this.bFormat = false;
        let data: any = content.find((item) => item.type === ContentType.TextPlain); // 纯文本格式
        if (data) {
            return this.unFormatText(data.content);
        }

        // html
        data = content.find((item) => item.type === ContentType.TextHtml);
        if (data) {
            const div = document.createElement('div');
            div.innerHTML = data.content || '';
            return this.unFormatText(div.innerText);
        }

        // rtf
        const rtfData = content.find((item) => item.type === ContentType.TextRtf);
        // tslint:disable-next-line: prefer-conditional-expression
        if (rtfData && rtfData.content) {
            defaultObj = await import('./ParseRtf');
            const rtfParse = defaultObj.default;
            const rtfContent = await new rtfParse().parse(rtfData.content);
            // tslint:disable-next-line: no-return-await
            return this.removeAttributes(rtfContent);
        }
    }

    private removeAttributes(node: VNode): any {
        node.attrs = null;
        const children = node.children;
        if (children.length) {
            children.forEach((item) => {
                this.removeAttributes(item);
            });
        }
    }

    private async formatText(content: PasteContent[]): Promise<VNode>  {
        const customer = content.find((item) => item.type === ContentType.TextApollo);
        let defaultObj;
        let rtfData;
        let res: VNode;
        if (customer) {
            // tslint:disable-next-line: no-shadowed-variable
            defaultObj = await import('./ParseApollo');
            const apolloParse = defaultObj.default;
            res = new apolloParse().parse(customer.content);
            this.bFormat = this.bInnerFormat;
            return res;
        }
        this.bFormat = true;
        let data = content.find((item) => item.type === ContentType.TextHtml);
        if (!data || (!data.content && !data.image)) { // 不存在html格式时
            data = content.find((item) => item.type === ContentType.TextPlain); // 纯文本格式
            if (data) {
                this.bFormat = false;
                return this.unFormatText(data.content);
            }

            // 如果rtf存在，那么会先使用rtf转换器进行解码
            rtfData = content.find((item) => item.type === ContentType.TextRtf);
            if (rtfData && rtfData.content) {
                defaultObj = await import('./ParseRtf');
                const rtfParse = defaultObj.default;
                // tslint:disable-next-line: no-return-await
                return await new rtfParse().parse(rtfData.content);
            }

            // 只从word等文档中只复制了图片
            data = content.find((item) => item.type === ContentType.FileImage);
            if (data) {
                // tslint:disable-next-line: no-return-await
                return await this.imageNode(data);
            }
        }

        rtfData = content.find((item) => item.type === ContentType.TextRtf);

        // tslint:disable-next-line: prefer-conditional-expression
        if (rtfData && rtfData.content) {
            defaultObj = await import('./ParseWord');
        } else {
            defaultObj = await import('./ParseHtml');
        }
        const htmlParse = defaultObj.default;
        const html = new htmlParse();
        res = html.parse(data.content, this.accessReg);
        // 假如也有rtf，并且html存在图片，那么直接从rtf中取图片
        if (rtfData && rtfData.content) {
            html.transImage(rtfData.content);

        } else if (html.images?.length) {
            await this.imageContent(html.images);

        }

        return res;
    }

    private unFormatText(text: string): VNode {
        if (!text) {
            return;
        }
        this.bFormat = false;
        const children = [];
        const arrs = text.split(/\r|\n/);
        arrs.forEach((str) => {
            const child = new VNode();
            child.type = 'element';
            child.tagName = 'p';
            child.isBlock = true;
            const textChild = new VNode();
            textChild.type = 'text';
            textChild.tagName = 'text';
            textChild.content = decodeTag(str.trim());
            child.children = [textChild];
            children.push(child);
        });
        const vnode = new VNode();
        vnode.type = 'root';
        vnode.tagName = 'root';
        vnode.children = children;

        return vnode;
    }


    private async imageContent(datas: any[]): Promise<void> {
        for (let index = datas.length - 1; index >= 0; index--) {
            const data = datas[index];
            if (data.tagName !== 'img' || !(data.attrs as any)?.src) {
                continue;
            } else if ((data.attrs as any)?.src.startsWith('file://')) {
                data.tagName = 'delete';
                continue;
            }

            try {
                const res = await fetch((data.attrs as any)['data-src'] || (data.attrs as any)?.src, {
                    method: 'get',
                });
                if (200 === res.status) {
                    console.log(res)
                    const imgBlob = await res.blob();
                    await this.handleImageBlob(data, imgBlob);
                } else {
                    data.tagName = 'delete';
                }
            } catch (error) {
                ;
            }
        }
    }

    private handleImageBlob(data: VNode, blob): Promise<any>  {
        // const that = this;
        return new Promise((resolve, reject) => {
            
            const reads = new FileReader();
            reads.readAsDataURL(blob);
            reads.onloadend = async (theFile) => {
                // window['uploadId'].src = this.result;
                const image = new Image();
                image.src = theFile.target.result as any;
                const size = await (new Promise((resolve)=>{
                    image.onload = function() {
                        resolve({w: (this as any).width, h: (this as any).height});
                    };
                    image.onerror = async function (e) {
                        console.log(e);
                    }
                }));
                data.attrs = {
                    // src,
                    href: reads.result,
                    width: (size as any)?.w || 50,
                    height: (size as any)?.h || 50,
                    type: 10,
                };
                resolve(null);
            };
        }).catch(e => {
            console.log(e)
        });
    }
}
