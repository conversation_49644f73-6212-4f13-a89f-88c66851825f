import * as React from 'react';
import { FONT_MAPPING } from '../../common/commonDefines';
import { Reader } from '../../format/reader/reader';
import { DocumentCore } from 'src/model/DocumentCore';
import {GlobalEvent as gEvent} from '../../common/GlobalEvent';
import { Sketch } from '@uiw/react-color';
import {fonts} from './text/font';


interface IPageToolbarProps {
  pageProperty?: any;
  handleClickOutsideDropdown?: any;
  clickOutsideDropdown?: boolean;
  spacingConfig?: any;
  colorConfig?: any;
	changeParagraphAlignment?: Function;
	changeTextFontFamily?: Function;
  changeTextFontSize?: Function;
  changeTextBold?: Function;
  refresh: Function;
  host?: any;
  documentCore?: DocumentCore;
  testDocumentXml?: any;
}

interface IPageToolbarState {
  isActive: boolean
}


export default class PageToolbar extends React.Component<IPageToolbarProps, IPageToolbarState> {
  private isToolbarEvent: boolean;
  private toolbarEventIndex: number;
  private textColor: string;
  private color: string;
  private backgroundColor: string;
  private activeColorIndex: number;
  private _docId: number;
  constructor(props) {
    super(props);
    this.state = {
      isActive: false
    }
    this._docId = props.host.docId;
    this.color = this.textColor = this.backgroundColor = '#000';
    this.toolbarEventIndex = 1;
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    // console.log("in UNSAFE_componentWillReceiveProps");
    
  }

  componentDidMount() {
    gEvent.addEvent(this._docId, 'editorMouseUpStopUnSelection', this.mouseUp);
    gEvent.addEvent(this._docId, 'editorStopUnSelection', this.mousedown);

  }

  componentDidUpdate() {
  }

  mouseUp = (option) => {
    if (this.isToolbarEvent === true) {
      option.isStop = true;
    }
  }

  mousedown = (opiton, e) => {
    if (this.isToolbarEvent === true) {
      if (this.toolbarEventIndex-- < 1) {
        setTimeout(() => {
          this.setClassActive(false);
        }, 0);
      } else {
        opiton.isStop = true;
      }
    }
  }

  setClassActive(flag) {
    this.isToolbarEvent = flag;
    this.setState({isActive: flag});
    this.toolbarEventIndex = 1;
  }

  setMouseDownStop() {
    this.isToolbarEvent = true;
    this.toolbarEventIndex = 2;
    setTimeout(() => {
      this.isToolbarEvent = false;
    }, 0);
  }

  handlerChangeColor(color: {hex: string, rgb: any}) {
    // 确保color.hex存在，如果不存在则使用默认黑色
    const hexColor = color?.hex || '#000000';
    
    this.color = hexColor;
    this.setClassActive(false);
    if (this.activeColorIndex === 1) {
      this.setTextColor(this.color);
    } else {
      this.setBackgroundColor(this.color);
    }
    this.props.refresh(0);
  }

  /**
   * 工具栏点击: 段落对齐方式
   */
  handlerClick(e){
    let target = e.currentTarget;
    //console.dir(e.target.className.baseVal)
    // e.nativeEvent.stopImmediatePropagation();
    switch (target.classList[0]) {
        case "alignleft_icon":
          this.props.changeParagraphAlignment(0);
	        this.props.refresh();
          break;
          
        case "alignright_icon":
	        this.props.changeParagraphAlignment(2);
	        this.props.refresh();
          break;
          
        case "justify_icon":
	        this.props.changeParagraphAlignment(3);
	        this.props.refresh();
          break;
          
        case "bold_icon":
          this.props.changeTextBold();
          this.props.refresh(0);
          this.setMouseDownStop();
          break;

        case "italic_icon":
          this.props.documentCore.setTextItalic();
          this.props.refresh(0);
          this.setMouseDownStop();
          break;

        case "underline_icon":
            this.props.documentCore.setTextUnderline();
            this.props.refresh(0);
            this.setMouseDownStop();
            break;
        case 'font-color_icon':
            this.setClassActive(true);
            this.activeColorIndex = 1;
            this.color = this.textColor;
            break;
        case 'background-color_icon':
            this.setClassActive(true);
            this.activeColorIndex = 2;
            this.color = this.backgroundColor;
            break;
          case 'subscript_icon':
            this.props.documentCore.setTextSubscript();
            this.props.refresh(0);
            this.setMouseDownStop();
            break;
          case 'superscript_icon':
            this.props.documentCore.setTextSuperscript();
            this.props.refresh(0);
            this.setMouseDownStop();
            break;
        default:
          break;
    }
  }

  loadCache = (e) => {
    return ;
    // console.log(e.currentTarget);
    const cacheDocument = localStorage.getItem('cacheDocument');
    const cacheMedia = localStorage.getItem('cacheMedia');
    const cacheSettings = localStorage.getItem('cacheSettings');
    const cacheStyles = localStorage.getItem('cacheStyles');

    // let navigator = window.navigator as any;
    // let clipboard = navigator.clipboard;
    // let data = clipboard.readText();
    // console.log(data);

    if (cacheDocument && cacheMedia && cacheSettings && cacheStyles) {
      this.props.documentCore.createNewDocument(true);
      const reader = new Reader(this.props.documentCore.getDocument());

      /** read media.xml first */
      if (!reader.readMedia(cacheMedia)) { // fail safe
        this.props.documentCore.createNewDocument();
      }
      /** then read document.xml */
      if (!reader.read(cacheDocument)) { // fail safe
        this.props.documentCore.createNewDocument();
      }

      this.props.testDocumentXml();

    } else {
      alert("cache is not found");
    }
  }

  /**
   * 工具栏字体，字号的更改
   */
  handlerChange(e) {
    // 字体
    if ( fonts[0].label === e.currentTarget.name ) {

      const fontFamilyVal = e.currentTarget.value;
      this.props.changeTextFontFamily(fontFamilyVal);
      this.props.refresh();

    } else if ( fonts[1].label === e.currentTarget.name ) { // 字号
      // todo
      this.props.changeTextFontSize(e.currentTarget.value);
      this.props.refresh();
    }
  }

  render() {
    let toolbarColor = this.props.colorConfig ? this.props.colorConfig.toolbar : null;
    let toolbarHeight = this.props.spacingConfig ? this.props.spacingConfig.toolbarHeight : null;

    return (
      <div className="hz-editor-toolbar" style={{"backgroundColor": toolbarColor, "height": toolbarHeight}}>
        {this.renderToolbar()}
        {/* {this.renderToolbar()} */}
      </div>
    );
  }

  renderToolbar() {
    return (
      <div className="hz-editor-toolbar-single">
        <div style={{fontSize: "13px"}} onClick={this.loadCache}>cache</div>
        {this.renderDropdown(fonts.slice(0, 2))}
        {this.renderIcons()}
      </div>
    );
  }

  renderDropdown(array: any) {
    // const { textProperty } = this.state;
    return array.map(({label, key, options}, index) => {
      return (
        <div className="toolbar-text" key={index}>
          {/* <label>{label}: </label> */}
          {/* <select name={label} id={key} value={textProperty && textProperty[key]} onChange={this.handleChangeTextProto}> */}
          <select onChange={this.handlerChange.bind(this)} name={label} id={key}>
            {options.map((item, index) =>  <option value={item.value} key={index}>{item.name}</option>)}
          </select>
        </div>
      );
    });
  }

  renderIcons() {
    // 扩展更多常用颜色
    const colors = [
        // 红色系
        '#c00000', '#ff0000', '#ff6666', '#ffcccc',
        // 橙色系
        '#ffa500', '#ff8c00', '#ffd700',
        // 黄色系
        '#ffff00', '#fffacd', '#f0e68c',
        // 绿色系
        '#008000', '#00ff00', '#98fb98', '#00fa9a',
        // 蓝色系
        '#0000ff', '#1e90ff', '#87ceeb', '#00bfff',
        // 紫色系
        '#7030a0', '#800080', '#ba55d3', '#9370db',
        // 黑白灰
        '#000000', '#333333', '#666666', '#999999', '#cccccc', '#ffffff'
    ];
    
    return (
      <div className="toolbar-icon" style={{"position": "relative"}}>
        <div className={`toolbar-color${this.state.isActive ? ' active' : ''}`}>
          <Sketch 
            color={this.color} 
            presetColors={colors}
            disableAlpha={true}
            style={{ width: '230px' }}
            onChange={(color) => {
              this.handlerChangeColor({
                hex: color.hex,
                rgb: color.rgb || { r: 0, g: 0, b: 0, a: 1 }
              });
            }}  
          />
          <style>
          {`
              /* 隐藏HEX、R、G、B输入框和文字 */
              .w-color-sketch-fields,
              .sketch-picker input,
              .w-color-sketch input,
              .w-color-sketch-field,
              .w-color-sketch-field-label,
              .w-color-sketch-field-wrap {
                  display: none !important;
              }

              /* 隐藏颜色信息头部区域 */
              .w-color-sketch-wrap > div:not(.w-color-sketch-saturation):not(.w-color-sketch-hue):not(.w-color-sketch-alpha):not(.w-color-sketch-presets) {
                  display: none !important;
              }

              /* 直接隐藏标签文字 */
              .w-color-sketch-label,
              label[title="hex"],
              label[title="r"],
              label[title="g"],
              label[title="b"],
              div:has(> label[title="hex"]),
              div:has(> label[title="r"]),
              div:has(> label[title="g"]),
              div:has(> label[title="b"]) {
                  display: none !important;
              }

              /* 隐藏整个颜色值区域 */
              .w-color-sketch-swatch,
              .sketch-picker .flexbox-fix {
                  display: none !important;
              }

              /* 提高饱和度和色相选择器的高度 */
              .w-color-sketch-saturation {
                  height: 150px !important;
              }

              /* 调整预设颜色区域样式，让颜色块更大 */
              .w-color-sketch-presets span,
              .sketch-picker .w-color-sketch-presets span {
                  width: 20px !important;
                  height: 20px !important;
                  margin-right: 5px !important;
                  margin-bottom: 5px !important;
              }
              
              /* 增加预设颜色区域高度，显示更多行 */
              .w-color-sketch-presets,
              .sketch-picker .w-color-sketch-presets {
                  height: auto !important;
                  max-height: 120px !important;
                  overflow-y: auto !important;
                  padding-top: 5px !important;
              }
          `}
          </style>
        </div>
        <svg version="1.1" onMouseDown={this.handlerClick.bind(this)} className="bold_icon" x="0px" y="0px"
	        width="15px" height="15px" viewBox="0 0 448 448">
          <g>
            <path d="M321.939,210.326C340.689,188.055,352,159.322,352,128C352,57.42,294.578,0,224,0h-64H96H64v448h32h64h96
              c70.578,0,128-57.421,128-128C384,273.521,359.102,232.752,321.939,210.326z M160,64h50.75c27.984,0,50.75,28.71,50.75,64
              s-22.766,64-50.75,64H160V64z M239.5,384H160V256h79.5c29.225,0,53,28.71,53,64S268.725,384,239.5,384z"/>
          </g>
        </svg>

        <svg version="1.1" onMouseDown={this.handlerClick.bind(this)} className="italic_icon" x="0px" y="0px" width="15px" height="15px" viewBox="0 0 16 16">
          <g>
            <polygon style={{"fill":"#030104"}} points="5,0 5.007,0.984 7,0.984 4,14.965 2.007,14.965 2,16 11,16 11,14.965 9.023,14.965 
              12.023,0.984 14,0.984 14,0 	"/>
          </g>
        </svg>
        
        <svg version="1.1" onMouseDown={this.handlerClick.bind(this)} className="underline_icon" x="0px" y="0px" width="15px" height="15px" viewBox="0 0 508 508">
          <g>
          <path style={{"fill":"#030302"}} d="M209.068,446.608V75.552H76.548V0h354.904v75.552H299.244v371.056H209.068z" data-original="#FF9933" className="active-path" data-old_color="#FF9933"/>
          <rect x="76.556" y="484" width="354.88" height="24" data-original="#000000" className="" style={{"fill":"#000000"}}/>
          </g>
        </svg>
        
        <svg version="1.1" onMouseDown={this.handlerClick.bind(this)} className="superscript_icon" x="0px" y="0px" width="15px" height="15px" viewBox="0 0 436.544 436.544">
          <g>
            <path d="M165.304,284.075l52.534-76.223h35.689v-47.967h-73.374l-39.971,65.096l-7.137,11.991c-1.521,2.279-2.57,4.285-3.14,5.996
              h-0.856c-0.571-2.279-1.615-4.285-3.14-5.996c-3.999-7.234-6.189-11.231-6.567-11.991l-39.687-65.096H0.859v47.967h39.112
              l52.819,77.661l-56.243,83.083H0v47.675h73.66l44.252-71.376c2.856-4.569,5.236-8.758,7.139-12.563l2.568-5.995h0.859
              c0.571,2.279,1.619,4.288,3.14,5.995l6.855,11.995l45.395,71.944h70.81v-47.675h-31.132L165.304,284.075z"/>
            <path d="M400.571,174.734v22.839h-66.239c0.567-6.28,3.47-12.275,8.706-17.987c5.235-5.708,11.464-10.607,18.698-14.7
              c7.231-4.093,15.037-8.894,23.414-14.417c8.378-5.521,16.133-11.091,23.271-16.706c7.139-5.612,13.038-12.891,17.699-21.838
              c4.661-8.945,6.995-18.751,6.995-29.408c0-18.846-6.476-33.927-19.418-45.253c-12.933-11.326-29.872-16.989-50.819-16.989
              c-19.984,0-37.873,6.189-53.662,18.558c-7.054,5.52-13.049,11.803-17.997,18.843l29.988,26.269
              c4.185-5.14,7.61-8.757,10.29-10.849c8.754-7.426,17.98-11.137,27.682-11.137c7.807,0,14.514,2.331,20.13,6.995
              c5.615,4.665,8.408,10.614,8.408,17.843c0,6.283-2.472,12.371-7.42,18.276c-4.938,5.896-11.136,11.038-18.562,15.415
              c-7.409,4.375-15.406,9.563-23.982,15.559c-8.562,5.996-16.557,12.181-23.982,18.555c-7.419,6.377-13.606,14.609-18.555,24.698
              c-4.949,10.088-7.427,21.222-7.427,33.404c0,3.424,0.384,7.81,1.144,13.134l0.855,7.7h146.756v-58.803H400.571z"/>
          </g>
        </svg>
				
        <svg version="1.1" onMouseDown={this.handlerClick.bind(this)} className="subscript_icon" x="0px" y="0px" width="15px" height="15px" viewBox="0 0 437.116 437.116">
          <g>
            <path d="M165.306,178.022l52.532-76.23h35.689V53.827h-73.374l-39.971,65.094l-7.137,11.992c-1.521,2.283-2.568,4.283-3.14,5.997
              h-0.856c-0.571-2.286-1.615-4.286-3.14-5.997c-3.999-7.236-6.189-11.23-6.567-11.992L79.656,53.827H0.859v47.965h39.112
              l52.819,77.656l-56.243,83.083H0v47.678h73.66l44.252-71.372c2.857-4.572,5.236-8.758,7.139-12.563l2.568-5.995h0.859
              c0.571,2.282,1.619,4.281,3.14,5.995l6.855,11.991l45.395,71.944h70.81v-47.678h-31.132L165.306,178.022z"/>
            <path d="M401.138,324.486v22.85H334.91c0.76-7.423,4.661-14.37,11.704-20.841c7.046-6.472,15.327-12.467,24.838-17.987
              c9.51-5.52,18.986-11.468,28.407-17.843c9.418-6.375,17.415-14.562,23.986-24.561c6.563-9.993,9.852-21.262,9.852-33.825
              c0-18.846-6.478-33.929-19.417-45.254c-12.933-11.326-29.872-16.989-50.819-16.989c-20.553,0-38.451,6.189-53.666,18.56
              c-7.05,5.518-13.045,11.801-17.994,18.842l29.985,26.265c4.188-5.14,7.61-8.761,10.28-10.849
              c8.761-7.431,17.986-11.142,27.692-11.142c7.802,0,14.51,2.331,20.129,6.995c5.612,4.665,8.411,10.616,8.411,17.844
              c0,6.282-2.471,12.371-7.419,18.28c-4.941,5.9-11.136,11.03-18.562,15.41c-7.416,4.377-15.41,9.565-23.982,15.554
              c-8.562,5.996-16.561,12.187-23.986,18.556c-7.416,6.375-13.606,14.612-18.555,24.701c-4.948,10.092-7.426,21.224-7.426,33.4
              c0,0.198,0.287,4.575,0.855,13.134l1.143,7.703h146.749v-58.803H401.138L401.138,324.486z"/>
          </g>
        </svg>
        
        {/* alignment */}
        <svg version="1.1" onClick={this.handlerClick.bind(this)} className="alignleft_icon" x="0px" y="0px" width="15px" height="15px" viewBox="0 0 511.63 511.631">
          <g>
          <path d="M18.274,237.548h438.542c4.949,0,9.229-1.812,12.847-5.429c3.614-3.612,5.421-7.898,5.421-12.845v-36.547
            c0-4.952-1.807-9.231-5.421-12.847c-3.617-3.617-7.897-5.424-12.847-5.424H18.274c-4.952,0-9.233,1.807-12.851,5.424
            C1.809,173.495,0,177.778,0,182.727v36.547c0,4.947,1.809,9.233,5.424,12.845C9.044,235.736,13.326,237.548,18.274,237.548z"/>
          <path d="M18.274,127.909h328.897c4.948,0,9.236-1.809,12.854-5.424c3.613-3.617,5.424-7.898,5.424-12.847V73.091
            c0-4.948-1.811-9.229-5.424-12.85c-3.617-3.612-7.905-5.424-12.854-5.424H18.274c-4.952,0-9.233,1.812-12.851,5.424
            C1.809,63.858,0,68.143,0,73.091v36.547c0,4.948,1.809,9.229,5.424,12.847C9.044,126.1,13.326,127.909,18.274,127.909z"/>
          <path d="M506.206,389.147c-3.617-3.617-7.905-5.427-12.85-5.427H18.274c-4.952,0-9.233,1.81-12.851,5.427
            C1.809,392.762,0,397.046,0,401.994v36.546c0,4.948,1.809,9.232,5.424,12.854c3.621,3.61,7.904,5.421,12.851,5.421h475.082
            c4.944,0,9.232-1.811,12.85-5.421c3.614-3.621,5.425-7.905,5.425-12.854v-36.546C511.63,397.046,509.82,392.762,506.206,389.147z"
            />
            <path d="M18.274,347.179h365.449c4.948,0,9.233-1.811,12.847-5.428c3.617-3.614,5.428-7.898,5.428-12.847v-36.542
              c0-4.945-1.811-9.233-5.428-12.847c-3.613-3.617-7.898-5.428-12.847-5.428H18.274c-4.952,0-9.233,1.811-12.851,5.428
              C1.809,283.129,0,287.417,0,292.362v36.545c0,4.948,1.809,9.236,5.424,12.847C9.044,345.371,13.326,347.179,18.274,347.179z"/>
          </g>
        </svg>
        
        <svg version="1.1" onClick={this.handlerClick.bind(this)} className="alignright_icon" x="0px" y="0px" width="15px" height="15px" viewBox="0 0 511.63 511.631">
          <g>
            <path d="M493.356,383.721H18.274c-4.952,0-9.233,1.81-12.851,5.427C1.809,392.762,0,397.046,0,401.994v36.546
              c0,4.948,1.809,9.232,5.424,12.854c3.621,3.61,7.904,5.421,12.851,5.421h475.082c4.944,0,9.232-1.811,12.85-5.421
              c3.614-3.621,5.425-7.905,5.425-12.854v-36.546c0-4.948-1.811-9.232-5.425-12.847C502.588,385.53,498.3,383.721,493.356,383.721z"
              />
            <path d="M493.356,274.088H127.91c-4.952,0-9.233,1.811-12.85,5.428c-3.618,3.613-5.424,7.901-5.424,12.847v36.545
              c0,4.948,1.807,9.236,5.424,12.847c3.62,3.617,7.901,5.432,12.85,5.432h365.446c4.944,0,9.232-1.814,12.85-5.432
              c3.614-3.61,5.425-7.898,5.425-12.847v-36.545c0-4.945-1.811-9.233-5.425-12.847C502.588,275.895,498.3,274.088,493.356,274.088z"
              />
            <path d="M493.356,164.456H54.821c-4.952,0-9.235,1.807-12.85,5.424c-3.617,3.615-5.424,7.898-5.424,12.847v36.547
              c0,4.947,1.807,9.233,5.424,12.845c3.619,3.617,7.898,5.429,12.85,5.429h438.535c4.944,0,9.232-1.812,12.85-5.429
              c3.614-3.612,5.425-7.898,5.425-12.845v-36.547c0-4.952-1.811-9.231-5.425-12.847C502.588,166.263,498.3,164.456,493.356,164.456z
              "/>
            <path d="M506.206,60.241c-3.617-3.612-7.905-5.424-12.85-5.424H164.457c-4.952,0-9.235,1.812-12.85,5.424
              c-3.617,3.617-5.426,7.902-5.426,12.85v36.547c0,4.948,1.809,9.229,5.426,12.847c3.619,3.616,7.901,5.424,12.85,5.424h328.899
              c4.944,0,9.232-1.809,12.85-5.424c3.614-3.617,5.425-7.898,5.425-12.847V73.091C511.63,68.143,509.82,63.861,506.206,60.241z"/>
          </g>
        </svg>
        
        <svg version="1.1" onClick={this.handlerClick.bind(this)} className="justify_icon" x="0px" y="0px" width="15px" height="15px" viewBox="0 0 511.63 511.631">
          <g>
            <path d="M493.356,274.088H18.274c-4.952,0-9.233,1.811-12.851,5.428C1.809,283.129,0,287.417,0,292.362v36.545
              c0,4.948,1.809,9.236,5.424,12.847c3.621,3.617,7.904,5.432,12.851,5.432h475.082c4.944,0,9.232-1.814,12.85-5.432
              c3.614-3.61,5.425-7.898,5.425-12.847v-36.545c0-4.945-1.811-9.233-5.425-12.847C502.588,275.895,498.3,274.088,493.356,274.088z"
              />
            <path d="M493.356,383.721H18.274c-4.952,0-9.233,1.81-12.851,5.427C1.809,392.762,0,397.046,0,401.994v36.546
              c0,4.948,1.809,9.232,5.424,12.854c3.621,3.61,7.904,5.421,12.851,5.421h475.082c4.944,0,9.232-1.811,12.85-5.421
              c3.614-3.621,5.425-7.905,5.425-12.854v-36.546c0-4.948-1.811-9.232-5.425-12.847C502.588,385.53,498.3,383.721,493.356,383.721z"
              />
            <path d="M506.206,60.241c-3.617-3.612-7.905-5.424-12.85-5.424H18.274c-4.952,0-9.233,1.812-12.851,5.424
              C1.809,63.858,0,68.143,0,73.091v36.547c0,4.948,1.809,9.229,5.424,12.847c3.621,3.616,7.904,5.424,12.851,5.424h475.082
              c4.944,0,9.232-1.809,12.85-5.424c3.614-3.617,5.425-7.898,5.425-12.847V73.091C511.63,68.143,509.82,63.861,506.206,60.241z"/>
            <path d="M493.356,164.456H18.274c-4.952,0-9.233,1.807-12.851,5.424C1.809,173.495,0,177.778,0,182.727v36.547
              c0,4.947,1.809,9.233,5.424,12.845c3.621,3.617,7.904,5.429,12.851,5.429h475.082c4.944,0,9.232-1.812,12.85-5.429
              c3.614-3.612,5.425-7.898,5.425-12.845v-36.547c0-4.952-1.811-9.231-5.425-12.847C502.588,166.263,498.3,164.456,493.356,164.456z
              "/>
          </g>
        </svg>

        <svg version="1.1" onMouseDown={this.handlerClick.bind(this)} className="font-color_icon" x="0px" y="0px" width="15px" height="15px" viewBox="0 0 511.231 511.231">
          <path style={{"fill":"#CFD8DC"}} d="M106.282,361.898h298.667c29.455,0,53.333,23.878,53.333,53.333v42.667
            c0,29.455-23.878,53.333-53.333,53.333H106.282c-29.455,0-53.333-23.878-53.333-53.333v-42.667
            C52.949,385.776,76.827,361.898,106.282,361.898z"/>
          <path style={{"fill":"#FFC107"}} d="M414.485,410.453c-1.808-3.609-5.499-5.888-9.536-5.888h-64c-5.891,0-10.667,4.776-10.667,10.667
            c0,2.308,0.749,4.554,2.133,6.4l32,42.667c3.535,4.713,10.221,5.668,14.933,2.133c0.809-0.606,1.527-1.325,2.133-2.133l32-42.667
            C415.908,418.397,416.297,414.068,414.485,410.453z"/>
          <path style={{"fill":"#2196F3"}} d="M106.282,404.565h128c5.891,0,10.667,4.776,10.667,10.667v42.667c0,5.891-4.776,10.667-10.667,10.667
            h-128c-5.891,0-10.667-4.776-10.667-10.667v-42.667C95.616,409.34,100.391,404.565,106.282,404.565z"/>
          <g>
            <path style={{"fill":"#455A64"}} d="M362.282,340.565c-4.597,0.006-8.681-2.934-10.133-7.296L255.616,43.626l-96.533,289.643
              c-1.862,5.591-7.903,8.614-13.493,6.752c-5.591-1.862-8.614-7.903-6.752-13.493l0,0l106.667-320
              c2.535-5.591,9.122-8.068,14.713-5.533c2.454,1.113,4.42,3.079,5.533,5.533l106.667,320c1.862,5.589-1.16,11.629-6.749,13.491
              C364.575,340.382,363.432,340.566,362.282,340.565z"/>
            <path style={{"fill":"#455A64"}} d="M319.616,233.898h-128c-5.891,0-10.667-4.776-10.667-10.667c0-5.891,4.776-10.667,10.667-10.667h128
              c5.891,0,10.667,4.776,10.667,10.667C330.282,229.123,325.507,233.898,319.616,233.898z"/>
          </g>
        </svg>
        
        <svg version="1.1" onMouseDown={this.handlerClick.bind(this)} className="background-color_icon" x="0px" y="0px" width="15px" height="15px" viewBox="0 0 504.489 504.489">
          <g>
            <rect x="7.5" y="67.366" style={{"fill":"#36A6CE"}} width="369.757" height="369.757"/>
            <polygon style={{"fill":"#E5E9EA"}} points="333.165,111.458 333.165,111.458 221.364,111.458 163.393,111.458 51.592,111.458     51.592,169.429 51.592,169.429 51.592,202.555 109.563,202.555 109.563,169.429 163.393,169.429 163.393,335.06 127.592,335.06     127.592,393.031 163.393,393.031 221.364,393.031 257.164,393.031 257.164,335.06 221.364,335.06 221.364,169.429     275.194,169.429 275.194,202.555 333.165,202.555 333.165,111.458   "/>
            <polygon style={{"fill":"#E5E9EA"}} points="315.859,288.778 285.587,319.05 359.374,332.293 416.134,389.053 497.489,307.698     397.214,207.423   "/>
            <polygon style={{"fill":"#53BCE9"}} points="497.5,307.699 416.134,389.053 359.383,332.302 285.588,319.057 315.863,288.782     478.583,288.782   "/>
          </g>
          <g>
            <path d="M277.589,429.623H15V74.866h354.757v124.913c0,4.142,3.357,7.5,7.5,7.5s7.5-3.358,7.5-7.5V67.366    c0-4.142-3.357-7.5-7.5-7.5H7.5c-4.142,0-7.5,3.358-7.5,7.5v369.757c0,4.142,3.358,7.5,7.5,7.5h270.089c4.143,0,7.5-3.358,7.5-7.5    S281.732,429.623,277.589,429.623z"/>
            <path d="M377.257,374.27c-4.143,0-7.5,3.358-7.5,7.5v47.853h-67.166c-4.143,0-7.5,3.358-7.5,7.5s3.357,7.5,7.5,7.5h74.666    c4.143,0,7.5-3.358,7.5-7.5V381.77C384.757,377.628,381.4,374.27,377.257,374.27z"/>
            <path d="M275.195,210.055h57.971c4.143,0,7.5-3.358,7.5-7.5v-91.097c0-4.142-3.357-7.5-7.5-7.5l-216.565,0    c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5,7.5,7.5l209.065,0v76.097h-42.971v-25.626c0-4.142-3.357-7.5-7.5-7.5h-53.831    c-4.142,0-7.5,3.358-7.5,7.5V335.06c0,4.142,3.358,7.5,7.5,7.5h28.301v42.971H135.092V342.56h28.301c4.142,0,7.5-3.358,7.5-7.5    V169.429c0-4.142-3.358-7.5-7.5-7.5h-53.83c-4.142,0-7.5,3.358-7.5,7.5v25.626H59.092l0-76.098h32.507c4.142,0,7.5-3.358,7.5-7.5    s-3.358-7.5-7.5-7.5H51.592c-1.989,0-3.897,0.79-5.303,2.197c-1.407,1.406-2.197,3.314-2.197,5.303l0,91.098    c0,4.142,3.358,7.5,7.5,7.5h57.971c4.142,0,7.5-3.358,7.5-7.5v-25.626h38.83V327.56h-28.301c-4.142,0-7.5,3.358-7.5,7.5v57.971    c0,4.142,3.358,7.5,7.5,7.5h129.572c4.143,0,7.5-3.358,7.5-7.5V335.06c0-4.142-3.357-7.5-7.5-7.5h-28.301V176.929h38.831v25.626    C267.695,206.698,271.052,210.055,275.195,210.055z"/>
            <path d="M502.439,302.748l-17.15-17.15l-6.429-61.654c-1.228-11.767-8.916-21.533-20.066-25.486    c-11.148-3.954-23.273-1.214-31.641,7.152l-10.926,10.926l-14.063-14.063c-2.734-2.733-7.166-2.733-9.9,0l-81.355,81.355    L280.637,314.1c-1.843,1.843-2.509,4.558-1.729,7.044c0.78,2.487,2.878,4.334,5.443,4.795l71.626,12.856l55.206,55.207    c1.313,1.313,3.094,2.05,4.95,2.05s3.637-0.737,4.95-2.05l81.355-81.355c1.313-1.313,2.05-3.093,2.05-4.95    S503.751,304.06,502.439,302.748z M437.052,215.509c4.579-4.579,10.955-6.022,17.063-3.857    c6.104,2.165,10.149,7.302,10.821,13.744l4.639,44.488l-43.448-43.448L437.052,215.509z M416.133,379.153l-51.809-51.81    c-1.011-1.011-2.307-1.688-3.714-1.94l-60.601-10.877l97.204-97.204l90.375,90.375L416.133,379.153z"/>
            <path d="M339.787,288.775c0,3.866,3.134,7,7,7h101.865c3.866,0,7-3.134,7-7s-3.134-7-7-7H346.787    C342.92,281.775,339.787,284.909,339.787,288.775z"/>
          </g>
        </svg>
        
      </div>
    )
  }

  private setTextColor(color: string): void {
    this.props.documentCore.setTextColor(color);
    this.textColor = color;
  }

  private setBackgroundColor(color: string): void {
    this.props.documentCore.setTextBackgrounColor(color);
    this.backgroundColor = color;
  }

}
