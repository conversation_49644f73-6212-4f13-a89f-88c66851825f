import * as React from 'react';
import Page from './Page';
import Cursor from './Cursor';
import {TextArea} from './TextArea';
import MouseEvent from './MouseEvent';
import OperateAISuggestion from '../../../../common/external/OperateAISuggestion';
import KeyEvent from './KeyEvent';
import NewControlEvent from './NewControlEvent';
import { FixedSizeList as List } from '../reactWindow/index.cjs';
import {SafeMessage} from './SafeMessage';
import { DocumentCore } from '../../../../model/DocumentCore';
import {
    ViewModeType,
    FILL_COLOR,
    FillColorType,
    IEditorBackground,
    WatermarkType,
    WATERMARK_DEFAULT_FONTSIZE,
    numtoFixed,
    WATERMARK_COLOR, WATERMARK_COLOR2, FillTextColor, EDITOR_BACKGROUNDCOLOR_PROXY,
    IEditorBackgroundColor,
    /* IFTRUE_WATER */
    EXEC_DOM,
    fromCharCode,
    /* FITRUE_WATER */
    isMacOs,
    COMMENT_FLAG,
} from '../../../../common/commonDefines';

import {Copy} from '../../CopyPaste';
import {Scrollbar} from '../../../../common/Scrollbar';
import ExternalEvent from '../../../../common/external/Event';
import RevisionChangeEvent from './RevisionChangeEvent';
import { ICursorProperty } from '../../../../model/CursorProperty';
import { WavyUnderlineHandler } from './WavyUnderlineHandler';
// import HeaderFooter from '../../../../model/core/HeaderFooter';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../../../common/GlobalEvent';
import TableEvent from './TableEvent';
import { getTheme } from '@hz-editor/theme';
import { getDocumentCoreRecorder } from '../../../../model/DocumentCoreRecordReplay';
import NISTableEvent from './NISTableEvent';
import { NISTableLayer } from './NISTableLayer';
import { consoleLog, isGlobalTestData } from '../../../../common/GlobalTest';
import { Region } from '../../../../model/core/Region';
import { measure } from '../../../../model/core/util';
import { getPagePadding } from '@/common/commonMethods';
import { SPELL_CHECK } from '@/common/Spellcheck';
import { AsyncLoadComment } from './AsyncLoadComment';
// import { editorCallback } from '@/common/GlobalTest';

interface IDocumentProps {
    height?: number;
    headerHeight?: number;
    host: any;
    iframe?: any;
    unResizeCursor?: boolean;
}

interface IState {
    bRefresh: boolean;
}

// interface pageState {
//   pageInfo: any,
//   textPage: any,
// }
const ab8: any = '97';

export class Document extends React.Component<IDocumentProps, IState> {
    public externalEvent: ExternalEvent;
    // public external: IExternalInterface;
    public documentCore: DocumentCore;
    public docId: number;
    public currentRef: any;
    public bPrint: boolean;
    public odfSvrUrl:string;
    public pdfSvrUrl:string;
    private host: any;
    // private listDom: HTMLDivElement;
    private currentIndex: number;
    private listDom: HTMLDivElement;
    private cursor: Cursor;
    private textArea: TextArea;
    private mouseEvent: MouseEvent;
    private keyEvent: KeyEvent;
    private tableEvent: TableEvent;
    private nisTableEvent: NISTableEvent;
    private bUpdatePage: boolean;
    private newControlEvent: NewControlEvent;
    private revisionEvent: RevisionChangeEvent;
    private wavyUnderlineHandler: WavyUnderlineHandler;
    private _activeIndex: number;
    private _copyPaste: Copy;
    private _scale: number;
    private list: any;
    private scrollbar: Scrollbar;
    private _height: number;
    private _oldHeight: number; // 用于存储ContentChange前的高度，用于inline模式下向外部反馈变化事件
    private viewMode: ViewModeType;
    private textAreaRef: any;
    private _scrollPageDom: HTMLDivElement;
    private _dragScrollFlag: boolean;
    private bAutoSaveInc: boolean;
    private autoSaveIncTimer: any;
    private _itemSize: number;
    private _bCanInput: boolean;
    private _pageRefs: any;
    private _refPages: any[];
    private _refTimeout: any;
    private backgroundImage: string;
    private _header: any;
    private _footer: any;
    private _pageHeight?: number;
    private _pageWidth?: number;
    private _renderedPageInfo: any;
    private __timeStep: number = 0; // 控制水印刷新间隔
    private _totalPage: number; // model层面的页总数
    private _total: number; // UI层页总数

    private _bRefresh: boolean;
    private _bRefresh2: boolean;
    // AI建议相关属性
    private AISuggestion: OperateAISuggestion; // AI建议操作实例
    private aiSuggestionTimer: NodeJS.Timeout | null = null;
    private isAITimerRunning: boolean = false;
    private currentAIEnabledRegion: Region | null = null;
    private readonly AI_SUGGESTION_DELAY: number = 2000; // 2秒延迟
    private readonly THROTTLE_INTERVAL: number = 300; // 300毫秒节流间隔
    private lastInputProcessTime: number = 0;
    private aiRequestAbortController: AbortController | null = null; // 用于取消AI请求的AbortController

    private sendMonitorData: any;
    private _commentContainerRef: any; // 批注容器
    private _bAsyncLoadComment: boolean;

    constructor(props: IDocumentProps) {
        super(props);
        this.host = props.host;
        this.bPrint = this.host.bPrint;
        this.pdfSvrUrl = this.host.pdfSvrUrl;
        this.odfSvrUrl = this.host.odfSvrUrl;
        this.docId = this.host.docId;
        this.documentCore = props.host.state.documentCore;
        this.currentRef = React.createRef();
        this.textAreaRef = React.createRef();
        this._commentContainerRef = React.createRef();
        this.cursor = new Cursor(this);
        // 初始化 AISuggestion 实例
        this.AISuggestion = new OperateAISuggestion(this.host);
        this.state = {
            bRefresh: false,
        };
        this._activeIndex = 0;
        this.list = React.createRef();
        this.bAutoSaveInc = this.documentCore.isAutoSaveInc();
        if (this.isCascadeManager() === true) {
            this.bPrint = true;
        }
        /* IFTRUE_WATER */
        this[fromCharCode(ab8)] = EXEC_DOM[fromCharCode(ab8)];
        /* FITRUE_WATER */
    }
    
    public componentDidMount(): void {
        this.textArea = new TextArea(this);
        this.textArea.addTextArea(this.textAreaRef.current);
        if (!this.bPrint) {
            this.newControlEvent = new NewControlEvent(this);
            this.revisionEvent = new RevisionChangeEvent(this);
            // 初始化波浪线悬停处理器 
            this.wavyUnderlineHandler = new WavyUnderlineHandler(this.documentCore);
        }

        this.mouseEvent = new MouseEvent(this);
        if ( NURSING_FEATURE ) {
            this.nisTableEvent = new NISTableEvent(this);
        }
        this.initCursorPos();
        if (this.bPrint) {
            if (this.isCascadeManager()) {
                this.scrollbar = new Scrollbar(this);
            }
        } else {
            this.scrollbar = new Scrollbar(this);
        }
        if (this.bPrint === true) {
            this.cursor.setNodeVisible(true);
            return;
        }

        this.tableEvent = new TableEvent(this);
        this.keyEvent = new KeyEvent(this);
        this._copyPaste = new Copy(this);
        gEvent.addEvent(this.docId, gEventName.Resize, this.onResize);
        gEvent.addEvent(this.docId, gEventName.AutoSaveInc, this.setAutoSave);
        gEvent.addEvent(this.docId, gEventName.RefreshDocUI, this.refresh);
        if ( this.bAutoSaveInc ) {
            this.startAutoSave();
        }

        gEvent.addEvent(this.docId, gEventName.SendMonitorData, this.startSendMonitorData);
        
        // 添加波浪线鼠标事件监听 
        if (this.wavyUnderlineHandler) {
            this.addWavyUnderlineEvents();
        }
        
        const commentContainer = this._commentContainerRef.current;
        if (commentContainer) {
            const {clientHeight, scrollHeight} = this.listDom.parentElement;
            const right = clientHeight < scrollHeight ? '17px' : '';
            if (commentContainer.style.paddingRight !== right) {
                commentContainer.style.paddingRight = right;
            }
        }
    }

    public clearDatas(): void {
        this.newControlEvent?.clearDatas();
        this.tableEvent?.clearDatas();
        this.nisTableEvent?.clearDatas();
    }

    public setCursorTrueVisible(bVisible: boolean): boolean {
        if (this.cursor.isVisible() === bVisible) {
            return false;
        }
        this.setCursorVisible(bVisible);
        if (bVisible === true) {
            this.focus();
        } else {
            this.textArea?.blur();
        }
        return true;
    }

    public componentWillUnmount(): void {
        // 清除AI计时器
        this.clearAITimer();
        
        gEvent.deleteEvent(this.docId, gEventName.Resize, this.onResize);
        gEvent.deleteEvent(this.docId, gEventName.RefreshDocUI, this.refresh);
        gEvent.deleteEvent(this.docId, gEventName.AutoSaveInc, this.setAutoSave);
        gEvent.deleteEvent(this.docId, gEventName.SendMonitorData, this.startSendMonitorData);
        this.removeDragScrollEvent();
        this._pageRefs = null;
        this._refPages = null;
        this.externalEvent = null;
        this.textArea = null;
        this.listDom = null;
        this.scrollbar = null;
        this._scrollPageDom = null;
        this.list = null;
        this.clearDatas();
        // 清理波浪线处理器
        if (this.wavyUnderlineHandler) {
            this.removeWavyUnderlineEvents();
            this.wavyUnderlineHandler.destroy();
        }
        window.removeEventListener('resize', this.onResize);
        this.stopAutoSave();
        // console.log('close document')
        // console.log(gEvent.getAllEvents())
    }

    /**
     * 当前是否是级联面板使用
     * @returns 
     */
    public isCascadeManager(): boolean {
        return this.host._bCascadeManager;
    }


    public showSectionBorder(): boolean {
        return this.host.showSectionBorder();
    }

    public componentDidUpdate(): void {
        if ( this.newControlEvent && this.documentCore.isCursorInNewControl() ) {
            this.newControlEvent.renderBackground(this.cursor.getCursorPosition());
        }

        if ( this.bAutoSaveInc ) {
            this.startAutoSave();
        } else {
            this.stopAutoSave();
        }
        
        const commentContainer = this._commentContainerRef.current;
        if (commentContainer) {
            const {clientHeight, scrollHeight} = this.listDom.parentElement;
            const right = clientHeight < scrollHeight ? '17px' : '';
            if (commentContainer.style.paddingRight !== right) {
                commentContainer.style.paddingRight = right;
            }
        }

    }

    public render(): any {
        const { total: totalPages, pageProperty } = this.documentCore.render();
        const myRef = this.host.myRef.current;
        let scale = this.documentCore.getViewScale();
        const documentCore = this.documentCore;
        const viewMode = this.viewMode = documentCore.getViewMode();
        let width = this.host.clientWidth;
        if (width === undefined) {
            width = document.documentElement.clientWidth;
        }
        this._total = undefined;
        let total: number = this._totalPage = totalPages;
        let safeWidth = pageProperty.width * scale;
        if (viewMode === ViewModeType.MorePage) {
            let curWidth = safeWidth + 35;
            if (curWidth < width) {
                if (total > 1) {
                    safeWidth *= 2;
                }

                total = Math.ceil(total / 2);
            }
            this._total = total;
        } else if (viewMode === ViewModeType.CompactView) {
            const pageWidth = this._pageWidth = pageProperty.width - pageProperty.paddingLeft
            - pageProperty.paddingRight + getPagePadding.CompactWidth * 2;
            this._pageHeight = pageProperty.height; // - pageProperty.paddingTop - pageProperty.paddingBottom;
            scale = (pageProperty.width) / pageWidth * scale;
        }
        this._scale = scale;
        let checkIframeHeight = false;
        let height: number = this.props.height;
        if (myRef) {
            height = myRef.parentNode.clientHeight;
        }
        this.backgroundImage = null;
        this._header = null;
        this._footer = null;
        this.bPrint = this.host.bPrint;
        if (height === undefined) {
            height = document.firstElementChild.clientHeight;
        }
        height = (height - (this.props.headerHeight || 0)) || 0;
        if (this.documentCore.getDynamicHeightMode() && this.documentCore.getPageCount() === 1) {
            const tempHeight = this.getLastPageRowHeight();
            if (tempHeight < height) {
              height = tempHeight;
            }
        }

        if (this.shouldCheckIframeHeight(height) === true ) {
            checkIframeHeight = true;
        }
        height = numtoFixed(height, 0);
        // console.log(height)
        // console.log(this._height)
        this._height = height;

        if (checkIframeHeight === true) {
            this.checkIframeHeight(height);
        }

        let style = 'page-list development-mode';
        // if (this.host.id !== 'editor-id') {
        //     // initialized iframe
        //     width = pageProperty.width;
        //     style = 'page-list iframe-initialized';
        // }

        let className = 'ReactVirtualized__Grid editor-page-content editor-default-view';
        const rowHeight = documentCore.isInlineMode()
            ? (pageProperty.height + pageProperty.paddingBottom + pageProperty.paddingTop) * scale
            : numtoFixed(this.getPageRowHeight({index: 0}), 0);

        if (viewMode === ViewModeType.MorePage) {
            className += ' editor-morepage-view';
        } else if (viewMode === ViewModeType.WebView) {
            className += ' editor-web-view';
        } else {
            className += ' editor-other-view';
        }
        style = 'ReactVirtualized__Grid__innerScrollContainer ' + style;
        this._itemSize = rowHeight;
        let safe;
        let textShadow;

        // const userAgent = navigator.userAgent.toLowerCase();
        // const bWinOS = userAgent.indexOf('windows') > -1;
        if (!isMacOs) {
            const userAgent = navigator.userAgent.toLowerCase();
            const bIE = (userAgent.indexOf('msie') > -1 || userAgent.indexOf('trident') > -1 ||
                        userAgent.indexOf('edge') > -1);
            const bChorme = !bIE && userAgent.indexOf('chrome') > -1;
            if (bChorme) {
                textShadow = 'transparent 0px 0px 0px, rgb(0 0 0 / 100%) 0px 0px 0px';
            }
        }

        if (this.bPrint !== true) {
            safe = <SafeMessage host={this} width={safeWidth} maxWidth={width} />;
        }

        this._pageRefs = {};
        const backgroundColorPr = EDITOR_BACKGROUNDCOLOR_PROXY as IEditorBackgroundColor;
        const color = backgroundColorPr && backgroundColorPr.backgroundColor;

        const pageProps = {...pageProperty};
        if (viewMode === ViewModeType.CompactView) {
            pageProps.width = this._pageWidth;
            pageProps.height = this._pageHeight;

        } else if (viewMode === ViewModeType.MorePage) {
            pageProps.width = safeWidth;
        }
        if (documentCore.isInlineMode()) {
            pageProps.height += pageProps.paddingBottom + pageProps.paddingTop;
        }
        this._renderedPageInfo = {
            pageProperty,
            total,
            pageProps
        };
        return (
            <div ref={this.currentRef} className={className} style={{background: color, textShadow}}>
                <List
                    bRefresh={this.state.bRefresh}
                    className={style}
                    ref={this.list}
                    isScrolling={false}
                    showAll={this.bPrint}
                    // 会导致页面重刷，但是可以通过shouldComponentUpdate函数减少不必要的刷新。滚动页面时，选择区域的渲染也需要此onScroll
                    onScroll={this.onScroll}
                    dragScroll={this.dragScroll}
                    overflow={documentCore.isInlineMode() ? 'auto hidden' : ''} // 控制滚动条
                    // overscanRowCount={1}
                    // width={document.documentElement.clientWidth}
                    overscanCount={1}
                    // height constraint for list (just drawing canvas ui height; nothing todo with pageproperty)
                    height={0}
                    clientHeight={height}
                    itemCount={total}
                    itemSize={rowHeight} // page count

                    extendComponents={[this.renderCommentLayer(total, pageProps, rowHeight)]}
                    // onRowsRendered={this.onRowsRendered}
                    // rowHeight={this.getPageRowHeight}
                    // scrollToIndex={0} // 初始化打开文档时，可指定显示第几页
                >
                    {this.renderPages}
                </List>
                <textarea ref={this.textAreaRef} id='textarea_input'/>
                {safe}
            </div>
        );
    }

    public setSelections(): void {
        this.setCursorVisible(false);
        if (this.mouseEvent) {
            this.setCursorVisible(false);
            this.mouseEvent.setSelections();
        }
    }

    public clearSection(): void {
        this.mouseEvent.clearSection();
    }

    public getPageIndex(): number {
        return this.mouseEvent.getPageIndex();
    }

    public getHeaderHeight(): number {
        return this.props.headerHeight;
    }

    public getCurPageIndex(): number {
        return this.mouseEvent.getCurPageIndex();
    }

    public getCopyPaste(): Copy {
        return this._copyPaste;
    }

    public isMousdown(): boolean {
        return this.mouseEvent.isMousdown();
    }

    public setPageIndex(pageIndex: number): void {
        if (this.mouseEvent) {
            this.mouseEvent.setPageIndex(pageIndex);
        }
    }

    public getCurrentPageNode(): SVGElement {
        return this.getNewPageNode(this.getPageIndex());
    }

    public getScale(): number {
        return this._scale;
    }

    // public cursorBlur(): void {
    //     this.cursor.cursorBlur();
    // }

    public setCursorVisible( bVisible: boolean): void {
        const imageFlags = this.documentCore.getImageFlags();
        if (bVisible !== false && this.newControlEvent && this.newControlEvent.isCursorHidden()
        || (imageFlags.isImageOnClick || imageFlags.isHandlerOnClick)) {
            bVisible = false;
        }
        this.cursor.setCursorVisible(bVisible);
    }

    public setCursorPosition(position: any): void {
        position.x = Math.round(position.x);
        position.y1 = Math.round(position.y1);
        position.y2 = Math.round(position.y2);
        const flag = this.cursor.setCursorPosition(position);

        if ( this.documentCore.isStrictMode2() ) {
            if ( this.documentCore.isCursorInNewControl() || this.documentCore.isCursorInRegion()) {
                this.cursor.setNodeVisible(false);
            } else {
                this.cursor.setNodeVisible(true);
            }
        } else {
            this.cursor.setNodeVisible(this.documentCore.isProtectedMode());
        }

        if (!flag) {
            return;
        }
        // console.dir(this.cursor.getCursor());
        const attr = this.cursor.getCursor()
            .getBoundingClientRect();
        // console.dir(this.textArea.wrapper())
        // console.log(attr)
        this.textArea.setTextAreaPosition(attr.left, attr.top);
    }

    // public cursorFocused(): void {
    //     this.cursor.cursorFocused();
    // }

    // public blurCursor(pageNode: SVGElement): void {
    //     const cursor = this.cursor;
    //     this.renderSetCursor(pageNode);
    //     // cursor
    // }

    public focus(): void {
        this.textArea?.focus();
    }

    public addTabindex(): void {
        this.textArea.addTabindex();
    }

    public getContainer(): HTMLDivElement {
        // if (this.listDom == null) {
        //     this.listDom = this.currentRef.current.firstChild.firstChild;
        // }
        // return this.listDom;
        if (!this.currentRef.current) {
            return;
        }
        this.listDom = this.currentRef.current.firstChild.firstChild;
        return this.listDom;
    }

    public clearContainer(): void {
        this.removeDragScrollEvent();
        this.listDom = null;
        this._scrollPageDom = null;
    }

    public compositionStart(): void {
        //
    }

    public isCusorVisible(): boolean {
        return this.cursor.isVisible();
    }

    public compositionEnd(content: string, bComposition: boolean): void {
        if (this._bCanInput === false) {
            return;
        }
        const documentCore = this.documentCore;
        if ( false === documentCore.canInput() || null == content || '' === content) {
            return;
        }
        if (true === bComposition) {
            if ( false === documentCore.onCompositionStart()) {
                return ;
            }
        }
        this._bRefresh = false;
        if ( documentCore.insertCompositionInput(content, bComposition) ) {
            if (this.nisTableEvent) {
                this.nisTableEvent.onInput(content);
            }
            // 微软输入法
            if (true === bComposition && '' !== content) {
                // this.changeDocument();
                // this.updateCursor(true, true, true);
                if (this._bRefresh2) {
                    this._bRefresh2 = false;
                    this.refresh(true);
                } else {
                    this.refreshPages();
                }
                
            }
            this.keyDownInputContent(content);
        }
        this._bRefresh = true;
    }

    // 组件卸载时清理资源已在上面的componentWillUnmount方法中实现

    public insertCompositionInput(content: string, bComposition: boolean): void {
        if (this._bCanInput === false) {
            return;
        }
        const documentCore = this.documentCore;
        if ( null == content || '' === content || documentCore.isProtectedMode()
            || false === documentCore.canInput() ) {
            return ;
        }
        this._bRefresh = false;
        if ( false === bComposition && documentCore.onCompositionStart()
            && documentCore.insertCompositionInput(content, bComposition) ) {
            this.mouseEvent.clearSection(); // input after add table row or col
            if (this.nisTableEvent) {
                this.nisTableEvent.onInput(content);
            }
            if (this._bRefresh2 === true) {
                this._bRefresh2 = false;
                this.refresh(true);
            } else {
                this.refreshPages();
            }
            
            // this.updateCursor(true, true, true);
            // this.host.handleRefresh(undefined, true);
            // this.host.handleContainerRefresh();
            this.keyDownInputContent(content);
        }
        this._bRefresh = true;
    }

    public isPrint(): boolean {
        return this.bPrint;
    }

    public setMouseDown(flag: boolean): void {
        if (!this.newControlEvent) {
            return;
        }
        this.newControlEvent.setMouseDown(flag);
    }

    /**
     * 处理输入事件，检查是否需要启动AI计时器
     * 使用节流机制减少处理频率
     */
    private handleInputEventForAI(): void {
        const now = Date.now();
        
        // 如果距离上次处理时间不足节流间隔，则跳过
        if (now - this.lastInputProcessTime < this.THROTTLE_INTERVAL) {
            return;
        }
        
        // 更新上次处理时间
        this.lastInputProcessTime = now;
        
        // 获取当前编辑区域
        const region = this.getCurrentEditingRegion();
        
        // 检查区域是否启用AI支持
        if (region && region.getEnableAISupport && region.getEnableAISupport()) {
            // 更新当前区域引用
            this.currentAIEnabledRegion = region;
            
            // 如果计时器未运行，则启动计时器
            if (!this.isAITimerRunning) {
                this.startAITimer();
            } else {
                // 如果计时器已运行，则重置计时器（不创建新的）
                this.resetAITimer();
            }
        } else {
            // 如果不在启用AI的区域内，则清除计时器
            this.clearAITimer();
        }
    }

    /**
     * 获取当前正在编辑的区域
     * @returns 当前编辑区域，如果不在区域内则返回null
     */
    private getCurrentEditingRegion(): Region | null {
        if (!this.documentCore) {
            return null;
        }
        
        // 使用documentCore的getCursorInRegion方法获取当前区域
        const region = this.documentCore.getCursorInRegion();
        if (region && region instanceof Region) {
            return region;
        }
        
        return null;
    }

    /**
     * 启动AI计时器
     */
    private startAITimer(): void {
        this.clearAITimer();
        
        // 取消之前的请求（如果有）
        this.cancelAIRequest();
        
        this.aiSuggestionTimer = setTimeout(async () => {
            if (this.currentAIEnabledRegion) {
                console.log("检测到区域内2秒无操作，触发AI建议请求", this.currentAIEnabledRegion);
                
                try {
                    // 创建新的AbortController用于取消请求
                    this.aiRequestAbortController = new AbortController();
                    
                    // 获取当前区域的历史文本
                    const historyText = this.AISuggestion.getHistoryText(this.currentAIEnabledRegion);
                    
                    // 构建请求参数
                    const params = {
                        historyExisting: historyText,
                        needGenerateLength: 100 // 默认生成100个字符的建议
                    };
                    
                    // 发送AI建议请求（使用带重试的方法）
                    const suggestions = await this.AISuggestion.requestAISuggestionWithRetry(
                        params,
                        this.aiRequestAbortController.signal,
                        3 // 最多重试3次
                    );
                    
                    // 如果有建议结果且请求未被取消，显示建议弹窗
                    if (suggestions && suggestions.length > 0 && this.aiRequestAbortController) {
                        // 直接传递字符串数组作为建议
                        await this.AISuggestion.showAISuggestionAtCursor(suggestions);
                        // 注意：onSelect回调已在OperateAISuggestion类中处理
                    }
                } catch (error) {
                    // 如果不是取消请求导致的错误，记录错误信息
                    if (error.name !== 'AbortError') {
                        console.error("AI建议请求失败:", error);
                    }
                } finally {
                    // 清理状态
                    this.aiRequestAbortController = null;
                }
            }
            
            this.isAITimerRunning = false;
            this.aiSuggestionTimer = null;
        }, this.AI_SUGGESTION_DELAY);
        
        this.isAITimerRunning = true;
    }
    /**
     * 重置AI计时器（不创建新的，只延长现有计时器）
     */
    private resetAITimer(): void {
        if (this.aiSuggestionTimer) {
            clearTimeout(this.aiSuggestionTimer);
            
            // 重新启动计时器，复用startAITimer的逻辑
            this.aiSuggestionTimer = setTimeout(async () => {
                if (this.currentAIEnabledRegion) {
                    console.log("检测到区域内2秒无操作，触发AI建议请求", this.currentAIEnabledRegion);
                    
                    try {
                        // 创建新的AbortController用于取消请求
                        this.aiRequestAbortController = new AbortController();
                        
                        // 获取当前区域的历史文本
                        const historyText = this.AISuggestion.getHistoryText(this.currentAIEnabledRegion);
                        
                        // 构建请求参数，与后端结构保持一致
                        const params = {
                            historyExisting: historyText,
                            needGenerateLength: 20, // 默认生成100个字符的建议
                            patientInfo: this.AISuggestion.getPatientInfo ? this.AISuggestion.getPatientInfo() : undefined,
                            labData: this.AISuggestion.getLabData ? this.AISuggestion.getLabData() : undefined
                        };
                        
                        // 发送AI建议请求（使用带重试的方法）
                        const suggestions = await this.AISuggestion.requestAISuggestionWithRetry(
                            params,
                            this.aiRequestAbortController.signal,
                            3 // 最多重试3次
                        );
                        
                        // 如果有建议结果且请求未被取消，显示建议弹窗
                        if (suggestions && suggestions.length > 0 && this.aiRequestAbortController) {
                            // 直接传递字符串数组作为建议
                            await this.AISuggestion.showAISuggestionAtCursor(suggestions);
                            // 注意：onSelect回调已在OperateAISuggestion类中处理
                        }
                    } catch (error) {
                        // 如果不是取消请求导致的错误，记录错误信息
                        if (error.name !== 'AbortError') {
                            console.error("AI建议请求失败:", error);
                        }
                    } finally {
                        // 清理状态
                        this.aiRequestAbortController = null;
                    }
                }
                
                this.isAITimerRunning = false;
                this.aiSuggestionTimer = null;
            }, this.AI_SUGGESTION_DELAY);
        }
    }

    /**
     * 清除AI计时器
     */
    private clearAITimer(): void {
        if (this.aiSuggestionTimer) {
            clearTimeout(this.aiSuggestionTimer);
            this.aiSuggestionTimer = null;
            this.isAITimerRunning = false;
        }
        
        this.currentAIEnabledRegion = null;
        
        // 同时取消任何进行中的请求
        this.cancelAIRequest();
    }
    
    /**
     * 取消当前正在进行的AI请求
     */
    private cancelAIRequest(): void {
        if (this.aiRequestAbortController) {
            this.aiRequestAbortController.abort();
            this.aiRequestAbortController = null;
        }
    }

    public onKeyDown(event: any, bComposition: boolean): void {
        if (!this.revisionEvent) {
            return;
        }
        this.revisionEvent.hideTips();

        if (!this.keyEvent) {
            return;
        }

        if (this.nisTableEvent.onKeyDown(event) === false) {
            if (isGlobalTestData()) {
                consoleLog('禁止输入非数字');
            }

            this._bCanInput = false;
            return;
        }
        this._bCanInput = true;
        this.setMouseDown(false);
        this.keyEvent.onKeyDown(event, bComposition);
        // 处理AI建议计时器
        this.handleInputEventForAI();
        const keyCode = event.keyCode;
        if (this.nisTableEvent && [8, 46].includes(keyCode)) {
            this.nisTableEvent.onInput(event.key, true);
        }
    }

    public onKeyUp(e: any, bComposition: boolean): void {
        if (!this.keyEvent) {
            return;
        }

        this.keyEvent.onKeyUp(e, bComposition);
        // 处理AI建议计时器
        this.handleInputEventForAI();
    }

    public getCursor(): any {
        return this.cursor.getCursor();
    }

    public getCursor2(): any {
        return this.cursor;
    }

    public isSelected(): boolean {
        return this.mouseEvent.isSelected();
    }

    /**
     * 光标更新，鼠标点击事件进行更新
     * @param pageNode 当前光标父层的svg
     */
    public renderSetCursor( pageNode: any, bSetSelected?: boolean ): void {
        // console.log('blink func')
        if (pageNode) {
            const position = this.documentCore.getCursorPosition();
            // const newPageId = this.getPageIndex();

            if ( position.pageNum === (+pageNode.parentNode.getAttribute('page-index')) ) {
                // this.endPoint = position;
                this.insertCursor(pageNode);

                // restrict cursor when header reaches max domain
                this.restrictCursorPositionAtHeaderMaxHeight(position);

                this.setCursorPosition(position);
                // this.cursorFocused();
                if (this.bPrint !== true) {
                    this.textArea.focus();
                }
                const imageFlags = this.documentCore.getImageFlags();
                if (imageFlags.isImageOnClick || imageFlags.isHandlerOnClick) {
                    this.setCursorVisible(false);
                } else if (bSetSelected) {
                    const bActSelected = this.getSelected();
                    if ( bActSelected) {
                        this.setCursorVisible(false);
                        this.setSelections();
                    } else {
                        this.setCursorVisible(true);
                    }
                } else {
                    this.setCursorVisible(true);
                }

            } else {
                this.mouseEvent.setPageIndex(position.pageNum);
            }
        }
    }

    public resetCursor(): void {
        const position = this.documentCore.getCursorPositionBySetPoint(0, 0, 0);
        this.cursor.setCursorPosition(position);
        this.setCursorVisible(true);
    }

    public clearTipFlag(): void {
        this.newControlEvent?.clearTipFlag();
    }

    public getCursorPosition(): any {
        if (!this.cursor) {
            return;
        }

        return this.cursor.getCursorPosition();
    }

    /**
     * 更新光标位置
     * @bComposition 是否有键盘输入
     * @bForceUpdate 是否需要强制更新：一般只有在对文档内容进行编辑的时候，才需要强制更新
     * @bResetCursor 是否需要重新插入光标，使当前页面重新获取焦点：在操作工具栏后或其他操作，页面焦点会失去
     */
    public updateCursor(bComposition: boolean = false, bForceUpdate: boolean = false,
                        bResetCursor: boolean = false): boolean {
        const documentCore = this.documentCore;
        const position = documentCore.getCursorPosition();
        const pageIndex = this.getPageIndex();

        // tslint:disable-next-line: newline-per-chained-call
        // const curHdrFtr = this.documentCore.getDocument().hdrFtr.getCurHeaderFooter();
        // 键盘移动光标时，判断光标是否需要跨页
        if (pageIndex !== position.pageNum || true === bResetCursor) {
            let newPageNode;
            if (position.pageNum !== pageIndex) { 
                newPageNode = this.getNewPageNode(position.pageNum, position);
                this.setPageIndex(position.pageNum);
                // 进行页面加载，页面加载完再进行光标定位
                // if (!newPageNode) {
                //     // this.changeDocument();
                //     return false;
                // }
            }

            if (null != newPageNode) {
                this.insertCursor(newPageNode);
            }

            // restrict cursor when header reaches max domain
            this.restrictCursorPositionAtHeaderMaxHeight(position);
            // console.log(position)
            this.setCursorPosition(position);
            // this.textArea.focus();
        } else {

            // restrict cursor when header reaches max domain
            this.restrictCursorPositionAtHeaderMaxHeight(position);

            this.setCursorPosition(position);
        }

        const bSelected = this.getSelected();
        if (bSelected !== true) {
            // 强制光标可见
            if (!documentCore.isProtectedMode2()) {
                this.setCursorVisible(true);
            }

            // same SUSPEND
            const imageFlags = this.documentCore.getImageFlags();
            if (imageFlags.isImageOnClick || imageFlags.isHandlerOnClick) {
                // this.cursorBlur();
                this.setCursorVisible(false);
            }
        } else {
            this.setCursorVisible(false);
        }

        if (documentCore.isInlineMode()) {
            setTimeout(() => {
                this.focus();
            }, 0);
        } else {
            this.focus();
        }

        if (true === bForceUpdate) {
            // this.changeDocument();
            // isSelected
            /* IFTRUE_WATER */
            // 重刷水印，保持位置
            this.documentCore.resetCorePosition(true);
            /* FITRUE_WATER */
            this.changeDocument()
            .then(() => {
                if (bSelected === true) {
                    this.setSelections();
                } else if (0 >= position.y1) {
                    const newPosition = documentCore.getCursorPosition();
                    position.x = newPosition.x;
                    position.y1 = newPosition.y1;
                    position.y2 = newPosition.y2;
                    this.setCursorPosition(position);
                }
            });
        }

        return bForceUpdate;
    }

    public refreshPages(options?: any): void {
        if (true) {
            const refs = this._pageRefs;
            if (!refs) {
                this.updateCursor(true, true, true);
                return;
            }
            const documentCore = this.documentCore;
            const position = documentCore.getCursorPosition();
            let pageIndex = position.pageNum;
            const pageCount = documentCore.getPageCount();
            const pageKeys: any[] = Object.keys(refs);
            if (pageKeys[pageKeys.length - 1] >= pageCount) {
                this.updateCursor(true, true, true);
                return;
            }
            let pageRef = refs[pageIndex];
            // 当前页都不存在的情况下得全局刷新，否则不显示新的一页
            if (!pageRef || !pageRef.current) {
                this.updateCursor(true, true, true);
                return;
            }
            // 当前页刷新
            let res = pageRef.current.refresh(options);
            if (!res) {
                pageRef.current.refresh({bNextPage: true});
            }
            let bNextPage: boolean = false;
            if (typeof res === 'object') {
                if (res.bRefreshAll === true) {
                    this.updateCursor(true, true, true);
                    return;
                }
                bNextPage = res.bNextPage;
                if (res.bPrevPage === true && res.ids) {
                    pageRef = refs[pageIndex - 1];
                    if (pageRef && pageRef.current) {
                        pageRef.current.refresh(res);
                    }
                } else if (bNextPage && res.ids) { // 当输入的内容所在的区域跨页时，会进行上下页所在的区域进行刷新
                    pageRef = refs[++pageIndex];
                    if (pageRef && pageRef.current) {
                        res = pageRef.current.refresh(res);
                    }
                    if (res) {
                        bNextPage = res.bNextPage;
                    }
                    // 当输入内容所在页跨页，并且当前光标在下一页，那么当前区域的上一页也需要更新
                }
            }

            // 假如上面的页面刷新了，还需要往后刷，那么会进行延迟刷新
            if (bNextPage === true) {
                if (this._refPages === undefined) {
                    this._refPages = [];
                }
                const pages = this._refPages;
                pageIndex++;
                pageKeys.forEach((value) => {
                    if (pageIndex <= +value) {
                        pages.push({pageIndex: +value});
                    }
                });
                // for (let index = pageIndex, len = pageKeys.length; index < len; index++) {
                //     pageRef = refs[index];
                //     if (!pageRef || !pageRef.current) {
                //         break;
                //     }
                //     if (pages.find((page) => page.pageIndex === index)) {
                //         continue;
                //     }
                //     pages.push({pageIndex: index});
                // }
                if (pages.length > 0) {
                    clearTimeout(this._refTimeout);
                    this._refTimeout = setTimeout(() => {
                        pages.forEach((page) => {
                            pageRef = refs[page.pageIndex];
                            if (!pageRef || !pageRef.current) {
                                return;
                            }
                            pageRef.current.refresh({bNextPage: true});
                        });
                        this._refPages = [];
                    }, 200);
                } else if (0 === pages.length && !pageKeys.includes(pageIndex.toString()) && pageIndex < pageCount) {
                    this.updateCursor(true, true, true);
                    return;
                }
            }

            let newPageNode;
            const oldPageIndex = this.getPageIndex();
            if (position.pageNum !== oldPageIndex) {  
                newPageNode = this.getNewPageNode(position.pageNum, position);
                if (newPageNode) {
                    this.setPageIndex(position.pageNum);
                    this.insertCursor(newPageNode);
                }
            } else {
                const newPosition = documentCore.getCursorPosition();
                position.x = newPosition.x;
                position.y1 = newPosition.y1;
                position.y2 = newPosition.y2;
            }

            // 重新设置光标
            this.setCursorPosition(position);
            if (this.isCusorVisible() === false) {
                this.setCursorVisible(true);
            }
            if (documentCore.isSelectionUse()) {
                this.setSelections();
            } else {
                this.clearSection();
            }
            return;
        }

        this.updateCursor(true, true, true);
    }

    /**
     * 限制多次UI刷新
     * @param flag 是否当前进行刷新
     */
    public updateRefreshFlag = (flag: boolean): void => {
        this._bRefresh = flag;
    }

    /**
     * 针对键盘输入触发多个地方更新的问题
     * 当前点已经有刷新触发，不需要再进行多次刷新
     * 当需要进行刷新时得触发updateRefreshFlag2
     */
    public isNoRefresh = (): boolean => {
        return this._bRefresh === false;
    }

    /**
     * 针对键盘输入触发多个地方更新的问题
     * @param flag 
     */
    public updateRefreshFlag2 = (flag: boolean, ): void => {
        this._bRefresh2 = flag;
    }

    /**
     * 全局更新，内容，光标进行更新
     * @param bRefreshFlag 
     * @returns 
     */
    public refresh = (bRefreshFlag?: boolean): Promise<boolean> => {
        if (bRefreshFlag === true) {
            this._bRefresh = true;
        }
        if (this._bRefresh === false) {
            return Promise.resolve(false);;
        }
        const flag = this.updateCursor(true, true, true);
        setTimeout(() => {
            this.tableEvent?.showRowSettingBtn();
        }, 10);
        if (flag !== true) {
            return this.changeDocument();
        } else {
            return Promise.resolve(false);
        }
    }

    public refreshByAsync(): Promise<boolean> {
        return this.changeDocument();
    }

    public handleRefresh = (): void => {
        this.refresh();
        // this.setState({});
    }

    public getKeyEvent(): any {
        return this.keyEvent?.getKeyEvent();
    }

    public getKeyBoardEvent(): any {
        return this.keyEvent?.getKeyBoardEvent();
    }

    public testDocumentXml = (bInsertFile: boolean = false) => {
        if (!bInsertFile) {
            this.changeDocument();
            this.initCursorPos();
            this.clearSection();
            return;
        } else {
            this.updateCursor(false, false, true);

            // recomputeRowHeights() may not properly trigger rerender,
            // thus causing 'position: absolute' has no 'position: relative' parent node
            if (this.dynamicModeRefresh() === true) {
                return ;
            }
        }
        this.changeDocument();
    }

    public dynamicModeRefresh(): boolean {
        // if (this.documentCore.getDynamicHeightMode()) {
        //     const innerScrollContainer = this.currentRef.current
        //             .querySelector('.ReactVirtualized__Grid__innerScrollContainer');
        //     if (innerScrollContainer != null) {
        //         innerScrollContainer.style.position = 'static';

        //         this.setState({}, () => {
        //             setTimeout(() => {
        //                 innerScrollContainer.style.position = 'relative';
        //             }, 0);
        //         });
        //         return true;
        //     }
        // }
        return false;
    }

    public forceUpdate2(): void {
        this.documentCore.removeSelection();
        this.clearSection();
        this.initCursorPos();
        this.changeDocument();
    }

    public updateCursor2(): void {
        // this.setCursorVisible(false);
        this.changeDocument();
    }

    public setCursorByXY(x: number, y: number, event: any): void {
        this.mouseEvent.setCursorByXY(x, y, event);
    }

    public scrollToTop(position: number = 0): void {
        if (this.list.current && this.list.current.scrollTo) {
            this.list.current.scrollTo(position);
        }
    }

    public setScrollPageVisible(): void {
        if (this._dragScrollFlag !== true) {
            return;
        }
        this._dragScrollFlag = false;
        this._scrollPageDom.style.display = 'none';
    }

    public changeDocument(): Promise<boolean> {
        return new Promise((resolve, reject) => {
            const record = getDocumentCoreRecorder();
            if (record.isRecording()) {
                record.record({
                    external: true,
                    method: 'documentRefresh',
                    args: [],
                });
            }
            SPELL_CHECK.clearAll();
            this.setState({ bRefresh: !this.state.bRefresh}, () => {
                SPELL_CHECK.render();
                resolve(true);
            });
        });
    }

    public resetMovingTableNewBorder(): void {
        if ( this.documentCore.isMovingTableBorder() ) {
            this.documentCore.resetMovingTableNewBorder();
            this.changeDocument();
        }
    }

    /**
     * 清除当前的输入任务队列
     */
    public clearTaskList(): void {
        const list = this.textArea ? this.textArea.getTaskList() : null;
        if (list && list.length) {
            list.forEach((value) => {
                clearTimeout(value);
            });
            list.length = 0;
        }
    }

    public isMorePage(): boolean {
        return this._total !== undefined && this._total !== this._totalPage;
    }

    public get totalPage(): number {
        return this._totalPage;
    }

    /**
     * tab跳转时，保持focus
     */
    public keepFocusForJumpTab(): void {
        // if (this.isSelected()) {
        //     this.textArea.focus();
        //     this.setCursorVisible(false);
        //     this.setSelections();
        // }
    }

    public getSelected(): boolean {
        // if (this.documentCore.getDocumentSelection().bUse) {
        //     return true;
        // }

        return this.documentCore.isSelectionUse();
    }

    public canSetCursorVisible(): boolean {
        return !(this.documentCore.isProtectedMode() ||
            (this.documentCore.isStrictMode2() &&
                !(this.documentCore.isCursorInNewControl() || this.documentCore.isCursorInRegion())));
    }

    /**
     * 无按键，鼠标，滚动操作，10秒后开始发送
     */
    public startSendMonitorData = (): void => {
        if (this.documentCore.getElementMonitor()) {
            clearTimeout(this.sendMonitorData);
    
            this.sendMonitorData = setTimeout(() => {
                this.documentCore.sendMonitorData();
            }, 5000);
        }
    }

    public resetTextArea() {
        this.textArea.reset();
    }

    private keyDownInputContent(content?: string): void {
        this.newControlEvent.setRegionDirty();
    }

    private onResize = (): void => {
        // console.log(333)
        this.clearContainer();
        this.changeDocument();
    }

    private setOnly = (): void => {
        //add by tinyzhi 暂时屏蔽setonly 功能
        //this.documentCore.protectDoc2(true);
    }

    private onRowsRendered = (obj: any): void  => {
        if (!this.mouseEvent) {
            return;
        }
        this.setSelections();
        const position = this.cursor.getCursorPosition();
        this.newControlEvent.renderBackground(position);
        // const pageIndex = position.pageNum;
        // if (pageIndex >= obj.startIndex && pageIndex <= obj.stopIndex && this.isSelected() !== true ) {
        //     this.scrollInsertCursor(position);
        // }
    }

    private scrollToPosition(position: any): void {
        if (!position || this._itemSize === undefined) {
            return;
        }
        // const pageProperty = this.documentCore.getPageProperty();
        const height = this._itemSize + 20;
        let top: number = 0;
        if (position.pageNum < this.getPageIndex()) {
            top = position.pageNum * height + position.y2 - 30;
        } else {
            top = position.pageNum * height + position.y2 - this._height + 30;
        }

        this.list.current.scrollTo(top);
        // this.currentRef.current.firstChild.scrollTop = top + 'px';
        if (this.bUpdatePage === false) {
            setTimeout(() => {
                this.bUpdatePage = true;
                this.mouseEvent.setPageIndex(position.pageNum);
                const pageNode = this.getNewPageNode(this.currentIndex);
                this.renderSetCursor(pageNode, true);
            }, 0);
        }
    }

    private dragScroll = (index: number, itemCount: number, scrollTop: number, oldScrollTop: number) => {
        const listRef = this.list.current;
        const externalEvent = this.host.externalEvent;
        // add scrollInEnd event
        if (externalEvent && listRef && listRef._outerRef ) {
            const listDiv = listRef._outerRef;
            const { clientHeight, scrollHeight } = listDiv;
            const bottomDistance = scrollHeight - clientHeight - scrollTop;
            const oldBottomDistance = scrollHeight - clientHeight - oldScrollTop;
            if (oldBottomDistance > 10 && bottomDistance <= 10) {
                // reach the end
                if (typeof externalEvent.nsoScrollReachEnd === 'function') {
                    externalEvent.nsoScrollReachEnd();
                }
            } else if (bottomDistance > 10 && oldBottomDistance <= 10) {
                // leave the end
                if (typeof externalEvent.nsoScrollLeaveEnd === 'function') {
                    externalEvent.nsoScrollLeaveEnd();
                }
            }
        }

        if (!this.mouseEvent.isMousdown()) {
            return;
        }
        // index++;
        let dom = this._scrollPageDom;
        if (this._dragScrollFlag === true) {
            // this.setScrollPageY(scrollTop, oldScrollTop);
            // dom.innerHTML = `第${index}/总${itemCount}页`;
            return;
        }

        if (!dom) {
            dom = document.createElement('div');
            this._scrollPageDom = dom;
            dom.className = 'scroll-page';
            // const span = this._scrollPagePositionDom = document.createElement('span');
            // span.className = 'scroll-page-position';
            const container = this.currentRef.current;
            // container.firstChild.appendChild(span);
            container.appendChild(dom);
            this.addDragScrollEvent();
        } else {
            if (dom.style.display === 'none') {
                dom.style.display = '';
            }
        }

        if (this.isMorePage()) {
            index = 2 * index;
        }
        // this.setScrollPageY(scrollTop, index);
        dom.innerHTML = `第${index + 1}/总${this._totalPage}页`;
        this._dragScrollFlag = true;
    }

    private setScrollPageY(scrollTop: number): void {
        const current = this.list.current;
        const itme = current.getItemOption();
        const itemCount = itme.itemCount;
        if (itemCount < 2) {
            return;
        }
        const itemSize = itme.itemSize;
        let currentIndex = this.list.current.getItemIndexByPosition(scrollTop) + 1;
        if (itemCount < currentIndex && scrollTop - (currentIndex * itemSize) > itemSize *  3 / 4) {
            currentIndex++;
        }

        if (this.isMorePage()) {
            currentIndex = 2 * currentIndex - 1;
        }
        this._scrollPageDom.innerHTML = `第${currentIndex}/总${this._totalPage}页`;
    }

    private addDragScrollEvent(): void {
        this.currentRef.current.firstChild.addEventListener('scroll', this.dragScrollEvent);
    }

    private removeDragScrollEvent(): void {
        if (!this._scrollPageDom) {
            return;
        }
        this.currentRef.current?.firstChild?.removeEventListener('scroll', this.dragScrollEvent);
    }

    private dragScrollEvent = (e): void => {
        // console.log(e)
        if (this._dragScrollFlag !== true) {
            return;
        }
        // this.dragScrollEventTime(e);
        // this.setScrollPageMove(e.target.scrollTop);
        setTimeout(() => {
            this.setScrollPageY(e.target.scrollTop);
        }, 20);
    }

    // private dragScrollEventTime = (e: any): void => {
    //     setTimeout(() => {
    //         const dom = this._scrollPageDom;
    //         let newTop = e.target.scrollTop;
    //         span.style.top = top + 'px';
    //         newTop = span.getBoundingClientRect().top;
    //         top = newTop - top;
    //         const y = parseInt(dom.style.top, 10);
    //         console.log(top + y, y, top, e.target.scrollTop);
    //         dom.style.top = (y + top) + 'px';
    //     }, 50);
    // }

    // private dragScrollEvent = (e): void => {

    // }

    private onScroll = (obj: any): void => {
        // console.log(obj)
        // if (this.bUpdatePage !== false) {
        //     // this.currentIndex = undefined;
        //     return;
        // }
        // console.log(1)
        // setTimeout(() => {
        //     this.bUpdatePage = true;
        //     const pageNode = this.getNewPageNode(this.currentIndex);
        //     this.renderSetCursor(pageNode, true);
        // })
        // const pageNode = this.getNewPageNode(this.currentIndex);
        // this.renderSetCursor( pageNode, true);
        // this.setState({});

        if (this.newControlEvent) {
            this.newControlEvent.onScroll();
        }

        if ( this.documentCore.isSelectionUse() && !this.documentCore.isMovingTableBorder() ) {
            this.setSelections();
        }

        if (1 === this.documentCore.isRecalcPages()) {
            this.setState({ bRefresh: !this.state.bRefresh});
            this.documentCore.resetRecalcPages();
        }

        this.startSendMonitorData();

        SPELL_CHECK.onScroll(obj.pageIndexs);
    }

    private getNewPageNode(pageNum: number, position?: any): SVGElement {
        let dom = this.listDom;
        if (!dom) {
            this.listDom = dom = this.currentRef.current.firstChild.firstChild;
        }
        let svg = dom.querySelector(`.page-wrapper[page-index='${pageNum}'] > svg`) as SVGElement;
        if (!svg) {
            this.bUpdatePage = false;
            this.scrollToPosition(position);
            this.currentIndex = pageNum;
            svg = dom.querySelector(`.page-wrapper[page-index='${pageNum}'] > svg`) as SVGElement;
        } else if (this.textArea.getTextAreaElem()) {
            setTimeout(() => {
                this.textArea?.focus();
            }, 0);
        }
        return svg;
    }

    private insertCursor = (pageNode: SVGElement, pageId?: number) => {
        if (pageId === undefined) {
            pageId = this.getPageIndex();
        }
        const flag = this.cursor.insertCursor(pageNode, pageId);
        // if (flag === true) {
        //     pageNode.parentNode.appendChild(this.textArea.wrapper());
        // }
    }

    private initCursorPos(): void {
        let position: any;
        if (this.props.unResizeCursor === true) {
            position = {
                x: 120,
                y1: 99,
                y2: 118,
                pageNum: 0,
            };
        } else {
            const documentCore = this.documentCore;
            position = documentCore.getCursorPositionBySetPoint(0, 0, 0);
        }
        this.scrollToTop();
        this.setPageIndex(0);

        const newPageNode = this.getNewPageNode(position.pageNum, position);
        // this.textArea.insertHiddenTextArea(this.currentRef.current, newPageNode);
        if (null != newPageNode) {
            this.cursor.insertCursor(newPageNode, position.pageNum);

            // restrict cursor when header reaches max domain
            this.restrictCursorPositionAtHeaderMaxHeight(position);

            this.setCursorPosition(position);
            this.cursor.cursorFocused();
            this.textArea.focus();
            // this.setCursorVisible(true);
        } else {
            // this.bUpdatePage = false;
            // this.setCursorVisible(true);
        }
    }

    private localMeasureText(text: string, text2: string, fontFamily: string, fontSize: number): number {
        let textWidth = 0;
        const textRects = measure(text, {fontFamily, fontSize});
        if (textRects) {
            textRects.forEach(rect => {
                textWidth += rect.width;
            });
        }

        let text2Width = 0;
        if (text2) {
            const text2Rects = measure(text2, {fontFamily, fontSize});
            if (text2Rects) {
                text2Rects.forEach(rect => {
                    text2Width += rect.width;
                });
            }
        }
        return textWidth < text2Width ? text2Width : textWidth;
    }

    /**
     * 渲染所有页样式以及当前页面内容
     */
    // tslint:disable-next-line: typedef
    private renderPages = ({index, isVisible, style, startIndex }) => {
        // 只渲染可视区域的页面
        if (true !== isVisible) {
            return null;
        }

        const documentCore = this.documentCore;
        // const { pageProperty, total, pageProps } = this._renderedPageInfo;
        if ( this._total <= index || documentCore.getPageCount() !== this._totalPage) {
            return null;
        }

        let arrs: any;
        let htmlStyle;
        let pageIndex: number;
        let className: string;
        if (this._total === undefined || this._total === this._totalPage) {
            const res = this.renderPage(index, style, startIndex);
            className = 'page-wrapper';
            pageIndex = index;
            if (res) {
                arrs = res.dom;
                htmlStyle = res.option.htmlStyle;
            }
        } else {
            arrs = [];
            let curIndex = index * 2;
            const oldIndex = curIndex;
            let pageWidth = 0;
            className = 'more-page';
            for (let len = Math.min(curIndex + 2, this._totalPage); curIndex < len; curIndex++) {
                const res = this.renderPage(curIndex, style, startIndex);
                if (res) {
                    htmlStyle = res.option.htmlStyle;
                    pageWidth += htmlStyle.width;
                    const backgroundImage = htmlStyle.backgroundImage;
                    delete htmlStyle.backgroundImage;
                    arrs.push(<div key={curIndex} className='page-wrapper' page-index={curIndex} style={{maxWidth: htmlStyle.width, backgroundImage}}>{res.dom}</div>);
                }
            }
            
            if (curIndex - 2 === oldIndex) {
                pageWidth += 15;
            } 
            // else if (this._bAsyncLoadComment) {
            //     pageWidth = 2 * pageWidth + 15;
            // }
            pageIndex = oldIndex;
            htmlStyle.width = pageWidth;
        }
        
        

        return (
            <div
                key={index}
                page-index={pageIndex}
                style={htmlStyle}
                className={className}
            >
               {arrs} 
            </div>
        );
        // return arrs;
        // {this.renderComboxList(index)}
        //         {this.renderComboxDropButton(index)}
        //         {this.renderDateList(index)}
        //         {this.renderDateDropButton(index)}
    }

    private renderPage(index: number, style: any, startIndex: number): {dom: any, option: any} {
        const { pageProperty, total, pageProps } = this._renderedPageInfo;
        const documentCore = this.documentCore;
        
        this._pageRefs[index] = React.createRef();

        // ----------------  compute header height
        const [contentTopY, contentBottomY] = this.getContentHeights(documentCore, index, pageProperty);

        const container = this.host.myRef.current;
        const pageHeight = pageProperty.height * this._scale;
        let pageWidth: number;
        let height = 20;
        if (this.viewMode === ViewModeType.WebView || documentCore.isInlineMode()) {
            height = 0;
        }
        const htmlHeight = style.height - height;
        if (this.viewMode === ViewModeType.CompactView) {
            if (documentCore.isStartScale()) {
                pageWidth = numtoFixed(pageProperty.width, 0);
            } else {
                pageWidth = numtoFixed(this._pageWidth * this._scale, 0);
            }
            // htmlHeight = pageHeight = numtoFixed(this._pageHeight * this._scale, 0);
        } else {
            pageWidth = parseInt((pageProperty.width * this._scale) as any, 10);
        }

        // TODO: min width & height check!!! (since custom width & height)

        // watermark start
        if (!this.backgroundImage) {
            const editorBackground: IEditorBackground = documentCore.getEditorBackground();
            // console.log(editorBackground);
            const {watermarkEnabled, watermarkType, watermarkText, fillColorEnabled, fillColorType} = editorBackground;
            let fillColor = FILL_COLOR[FillColorType.White];
            let watermarkRate = 2;
            let wmText = 'hello world';
            let wm2Text = '';
            let wmSize = WATERMARK_DEFAULT_FONTSIZE;
            let wm2Size = WATERMARK_DEFAULT_FONTSIZE;
            let show2Wm = false; // double safety
            if (watermarkType != null) {
                if (watermarkType === WatermarkType.Tight) {
                    watermarkRate = 6;
                } else {
                    watermarkRate = 3;
                }
            }
            // console.log(watermarkText)
            if (watermarkText != null && watermarkText.length > 0) {
                // wmText = watermarkText.slice(0, 10)
                wmText = this.escapeWatermark(watermarkText[0].text);
                wmSize = +watermarkText[0].size;

                if (watermarkText[1] != null) {
                    wm2Text = this.escapeWatermark(watermarkText[1].text);
                    wm2Size = +watermarkText[1].size;
                    show2Wm = true;
                }
            }
            // console.log(wmSize, wm2Size)
            if (fillColorType != null) {
                switch (fillColorType) {
                    case FillColorType.White: {
                        fillColor = FILL_COLOR[FillColorType.White];
                        break;
                    }
                    case FillColorType.Granite: {
                        fillColor = FILL_COLOR[FillColorType.Granite];
                        break;
                    }
                    case FillColorType.Ceramic: {
                        fillColor = FILL_COLOR[FillColorType.Ceramic];
                        break;
                    }
                    case FillColorType.Plain: {
                        fillColor = FILL_COLOR[FillColorType.Plain];
                        break;
                    }
                    default: {
                        break;
                    }
                }
            }
            // console.log(pageWidth, pageHeight)
            // tslint:disable-next-line: max-line-length
            const defaultTemplate = `<svg xmlns='http://www.w3.org/2000/svg' width='${pageWidth}' height='${pageHeight}'><rect width='${pageWidth}' height='${pageHeight}' style="fill: white" /></svg>`;
            // // tslint:disable-next-line: max-line-length
            // const fillColorTemplate = `<svg xmlns='http://www.w3.org/2000/svg' width='${pageProps.width}' height='${pageProps.height}'><rect width='${pageProps.width}' height='${pageProps.height}' style="fill: ${fillColor}" /></svg>`;

            if (fillColorEnabled === false) {
                fillColor = FILL_COLOR[FillColorType.White];
            }
            // // tslint:disable-next-line: max-line-length
            // const watermarkTemplate = `<svg xmlns='http://www.w3.org/2000/svg' width='${pageProps.width}' height='${pageProps.height}'><defs><pattern id="textstripe" patternUnits="userSpaceOnUse" width='${pageProps.width / 3}' height='${pageProps.height / watermarkRate}' x='0' y='0' patternTransform="rotate(-45)"><text x="0" y="30" font-size="40" fill='#ede9df'>${wmText}</text></pattern></defs><rect width='${pageProps.width}' height='${pageProps.height}' style="fill: ${fillColor}" /><rect width='${pageProps.width}' height='${pageProps.height}' style="fill: url(#textstripe);" /></svg>`;

            let currentTemplate = defaultTemplate;

            const {wmColor, wmOpacity} = this.getWatermarkFontProps();

            if (watermarkEnabled === true) {
                // width='${pageWidth / 3}' feel good
                // mesure dynamic width
                const fontFamily = '黑体';
                let text2 = '';
                if (wm2Text) {
                    text2 = `<text x="10" y="${wmSize + 60}" font-family="${fontFamily}" font-size="${wm2Size}" fill='${wmColor}' style='${wmOpacity}display: ${show2Wm === true ? 'block' : 'none'}'>${wm2Text}</text>`;
                }
                let text1 = ``;
                if (wmText) {
                    text1 = `<text x="10" y="60" font-family="${fontFamily}" font-size="${wmSize}" fill='${wmColor}' style='${wmOpacity}'>${wmText}</text>`;
                }
                const rectHeight = contentBottomY - contentTopY;
                const textWidth = this.localMeasureText(wmText, wm2Text,
                                     fontFamily, wmSize < wm2Size ? wm2Size : wmSize) + 10;
                // tslint:disable-next-line: max-line-length
                currentTemplate =
                `<svg xmlns='http://www.w3.org/2000/svg' width='${pageWidth}' height='${pageHeight}'>
                    <defs>
                        <pattern id="textstripe" patternUnits="userSpaceOnUse" width='${textWidth < pageWidth / 3 ? pageWidth / 3 : textWidth}' height='${pageHeight / watermarkRate}' x='0' y='0' patternTransform="rotate(-30)">
                            ${text1}
                            ${text2}
                        </pattern>
                    </defs>
                    <rect width='${pageWidth}' height='${pageHeight}' style="fill: white" />
                    <rect y='${contentTopY}' width='${pageWidth}' height='${rectHeight}' style="fill: ${fillColor}" />
                    <rect y='${contentTopY}' width='${pageWidth}' height='${rectHeight}' style="fill: url(#textstripe);" />
                </svg>`;
                // currentTemplate = watermarkTemplate;
            } else if (fillColorEnabled === true) {
                // tslint:disable-next-line: max-line-length
                currentTemplate = `<svg xmlns='http://www.w3.org/2000/svg' width='${pageWidth}' height='${pageHeight}'>
                    <rect width='${pageWidth}' height='${pageHeight}' style="fill: white" />
                    <rect y='${contentTopY}' width='${pageWidth}' height='${contentBottomY - contentTopY}' style="fill: ${fillColor}" />
                </svg>`;
                // currentTemplate = fillColorTemplate;
            }
            // console.log(currentTemplate)
            this.backgroundImage = window.btoa(unescape(encodeURIComponent(currentTemplate)));
        }
        const base64Mark = this.backgroundImage;
        // const leftPos = (document.documentElement.clientWidth - pageProperty.width) / 2;
        const htmlStyle = {
            ...style,
            width: pageWidth,
            height: htmlHeight,
            // top: style.top,
            // height: (style.height - height),

            backgroundImage: `url("data:image/svg+xml;base64,${base64Mark}")`,
        };

        const theme = getTheme();
        const regionOperatorStyle = theme.NewControl.ShowRegionOperator ? {} : {display: 'none'};
        const btnTooltipStyle = {
            color: theme.NewControl.DefaultMessageTipColor,
            backgroundColor: theme.NewControl.DefaultMessageTipBackgroundColor
        };
        const contents = [];
        const isWebView = (ViewModeType.WebView !== documentCore.getViewMode());
        contents[0] = documentCore.getContentByPageId(index);
        if (isWebView) {
            if (this._header && index !== 0) {
                contents[1] = !documentCore.isSetHeadFooterInterface() ? this._header
                                : documentCore.getContentByPageId(index, 1);
            } else if (documentCore.hasHeaderFooter(true)) {
                contents[1] = documentCore.getContentByPageId(index, 1);
                if (index !== 0) {
                    this._header = contents[1];
                }
            }
            if (this._footer && index !== 0) {
                contents[2] = this._footer;
            } else if (documentCore.hasHeaderFooter(false)) {
                contents[2] = documentCore.getContentByPageId(index, 2);
                if (index !== 0) {
                    this._footer = contents[2];
                }
            }
        }

        let otherDom: any;
        if (this.bPrint !== true) {
            let rgClassName = 'region-btn';
            const regionIcon = theme.NewControl.RegionCustomIcon || 'default';
            rgClassName += regionIcon === 'default' ? ' default' : ' iconfont ' + regionIcon;
            otherDom = (
                <React.Fragment>
                    {/* <NewControlLayer host={this} pageIndex={index} /> */}
                    <NISTableLayer host={this} pageIndex={index} />
                    {/* <span className='view-mode-span'/> */}
                    <button className={rgClassName} style={regionOperatorStyle} tabIndex={-1}><span className='btn-tooltip' style={btnTooltipStyle}>点击打开菜单</span></button>
                </React.Fragment>
            );
        }
        return {dom: (
            <React.Fragment>
                <Page
                    key={index}
                    ref={this._pageRefs[index]}
                    {...pageProps}
                    dynamicHeight={this._height}
                    index={index}
                    showPageBorder={true}
                    contents={contents}
                    editorContainer={container}
                    documentCore={documentCore}
                    handleRefresh={this.handleRefresh}
                    host={this}
                    minPageIndex={startIndex}
                    cursorType={documentCore.getCursorType()}
                    _bCascadeManage={this.isCascadeManager()}
                />
                {otherDom}
            </React.Fragment>
        ), option: {
            htmlStyle
        }};
    }

    private getWatermarkFontProps(): IWatermarkFontProps {
        const result = {
            wmColor: WATERMARK_COLOR,
            wmOpacity: ''
        };

        const editorBackground: IEditorBackground = this.documentCore.getEditorBackground();
        const {fillTextColor} = editorBackground;

        switch (fillTextColor) {
            case FillTextColor.Default: {
                // tencent color
                result.wmColor = WATERMARK_COLOR2;
                result.wmOpacity = 'opacity: 15%; ';
                break;
            }
            case FillTextColor.Normal: {
                // ui normal
                result.wmColor = WATERMARK_COLOR2;
                result.wmOpacity = 'opacity: 25%; ';
                break;
            }
            case FillTextColor.Dark: {
                // ui dark
                result.wmColor = WATERMARK_COLOR2;
                result.wmOpacity = 'opacity: 35%; ';
                break;
            }
            case FillTextColor.DeepDark: {
                result.wmColor = WATERMARK_COLOR2;
                result.wmOpacity = 'opacity: 50%; ';
                break;
            }
            default: {
                break;
            }
        }
        return result;
    }

    private escapeWatermark(waterMarkText: string): string {
        let result = waterMarkText;
        if (waterMarkText != null) {
            result = result.replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/'/g, '&apos;')
            .replace(/"/g, '&quot;');
        }

        return result;
    }

    private getPageRowHeight = (indexObj: {index: number}): number => {
        // row ~ page
        const lastPageIndex = this.documentCore.getPageCount() - 1;
        const { pageProperty } = this.documentCore.render();
        if (this.documentCore.getDynamicHeightMode() && indexObj.index === lastPageIndex) {
            return this.getLastPageRowHeight() * this._scale;
        } else {
            const viewMode = this.viewMode;
            let height: number;
            if (viewMode === ViewModeType.WebView) {
                const page = this.documentCore.getPagePositionInfo(indexObj.index);
                height = (page.height - page.y - (page.height - page.yLimit)) * this._scale;
                // const sectionPageMargins = this.documentCore.getSectionPageMargins();
                // height = pageProperty.height - sectionPageMargins.paddingTop - sectionPageMargins.paddingBottom;
                // } else if (viewMode === ViewModeType.CompactView) {
                //     height = (pageProperty.height - pageProperty.paddingTop - pageProperty.paddingBottom
                //         ) * this._scale + 20;
            } else {
                height = (pageProperty.height) * this._scale + 20;
            }
            return height;
        }
    }

    private getLastPageRowHeight(): number {
        const logicDocument = this.documentCore.getDocument();
        const { pageProperty } = this.documentCore.render();

        // get bottom pos of last elem of last page
        const lastDocumentContent: any = logicDocument.content[logicDocument.content.length - 1];

        let lastPara = lastDocumentContent;
        // region need extra care. Table is ok.
        if (lastDocumentContent && lastDocumentContent.isRegion()) {
            const lastParaArray = lastDocumentContent.getContent();
            if (lastParaArray.length > 0) {
                lastPara = lastParaArray[lastParaArray.length - 1];
            }
        }
        if (lastPara != null) {
            const lastParaPage = lastPara.pages[lastPara.pages.length - 1];
            if (lastParaPage != null) {
                const lastPageYLimit = lastParaPage.bounds.bottom;
                if (lastPageYLimit != null) {
                    return lastPageYLimit + 90;
                }
            }
        }

        return pageProperty.height + 20;
    }

    private checkIframeHeight(innerHeight: number): void {
        // return;
        const height = innerHeight.toFixed(0);
        const iframe = this.props.iframe;
        // console.log(iframe)
        if (this.documentCore.getDynamicHeightMode() ) {
            if (iframe != null && iframe.style.height !== height + 'px') {
                this.props.iframe.style.height = height + 'px';
            }
        } else {
            if (iframe != null && iframe.style != null) {
                this.props.iframe.style.height = '';
            }
        }
    }

    private dyNamicModeRecomputeRowHeight(): void {
        // TODO: better performance?
        // if (dynamicHeightEnabled.value || (dynamicHeightEnabled.old && !dynamicHeightEnabled.value)) {
        if (this.documentCore.getDynamicHeightMode() ) {
            this.list.current.recomputeRowHeights(this.documentCore.getPageCount() - 1);
        } else {
            this.list.current.recomputeRowHeights();
        }
    }

    private restrictCursorPositionAtHeaderMaxHeight(position: ICursorProperty): void {
        // restrict cursor when header reaches max domain

        const documentCore = this.documentCore;

        if (documentCore.isInHeaderFooter()) {
            const logicDocument = documentCore.getDocument();
            const curHdrFtr = logicDocument.hdrFtr.getCurHeaderFooter();

            if (curHdrFtr != null && curHdrFtr.isHeaderFooter(false)) {
                const curHdrFtrContent = curHdrFtr.content;
                const restoredYLimit = curHdrFtrContent.yLimit / 10;
                if (restoredYLimit !== 0 && (position.y1 > restoredYLimit || position.y2 > restoredYLimit)) {

                    // approximate para index, may not be accurate
                    // tslint:disable-next-line: max-line-length
                    // let lastParaInHeaderAreaIndex = curHdrFtrContent.getContentPosByXY(position.x, restoredYLimit, curHdrFtrContent.curPage);

                    // get last index of content, not getting para index by getContentPosByXY() like above
                    // reason: the mechanism is 'empty para is inserted before', thus while inserting EMPTY PARA,
                    // if para index is retrieved by getContentPosByXY(), then it points to the inserted new EMPTY para
                    // but the new para's .curPos is not recalcCurpos() yet, thus stuck.
                    let lastParaInHeaderAreaIndex = curHdrFtrContent.content.length - 1;

                    let lastParaInHeaderArea = curHdrFtrContent.content[lastParaInHeaderAreaIndex];
                    let headerYLimit = Math.round(lastParaInHeaderArea.getCurPos().y);

                    let i = 0;
                    while (headerYLimit + 5 > restoredYLimit) { // why + 5? not sure, in document.ts
                        lastParaInHeaderAreaIndex--;
                        if ( -1 >= lastParaInHeaderAreaIndex ) {
                            break;
                        }

                        lastParaInHeaderArea = curHdrFtrContent.content[lastParaInHeaderAreaIndex];
                        headerYLimit = Math.round(lastParaInHeaderArea.getCurPos().y);

                        // 空段落回车，新加的段会插在最后一段之前，且.curpos未设过
                        if (headerYLimit === 0) {
                            const originalCurPos = curHdrFtrContent.curPos.contentPos;
                            curHdrFtrContent.curPos.contentPos = lastParaInHeaderAreaIndex;
                            curHdrFtrContent.updateCursorXY();
                            curHdrFtrContent.curPos.contentPos = originalCurPos;
                            headerYLimit = Math.round(lastParaInHeaderArea.getCurPos().y);
                        }

                        i++;
                        if (i >= 10000) {
                            // tslint:disable-next-line: no-console
                            console.warn('loop exceeds 10000 times');
                            break;
                        }
                    }
                    const headerXLimit = Math.round(lastParaInHeaderArea.getCurPos().x);

                    // TODO: height may not be good way
                    const height = position.y2 - position.y1;
                    position.y2 = headerYLimit + 5;
                    position.y1 = position.y2 - height;
                    position.x = headerXLimit; // always at para end
                    // position.y2 = restoredYLimit;
                    // position.y1 = restoredYLimit - height;
                    // console.log(position)
                }
            }
        }
    }

    private shouldCheckIframeHeight(height: number): boolean {
        // also catch // false -> true, true -> false
        const documentCore = this.documentCore;
        const logicDocument = this.documentCore.getDocument();
        const bDynamicHeight = documentCore.getDynamicHeightMode();
        const bDynamicHeightPrev = logicDocument.getDynamicHeightModePrev();
        const result = (this._height != null && this._height !== height) ||
         ( bDynamicHeight === false &&  bDynamicHeightPrev === true) ||
         (bDynamicHeight === true && bDynamicHeightPrev === false);

        if (bDynamicHeight !== bDynamicHeightPrev) {
            logicDocument.setDynamicHeightModePrev(bDynamicHeight);
        }
        return result;
    }

    private setAutoSave = (bAutoSaveInc: boolean): void => {
        // const bAutoSaveInc = this.documentCore.isIncAutoSave();

        if ( bAutoSaveInc ) {
            if ( !this.bAutoSaveInc && !this.autoSaveIncTimer ) {
                this.startAutoSave();
            }
        } else {
            this.stopAutoSave();
        }

        this.bAutoSaveInc = bAutoSaveInc;
    }

    private startAutoSave(): void {
        if ( this.autoSaveIncTimer ) {
            clearTimeout(this.autoSaveIncTimer);
        }

        const documentCore = this.documentCore;
        const time = (15 <= documentCore.getHistoryCount() ? 1000 : 3000);

        this.autoSaveIncTimer = setTimeout(() => {
            documentCore.getIncrementDatas();
        }, time);
    }

    private stopAutoSave(): void {
        if ( this.autoSaveIncTimer ) {
            clearTimeout(this.autoSaveIncTimer);
        }
    }


    /** 获取正文内容区域 */
    private getContentHeights(documentCore: DocumentCore, index: number, pageProperty: any): [number, number] {
        const page = documentCore.getHdrFtr().pages[index];
        const header = (page != null) ? page.header : null;
        const footer = (page != null) ? page.footer : null;
        let restoredHeaderYLimit = (header != null) ? header.content.yLimit / 10 : documentCore.getMaxHeight(null);
        const scale = 1; // this.scale;
        restoredHeaderYLimit *= scale;
        // header content bottom line 页眉正文线
        let lastLineInHeaderY = this.getLastLineInHeaderY(header) * scale;
        if (header != null) {
            if (lastLineInHeaderY > restoredHeaderYLimit || (header != null && header.content.getPages().length > 1)) {
                lastLineInHeaderY = restoredHeaderYLimit;
            }
        }
        lastLineInHeaderY = numtoFixed(lastLineInHeaderY);
        // line separator. Max(页上边距，页眉正文线y)
        const scaleTopLeftY = numtoFixed(pageProperty.paddingTop * scale);
        let contentTopY = Math.max(scaleTopLeftY, lastLineInHeaderY);
        if (header != null) {
            if (contentTopY > restoredHeaderYLimit || (header != null && header.content.getPages().length > 1)) {
                contentTopY = restoredHeaderYLimit;
            }
        }
        contentTopY = numtoFixed(contentTopY);

        // footer line
        const {height, paddingBottom} = documentCore.render().pageProperty;
        const firstLineInFooterY = numtoFixed(this.getFirstLineInFooterY(footer) * scale);
        const footerTopLeftY = numtoFixed((height - paddingBottom) * scale);
        const contentBottomY = numtoFixed((firstLineInFooterY === 0) ? footerTopLeftY :
            Math.min(footerTopLeftY, firstLineInFooterY)) ; // footerTopRight.y?
        return [contentTopY, contentBottomY];
    }

    /** 获取页眉底线Y坐标 */
    private getLastLineInHeaderY(header: any): number {
        let lastLineInHeaderY = 0;
        if (header != null && header.getTrueContent().length > 0) {
            const contentArr = header.content.content;
            for (let index = contentArr.length - 1; index >= 0; index--) {
                const content = contentArr[index];
                if (content.isHidden()) {
                    continue;
                }
                // const element = contentArr[contentArr.length - 1];
                lastLineInHeaderY = content.getPageBounds(0).bottom;
                break;
            }
        }
        return lastLineInHeaderY;
    }
    /** 获取页脚线 */
    private getFirstLineInFooterY(footer: any): number {
        let firstLineInFooterY = 0;
        if (footer != null && footer.getTrueContent().length > 0) {
            const contentArr = footer.content.content;
            const element = contentArr[0];
            if (element.getPageBounds(0) == null) {
                console.warn('cannot retrieve element bounds');
                return 0;
            }
            firstLineInFooterY = element.getPageBounds(0).top;
        }
        return firstLineInFooterY;
    }

    private renderCommentLayer(pageCount: number, pageInfo: any, rowHeight: number): any {
        // if (this._bAsyncLoadComment === undefined) {
        //     return null;
        // }

        const commentStatusInfo = this.documentCore.getCommentStatusInfo();
        if (this._bAsyncLoadComment !== true) {
            let count;
            // tslint:disable-next-line: no-conditional-assignment
            if (commentStatusInfo.bInsertNew || commentStatusInfo.bShowPanel || (count = this.documentCore.getAllCommentCount()) > 0) {
                this._bAsyncLoadComment = true;
            } else if (this._bAsyncLoadComment === undefined) {
                return null;
            }
        }

        const flag = COMMENT_FLAG.addCommentFlag;
        COMMENT_FLAG.addCommentFlag = false;

        const props = {
            pageInfo,
            pageCount,
            pageHeight: rowHeight,
            commentStatusInfo,
            addCommentFlag: flag
        };

        return (
            <AsyncLoadComment
                key='asyncLoadComment'
                documentCore={this.documentCore}
                host={this}
                visible={this._bAsyncLoadComment}
                props={props}
            />
        );
    }

    /**
     * 添加波浪线事件监听
     */
    private addWavyUnderlineEvents(): void {
        const container = this.getContainer();
        if (container && this.wavyUnderlineHandler) {
            container.addEventListener('mousemove', this.handleWavyUnderlineMouseMove);
            container.addEventListener('mouseleave', this.handleWavyUnderlineMouseLeave);
        }
    }

    /**
     * 移除波浪线事件监听
     */
    private removeWavyUnderlineEvents(): void {
        const container = this.getContainer();
        if (container && this.wavyUnderlineHandler) {
            container.removeEventListener('mousemove', this.handleWavyUnderlineMouseMove);
            container.removeEventListener('mouseleave', this.handleWavyUnderlineMouseLeave);
        }
    }

    /**
     * 处理波浪线鼠标移动事件
     */
    private handleWavyUnderlineMouseMove = (e: Event): void => {
        if (this.wavyUnderlineHandler) {
            this.wavyUnderlineHandler.handleMouseMove(e as any);
        }
    }

    /**
     * 处理波浪线鼠标离开事件
     */
    private handleWavyUnderlineMouseLeave = (): void => {
        if (this.wavyUnderlineHandler) {
            this.wavyUnderlineHandler.handleMouseLeave();
        }
    }

}

export interface IWatermarkFontProps {
    wmColor: string;
    wmOpacity: string;
}
