import { XmlComponent } from '../../xml-components';
import { HeaderStyleInfoType } from './header-style-info-type';

export class HeaderPage extends XmlComponent {

    constructor(index: number, type?: HeaderStyleInfoType, startPage?: number, endPage?: number) {

        super(`header${index}`);

        if (type === HeaderStyleInfoType.SINGLE) {
            this.root.push('all');
        } else if (type === HeaderStyleInfoType.MULTIPLE) {
            if (startPage && endPage) {
                this.root.push(startPage + '-' + endPage);
            }
        }

    }

    public setHeaderPage(startPage: number, endPage: number): void {
        this.root.push(startPage + '-' + endPage);
    }

}
