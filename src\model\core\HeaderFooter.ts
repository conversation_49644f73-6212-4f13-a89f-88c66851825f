import Document, { RecalcResultType, DocCurPosType } from './Document';
import { HeaderFooterType, EquationType, INewControlProperty, DocumentSectionType,
  IRevisionChange,
  ResultType,
  NewControlType,
  ImageMediaType} from '../../common/commonDefines';
import { IDrawSelectionBounds, IDrawTableNewBorder } from '../DocumentCore';
import { IHeaderFooterRecalcInfo } from '../HeaderFooterProperty';
import { DocumentContent } from './DocumentContent';
import { SectionPageNumInfo } from './Sections';
import { ICurrentCursorPos } from './Paragraph/ParagraphSearchPosXY';
import DocumentFrameBounds from './FrameBounds';
import DocumentContentElementBase from './DocumentContentElementBase';
import { ParaElementBase } from './Paragraph/ParaElementBase';
import MouseEventHandler, { IMouseEvent, MouseEventType } from '../../common/MouseEventHandler';
import { ParaElementType, ParagraphContentPos } from './Paragraph/ParagraphContent';
import { SectionProperty } from './SectionProperty';
import Paragraph from './Paragraph';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';
import { ILimits } from './DocumentPage';
import { ICursorProperty } from '../CursorProperty';
import { idCounter } from './util';
import Selection from './Selection';
import { NewControl } from './NewControl/NewControl';
import { DocumentElementState } from './HistoryState';
import { HistoryDescriptionType } from './HistoryDescription';
import { IFRAME_MANAGER } from '../../common/IframeManager';
import { TableBase } from './TableBase';
import { IOperateResult } from './History';

/**
 * single-footer class
 */
export default class HeaderFooter {
  public recalcInfo: IHeaderFooterRecalcInfo;
  public type: HeaderFooterType;
  public parent: CHeaderFooterController;
  public content: DocumentContent;
  public recalcType: HeaderFooterType;

  private id: number;
  private logicDocument: Document;
  private pageCountElements: any[];

  constructor(parent: CHeaderFooterController, logicDocument: Document, type: HeaderFooterType) {
    // function CHeaderFooter(Parent, LogicDocument, DrawingDocument, Type)
    this.id = idCounter.getNewId();
    this.parent = parent;
    this.logicDocument = logicDocument; // just document from documentCore.getDocument()
    this.type = type;
    this.recalcType = -1;
    this.recalcInfo = {
      curPage       : -1,
      recalcObj     : {},
      needRecalc    : {},
      pageNumInfo   : {},
      sectPr        : {},
    };
    this.pageCountElements = [];

    if (logicDocument) {
      // console.log(type);
      if (type === HeaderFooterType.Header) {
          this.content = new DocumentContent( this, logicDocument, 0, 0, 0, 0, false);
          // const testPara = this.content.content[0] as Paragraph;
          // const testPortion = new ParaPortion(testPara);
          // testPortion.addText('Tyger, tyger burning bright, in the forest of the night.威廉姆.布雷克');
          // testPortion.addText('Tyger, tyger burning bright, in the forest of the night.威廉姆.布雷克');
          // testPara.addToContent(0, testPortion);
          // // console.log(this.content.content)
          // const testPara2 = new Paragraph(this.content);
          // const testPortion2 = new ParaPortion(testPara2);
          // testPortion2.addText('是故不应取法，不应取非法。知我说法如筏喻者。');
          // // testPortion2.addText('是故不应取法，不应取非法。知我说法如筏喻者。');

          // const imgStr = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEEAAAAlCAYAAAAdkoQJAAAAAXNSR0IArs4c6QAAAARnQU1'
          //               + 'BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAATBSURBVGhD7ZhLKHVdGMePXHIt1xJRBiQptwhJTNxiYqAkpHh'
          //               + 'l4DIzEOUSpRRKDJiYmAjlLndFQkYMSLnfi5Bi8rz+j7WPc9n7OLzf23u+4/xqt9ez1t5rr+e/1nrWWltFFsgiwhsWEd4'
          //               + 'wWoSdnR2RMj+MEsHNzY2Gh4eFZX4YJYJKpfrZI6Gjo4NFWFtbEznmh6IIdXV1VFFRQX5+fiwCpkRSUpIoNS8MjoTo6Gj'
          //               + 'y8fFhEcwZo2MCpoWjoyO1t7dTWVkZ3d3didL/PwZF2N7eJgcHBxZhZGSEbGxsaHp6mpydnX+GCNbW1mRlZcUCDA0NUUh'
          //               + 'ICDk5OXHe+fm5eMowJycn1NDQICzTRVGEy8tLmpubYxGky8XFRZQax/HxMb9n6ii2cGxsTO08psHKyooo+Rp2dnY0Ozs'
          //               + 'rLNNET4SLiwtKT09XC1BdXS1K3ikvLxcpeWpqakTqnZ6eHvL29haWaaIW4erqip1GLMAdc7+1tVWUfoBRYQi8Nzo6Kqx'
          //               + '3oqKiOB/venp6crq4uFiUfp/r62vev0gd9l203hwcHOR7c3MzN1SO0NBQsre3F5Y+gYGBVFtbK6wPNjY2eFpAZH9/f5G'
          //               + 'rT1VVFWVnZ9PExARtbW1xHu5dXV08teAsxEQam7nJyUlescLDwzmAfwdZ+SAAdoxKwJHb21thabOwsEAxMTHC0qakpIR'
          //               + '2d3eFpcze3h63Qephd3d3Wlxc5Py/gZ4I6C25IT81NcWNSUlJ4YYpjRSAvYUmS0tLZGtrSwcHByLHtNATAb2ckZEhrPd'
          //               + 'VAqLAaZwdcE9ISODnsHuU4+bmRqSI9xd4/+npSeSYHloivL6+snOaoAcx9ySkAJSbm6v3rBwQCnVgxTFVtETIz8/nQ5M'
          //               + 'SMzMzahGCg4NZBLmpowumAc4dOIz9K+CbFhO/2JdfEzoiSA4q0dbWxs/gur+/57yioiKjhAB4b39/X1j/LfHx8Vx/fX2'
          //               + '9yPnA0GoG1F4/Pz8bDHaxsbHsrJxQj4+PHDTlwLIrOY76j46OOP0n4ESLUZiXl8ftwSjD0or6IUJfXx8lJyezjQvPYkl'
          //               + '9eXkRNWij9qipqYni4uKE9UFvby9XhI+5urqqVdUbXgpgtygJpysgGvdZL2mSlZWlbgsupHE+AV5eXlwfLuxDkN/S0kI'
          //               + 'DAwNcHhERQYWFhZzWRd0qVKjE+vo63319fcnDw4PFMHYKoJdQd2ZmJl+aREZGUmJiIi+7mvT399P8/LywiE5PT3nZRUc'
          //               + 'BBNq0tDROGwIx6OzsjNMQ5+HhgdO6sAgYPrpruxw4RaIHIABWEvxc+awnERQhQlBQkMjRZ3NzU6Q+npeQvqnkgBLLy8v'
          //               + '8axCgvTgTKcEi6A5TOQ4PD7kyNLCxsVHkvv+Cw/uYcwEBASJXG/TCV5BiDy7N5fkrlJaWUmdnp8ERLsHeY3h9xvj4ODd'
          //               + 'K85SIEyUcxIcKCgpErj6SyN3d3XyYgmCrq6uc97fAxi4sLExYhlHl5OQY3BtIwBE4m5qaqu6lyspKUaoMegTvoXeNmXL'
          //               + '/AtVnowARWXIaziCYSXsEY5EitKny5pt8PEB01pybkghStDUnVLp/giTgNNZeTTD/TfUk+CcoLgtyOzv8PTZHPl8bfwA'
          //               + 'WEd6wiEBEvwGIHo+bfnF3vQAAAABJRU5ErkJggg==';
          // const signature = new ParaDrawing(logicDocument, 65, 37, imgStr);
          // signature.setPreload(true);
          // // signature.setInHdrFtr(true);
          // testPortion2.addToContent(testPortion2.content.length, signature);
          // testPara2.addToContent(0, testPortion2);
          // this.content.content.push(testPara2);
          // testPara.next = testPara2;
          // testPara2.prev = testPara;
          // // this.content.content[0].Style_Add( this.Get_Styles().Get_Default_Header() );
      } else {
          this.content = new DocumentContent( this, logicDocument, 0, 0, 0, 0, false);
          // // console.log(this.content.content)
          // const testPara = this.content.content[0] as Paragraph;
          // const testPortion = new ParaPortion(testPara);
          // testPortion.addText('Rowen is the best Rowen is the best Rowen is the best ');
          // testPortion.addText('Rowen is the best Rowen is the best Rowen is the best ');
          // testPara.addToContent(0, testPortion);
          // // this.content.content[0].Style_Add( this.Get_Styles().Get_Default_Footer() );
      }
    }

    // Add this class to the Id table (required at the end of the constructor)
    // g_oTableId.Add( this, this.Id ); // wtf is this?
  }

  public getId(): number {
    return this.id;
  }

  public clearContent(flag?: boolean): void {
    this.content.clearContent(flag);
  }

  public getTheme(): any {
    //
  }

  public getColorMap(): any {
    //
  }

  public getNewControlBySelectArea(): string {
    return this.content.getNewControlBySelectAreaBase();
  }

  public selectNewControlByNewControl(newControl: NewControl, inCludeContent: boolean): number {
    return this.content.selectNewControlByNewControl(newControl, inCludeContent);
  }

  public copy(unEmptyPara: boolean = false): HeaderFooter {
    const newHeaderFooter = new HeaderFooter(this.parent, this.logicDocument, this.type);
    newHeaderFooter.content.copy2(this.content, unEmptyPara);
    return newHeaderFooter;
  }

  // set 排版信息 of headerfooter
  public setPage(pageAbs: number): void {
    if (pageAbs !== this.recalcInfo.curPage && undefined !== this.logicDocument.pages[pageAbs]) {
      // There may be a situation where our footer was calculated for a given page, but on it now
      // footer is not used. We prohibit changing the current page to the given page for this footer.
      const headerFooterController = this.parent;
      const headerFooterPage = this.parent.pages[pageAbs];
      if (undefined === headerFooterPage ||
        (this !== headerFooterPage.header && this !== headerFooterPage.footer)) {
        return;
      }
      const recalcObj = this.recalcInfo.recalcObj[pageAbs];
      if (undefined !== recalcObj) {
        this.recalcInfo.curPage = pageAbs;
        // console.log(recalcObj)
        // TODO: a condition to decide if header need recalculate
        // this.content.loadRecalculateObject(recalcObj);
      }
    }
  }

  public isNeedRecalculate(pageAbs: number): boolean {
    const pageNumInfo: SectionPageNumInfo = this.logicDocument.getSectionPageNumInfo(pageAbs);

    if ( this.type === this.recalcType || this.logicDocument.isTrackRevisions() ) {
      this.recalcType = -1;
      return true;
    }

    if (this.type !== this.parent.getCurHeaderFooter().type) {
      // TODO: more elegant
      const sectionProperty = this.logicDocument.sectionProperty;
      // force set from headerfooter modal
      // tslint:disable-next-line: max-line-length
      if (this.type === HeaderFooterType.Footer && sectionProperty.footerFirst != null && sectionProperty.footerDefault == null) {
        return true;
      // tslint:disable-next-line: max-line-length
      } else if (this.type === HeaderFooterType.Header && sectionProperty.headerFirst != null && sectionProperty.headerDefault == null) {
        return true;
      }

      // also cover: if cur headerfooter is changed due to setfirstpagediff, footer need to recalc
      if (this.type === HeaderFooterType.Footer && sectionProperty.headerFirst === this.parent.getCurHeaderFooter()) {
        return true;
      }

      // if cur headerfooter is not the inspected headerfooter, no need to recalc
      return false;
    }

    // console.log(this.recalcInfo.recalcObj[pageAbs])
    // if (undefined !== this.recalcInfo.recalcObj[pageAbs]) {
    //   console.log(this.recalcInfo.recalcObj[pageAbs].recalcContent.length === this.content.content.length)
    // }

    // TODO: this can be useful in the future if recalcObj is completely figured out
    if (true !== this.recalcInfo.needRecalc[pageAbs] &&
      true === pageNumInfo.comparePageNumInfo(this.recalcInfo.pageNumInfo[pageAbs]) &&
      undefined !== this.recalcInfo.recalcObj[pageAbs]) {

      // if (this.recalcInfo.recalcObj[pageAbs].recalcContent.length !== this.content.content.length) {
      //   return true;
      // }
      return false;
    }
    return true;
  }

  public recalculate(pageAbs: number, sectPr: SectionProperty): boolean {
    // console.log('headerfooter recalc')
    // The logic for recounting headers is as follows:
    // 1. When recounting the page, the footer is recounted every time (in meaning we go to the Recalculate function,
    // i.e. here)
    // 2. Next, we see if we need to recount this page at all RecalcInfo.NeedRecalc [Page_abs] if this value
    // not false, then you need to recount, and if not, then exit
    // 3. If you need to recount, recount again and see if the borders of the recount and the positions of floating
    // images, and set RecalcInfo.NeedRecalc [Page_abs] = false.
    let bChanges = false;
    const recalcObj = this.recalcInfo.recalcObj[pageAbs];
    IFRAME_MANAGER.setDocId(this.logicDocument.id);
    let oldSumH = 0;
    let oldBounds = null;
    const oldFlowPos = [];

    if (recalcObj === undefined) {
      bChanges = true;
    } else {
      oldSumH = recalcObj.getSummaryHeight();
      oldBounds = recalcObj.getPageBounds(0);
    }

    // Recalculate this column
    this.content.setStartPage(pageAbs);
    // this.content.prepareRecalculateObject();

    this.clearPageCountElements();

    let curPage = 0;
    let recalcResult = RecalcResultType.RecalcResult2NextPage;

    let i = 0;
    // TODO
    while (recalcResult !== RecalcResultType.RecalcResult2End) {
      recalcResult = this.content.recalculatePage(curPage++, true, true, false, false);
      // console.log(recalcResult);
      i++;
      if (i >= 10000) {
        // tslint:disable-next-line: no-console
        console.warn('loop exceeds 10000 times');
        break;
      }
    }

    // TODO: recalcObj saveRecalculateObject
    // this.recalcInfo.recalcObj[pageAbs] = this.content.saveRecalculateObject();

    this.recalcInfo.pageNumInfo[pageAbs] = this.logicDocument.getSectionPageNumInfo(pageAbs);
    this.recalcInfo.sectPr[pageAbs] = sectPr;
    this.recalcInfo.needRecalc[pageAbs] = false;

    // If we had some kind of recount before, then compare it with the current one.
    // 1. Compare the boundaries: at the header, we look at the change in the lower border, and the lower - upper
    // 2. Compare the position and size of the Flow objects (? seems no need)
    if (bChanges === false) {
      const newBounds = this.content.getPageBounds(0);
      if ((Math.abs(newBounds.bottom - oldBounds.bottom) > 0.001 && this.type === HeaderFooterType.Header) ||
        (Math.abs(newBounds.top - oldBounds.top) > 0.001 && this.type === HeaderFooterType.Footer)) {
        bChanges = true;
      }
    }

    if (bChanges === false) {
      const newSumH = this.content.getSummaryHeight();
      if (Math.abs(oldSumH - newSumH) > 0.001) {
        bChanges = true;
      }
    }

    // If the current page is not set, then set the one that turned out to be recounted first. Otherwise
    // case, set the calculation of the page that was before.
    if (this.recalcInfo.curPage === -1 || this.logicDocument.getSectionPageNumInfo(this.recalcInfo.curPage)
      .comparePageNumInfo(this.recalcInfo.pageNumInfo[this.recalcInfo.curPage])) {

      this.recalcInfo.curPage = pageAbs;
      const headerFooterController = this.logicDocument.getHdrFtr();
      if (this.logicDocument.getDocPosType() === DocCurPosType.HdrFtr
          && this === headerFooterController.getCurHeaderFooter() ) {
        // Update the interface to update the footer settings, as we could get into the new section
        this.logicDocument.updateSelectionState();
      }
    } else {
      // why choose same name?
      // let recalcObj = this.recalcInfo.recalcObj[this.recalcInfo.curPage];

      // console.log(recalcObj)
      // TODO: a condition to decide if header need recalculate
      // this.content.loadRecalculateObject(recalcObj);
    }

    return bChanges;
  }

  public recalculate2(pageAbs: number): void {
    IFRAME_MANAGER.setDocId(this.logicDocument.id);
    this.content.setStartPage(pageAbs);
    // this.content.prepareRecalculateObject();
    let curPage = 0;
    let recalcResult = RecalcResultType.RecalcResult2NextPage;

    let i = 0;
    while (RecalcResultType.RecalcResult2End !== recalcResult) {
      recalcResult = this.content.recalculatePage(curPage++, true, true, false, false);
      i++;
      if (i >= 10000) {
        // tslint:disable-next-line: no-console
        console.warn('loop exceeds 10000 times');
        break;
      }
    }
  }

  public resetRecalculateCache(): void {
    // this.refreshRecalcData2();
    // this.content.resetRecalculateCache();
  }

  public getStyles(): any {
    return this.logicDocument.getStyles();
  }

  public getTableStyleForPara(): void {
    return null;
  }

  public getShapeStyleForPara(): void {
    return null;
  }

  public getTextBackGroundColor(): void {
    return null;
  }

  public getPageContentStartPos(pageNum: number): ILimits { // pageNum argument not exist in Only
    return { x : this.content.x, y : 0, xLimit : this.content.xLimit, yLimit : 0 };
  }

  public setCurrentElement(bUpdateStates: boolean, pageAbs: number): void {
    let pageIndex = -1;

    if (undefined !== pageAbs && null !== pageAbs && this.parent.pages[pageAbs]) {
      if ((this === this.parent.pages[pageAbs].header || this === this.parent.pages[pageAbs].footer)) {
        pageIndex = pageAbs;
      }
    }

    if (-1 === pageIndex) {
      // for (let key in this.parent.pages) {
      for (const key of Object.keys(this.parent.pages)) {
        // let pIndex = key | 0; -> bit or ensures number
        const pIndex: number = +key; // equivalent to Number()
        if ((this === this.parent.pages[pIndex].header || this === this.parent.pages[pIndex].footer) &&
        (-1 === pageIndex || pageIndex > pIndex)) {
          pageIndex = pIndex;
        }
      }
    }

    this.parent.curHeaderFooter = this;
    this.parent.waitMouseDown = true;
    this.parent.curPage = pageIndex;

    if (-1 === pageIndex) {
      this.recalcInfo.curPage = -1;
    }

    const oldDocPosType = this.logicDocument.getDocPosType();
    this.logicDocument.setDocPosType(DocCurPosType.HdrFtr);

    if (true === bUpdateStates && -1 !== pageIndex) {
      this.setPage(pageIndex);

      // this.logicDocument.updateInterfaceState();
      // this.logicDocument.updateRulersState();
      this.logicDocument.updateSelectionState();
    }

  }

  public isThisElementCurrent(): boolean {
    if (this === this.parent.curHeaderFooter && DocCurPosType.HdrFtr === this.logicDocument.getDocPosType()) {
      return true;
    }
    return false;
  }

  public reset(x: number, y: number, xLimit: number, yLimit: number): void {
    this.content.reset(x, y, xLimit, yLimit);
  }

  public draw(): void {
    //
  }

  public recalculateCurPos(bUpdateX: boolean = true, bUpdateY: boolean = true): ICurrentCursorPos {
    if (-1 !== this.recalcInfo.curPage) {
      return this.content.recalculateCurPos(bUpdateX, bUpdateY);
    }
  }

  public getBounds(): DocumentFrameBounds {
    return this.content.getPageBounds(0);
  }

  public updateCursorXY( bUpdateX: boolean = true, bUpdateY: boolean = true ): void {
    this.content.updateCursorXY(bUpdateX, bUpdateY);
  }

  public getDividingLine(pageIndex: number): number {
    const oldPage = this.recalcInfo.curPage;

    this.setPage(pageIndex);
    const bounds = this.getBounds();

    if ( -1 !== oldPage ) {
      this.setPage( oldPage );
    }

    if (HeaderFooterType.Footer === this.type) {
      return bounds.top;
    } else {
      return bounds.bottom;
    }
  }

  public checkRange(): void {
    // TODO: may be useful
  }

  public getCurrentPageByPos(pos: ParagraphContentPos): number {
    return this.content.getCurrentPageByPos(pos);
  }

  public getSelectionNodePos(bStart: boolean): any {
    const pos = new ParagraphContentPos();
    pos.clear();
    let type: number;
    if (this.isFooter()) {
      type = DocumentSectionType.Footer;
    } else {
      type = DocumentSectionType.Header;
    }
    pos.add(type);
    this.content.getSelectionNodePos(pos, bStart, false);
    return pos;
  }

  // 插入页码
  public addPageNum(alignment: number): void {
    let styleId = null;
    if (this.type === HeaderFooterType.Header) {
      styleId = this.getStyles()
                    .getDefaultHeader();
    } else {
      styleId = this.getStyles()
                    .getDefaultFooter();
    }
    this.content.headerFooterAddPageNum(alignment, styleId);
  }

  public isCell(bIsReturnCell: boolean): boolean {
    if (bIsReturnCell === true) {
      return null;
    }
    return false;
  }

  public isHeaderFooter(bReturnHeaderFooter: boolean): boolean | HeaderFooter {
    if (bReturnHeaderFooter === true) {
      return this;
    }
    return true;
  }

  public isHeader(): boolean {
    return (HeaderFooterType.Header === this.type);
  }

  public isFooter(): boolean {
    return (HeaderFooterType.Footer === this.type);
  }

  public isFootNote(bReturnFooterNote: boolean): boolean {
    return (bReturnFooterNote ? null : false);
  }

  public isInTable(bReturnTopTable: boolean): boolean {
    if ( true === bReturnTopTable ) {
      return null;
    }
    return false;
  }

  public isMovingTableBorder(): boolean {
    return this.content.isMovingTableBorder();
  }

  public getMovingTableNewBorder(): IDrawTableNewBorder {
    return this.content.getMovingTableNewBorder();
  }

  public isInTableCell(): boolean {
    return this.content.isInTableCell();
  }

  public isSelectedTableCells(): boolean {
    return this.content.isSelectedTableCells();
  }

  public getCurrentTable(): TableBase {
      return this.content.getCurrentTable();
  }

  public isSelectionUse(): boolean {
    return this.content.isSelectionUse();
  }

  // may TODO
  public isTextSelectionUse(): boolean {
    return null;
  }

  public isUseInDocument(id: number): boolean {
    // if (this.parent) {
    //   return this.parent.isUseInDocument(this.getId());
    // }
    // return false;
    return null;
  }

  public checkPage(pageIndex: number): boolean {
    return this.parent.checkPage(this, pageIndex);
  }

  public getCurPosXY(): ICursorProperty {
    return this.content.getCursorPosXY();
  }

  public getSelectedText(): any {
    return null;
  }

  public getSelectedElementsInfo(): any {
    return null;
  }

  public getSelectedContent(selectedContent: any): DocumentContentElementBase[] {
    return this.content.getSelectedContentToCopy();
  }

  public getParaContentByXY(mouseEvent: MouseEventHandler, pageIndex: number): any {
    const contentPos = this.getContentPosByXY(pageIndex, mouseEvent.pointX, mouseEvent.pointY);
    if ( contentPos === undefined ) {
        return null;
    }

    const element = this.content.content[contentPos];
    if (!element) {
      return;
    }

    return element.getParaContentByXY(mouseEvent, this.getAbsoluteStartPage());
  }

  public updateCursorType(x: number, y: number, pageAbs: number): void {
    // if (this.content instanceof Document) {
    //   (this.content as Document).updateCursorType(x, y, 0);
    // }
    this.content.updateCursorType(x, y, 0);
  }

  // 是否点击中表格边框
  public isTableBorder(x: number, y: number, pageAbs: number, options?: any): TableBase {
    this.setPage(pageAbs);
    return this.content.isTableBorder(x, y, 0, options);
  }

  public isInText(x: number, y: number, pageAbs: number): any {
    return null;
  }

  public isInDrawing(x: number, y: number, pageAbs: number): any {
    return null;
  }

  public updateSelectionState(): void {
    if (this.recalcInfo.curPage === -1) {
      this.logicDocument.needUpdateTarget = true;
      return;
    }

    if (this.content.curPos.type === DocCurPosType.DrawingObjects) {
      //
    } else {
      // If we have a selection, then remove the cursor and draw a selection.
      // If there is no selection, then remove it and restore the cursor.
      if (this.content.isSelectionUse() === true) {
        // TODO: wtf is only here above?
        if (false === this.content.isSelectionEmpty()) {
          if (true !== this.content.selection.bStart) {
            this.recalculateCurPos();
          }
        } else {
          this.recalculateCurPos();
        }

      } else {
        this.recalculateCurPos();
      }
    }
  }

  // -----------------------------------
  // Functions for working with content
  // -----------------------------------
  public addNewParagraph(): number {
    return this.content.addNewParagraph();
  }

  public addInlineImage(width: number, height: number, src: string, name?: string,
                        type?: EquationType, svgElem?: any, mediaType?: ImageMediaType,
                        mediaSrc?: string, datas?: any): string {
    return this.content.addInlineImage(width, height, src, name, type, svgElem,
        mediaType, mediaSrc, datas);
  }

  public addInlineTable(cols: number, rows: number, tableHeaderNum?: number,
                        tableName?: string, bRepeatHeader?: boolean): boolean {
    // if (this.content instanceof Document) { // no table in table correct?
    //   this.content.addInlineTable(cols, rows);
    // }
    return this.content.addInlineTableController(cols, rows, tableHeaderNum, tableName, bRepeatHeader);
  }

  public addToParagraph(paraItem: ParaElementBase, bRecal?: boolean): number {
    return this.content.addToParagraph(paraItem, bRecal);
  }

  public remove(direction: number, bOnlyText: boolean, bOnlySelection?: boolean, bAddText?: boolean):
              IOperateResult {
    return this.content.remove(direction, bOnlyText, bOnlySelection, bAddText);
  }

  public getCursorPosXY(): ICursorProperty {
    return this.content.getCursorPosXY();
  }

  public moveCursorLeft(bShiftKey: boolean): boolean {
    const bRetValue = this.content.moveCursorLeft(bShiftKey);
    return bRetValue;
  }

  public moveCursorRight(bShiftKey: boolean): boolean {
    const bRetValue = this.content.moveCursorRight(bShiftKey);
    return bRetValue;
  }

  public moveCursorUp(bShiftKey: boolean): boolean {
    const bRetValue = this.content.moveCursorUp(bShiftKey);
    return bRetValue;
  }

  public moveCursorDown(bShiftKey: boolean): boolean {
    const bRetValue = this.content.moveCursorDown(bShiftKey);
    return bRetValue;
  }

  // may TODO
  public moveCursorToEndOfLine(bAddToSelect: boolean): boolean {
    return null;
  }

  // may TODO
  public moveCursorToStartOfLine(bAddToSelect: boolean): boolean {
    return null;
  }

  public moveCursorToStartPos(bAddToSelect: boolean): void {
    const bRetValue = this.content.moveCursorToStartPos(bAddToSelect);
    return bRetValue;
  }

  public moveCursorToEndPos(bAddToSelect: boolean): void {
    const bRetValue = this.content.moveCursorToEndPos(bAddToSelect);
    return bRetValue;
  }

  public moveCursorToXY(curPage: number, pointX: number, pointY: number,
                        bAddToSelect: boolean = false): void {
    this.setPage(curPage);
    return this.content.moveCursorToXY(curPage, pointX, pointY, bAddToSelect);
  }

  // may TODO
  public moveCursorToCell(bNext: boolean): any {
    //
  }

  public setParagraphAlignment(alignment: number): boolean {
    return this.content.setParagraphAlignment(alignment);
  }

  public setParagraphProperty(paraProperty: any): boolean {
    return this.content.setParagraphProperty(paraProperty);
  }

  // may TODO
  public setParagraphContextualSpacing(bValue: boolean): void {
    //
  }

  // may TODO
  public setParagraphPageBreakBefore(bValue: boolean): void {
    //
  }

  // may TODO
  public setImageProps(props: any): void {
    //
  }

  // may TODO
  public setTableProps(props: any): void {
    //
  }

  public removeSelection(): void {
    this.content.removeSelection();
  }

  public setSelectionStart(x: number, y: number, pageIndex: number, mouseEvent: IMouseEvent): void {
    this.setPage(pageIndex);
    if (this.logicDocument.getImageFlags().isStartAddShape) {
      //
    } else {
      return this.content.setSelectionStart(x, y, 0, mouseEvent);
    }
  }

  public setSelectionEnd(x: number, y: number, pageIndex: number, mouseEvent: IMouseEvent): void {
    this.setPage(pageIndex);
    return this.content.setSelectionEnd(x, y, 0, mouseEvent);
  }

  public checkPosInSelection(pageIndex: number, pointX: number, pointY: number, nearPos?: number): boolean {
    if (-1 === this.recalcInfo.curPage) {
      return false;
    }
    const headerFooterPage = this.content.getStartPage();
    if (nearPos || pageIndex === headerFooterPage) {
      // if (this.content instanceof Document) {
      //   return this.content.checkPosInSelection(pageIndex, pointX, pointY);
      // }
    }

    return false;
  }

  public selectAll(): void {
    return this.content.selectAll();
  }

  public getCurrentParagraph(): Paragraph {
    return this.content.getCurrentParagraph();
  }

  public startSelectionFromCurPos(): void {
    //
  }

  public getAbsolutePage(curPage: number): number {
    return curPage;
  }

  // ----------------------------------
  // Functions for working with tables
  // ----------------------------------
  public addTableRow(bBefore: boolean): boolean {
    return this.content.addTableRow(bBefore);
  }

  public removeTableRow(rowIndex?: number): boolean {
    return this.content.removeTableRow(rowIndex);
  }

  public removeTableColumn(): boolean {
    return this.content.removeTableColumn();
  }

  public mergeTableCells(): boolean {
    return this.content.mergeTableCells();
  }

  public splitTableCells(rows: number, cols: number): boolean {
    return this.content.splitTableCells(rows, cols);
  }

  public removeTable(): void {
    this.content.removeTable();
  }

  public canMergeTableCells(): boolean {
    return this.content.canMergeTableCells();
  }

  // public isPopWinNewControl(): boolean {
  //   return this.content.isPopWinNewControlController();
  // }

  public canInput(): boolean {
      return this.content.canInputController();
  }

  public canDelete(direction?: number): boolean {
      return this.content.canDeleteController(direction);
  }

  public canInsertNewControl(): boolean {
      return this.content.canInsertNewControlController();
  }

  public getDocumentSelection(): Selection {
    return this.content.selection;
  }

  public getContentPosByXY(pageIndex: number, pointX: number, pointY: number): number {
    return this.content.getContentPosByXY(pageIndex, pointX, pointY);
  }

  public isFocusInNewControl(mouseEvent?: MouseEventHandler, pageIndex?: number): boolean {
    return this.content.isFocusInNewControl(mouseEvent.pointX, mouseEvent.pointY, this.getAbsoluteStartPage());
  }

  public getFocusInNewControl(mouseEvent?: MouseEventHandler, pageIndex?: number): NewControl {
    return this.content.getFocusInNewControl(mouseEvent.pointX, mouseEvent.pointY, this.getAbsoluteStartPage());
  }

  public getCursorInNewControl(): NewControl {
    return this.content.getCursorInNewControl();
  }

  // -------------------------
  // The Undo / Redo function
  // -------------------------
  public refreshRecalData(): void {
    this.refreshRecalData2();
  }

  public refreshRecalData2(): void {
    // We save the recounted pages in the old recount, and the current one is reset
    this.recalcInfo.pageNumInfo = {};
    this.recalcInfo.sectPr      = {};
    this.recalcInfo.curPage     = -1;
    this.recalcInfo.needRecalc  = {};
  }

  public refreshRecalcDataBySection(): void {
    //
  }

  // TODO: hyperlinks related

  // TODO: font map, font names

  public addComment(): void {
    //
  }

  public canAddComment(): void {
    //
  }

  public addNewControl(property: INewControlProperty, sText?: string): number {
    let result: number;
    if (property.newControlType === NewControlType.Button) {
      if (this.logicDocument.checkParaButtonName(property.newControlName) === false) {
          return ResultType.Failure;
      }
      this.logicDocument.addParaButton({name: property.newControlName, content: sText, bPrint: undefined});
      result = ResultType.Success;
    } else {
      result = this.content.addNewControlInternal(property, sText);
    }
    
    if ( ResultType.Success !== result ) {
        return result;
    }

    if ( true === this.content.selection.bUse ) {
        this.removeSelection();
    }

    this.logicDocument.recalculate();
    this.logicDocument.updateCursorXY();

    return result;
  }

  public isCursorInNewControl(): boolean {
    const hdrFtrDocumentContent = this.content;
    let contentPos = hdrFtrDocumentContent.getCurPos().contentPos;

    if ( true === hdrFtrDocumentContent.selection.bUse ) {
        contentPos = hdrFtrDocumentContent.selection.endPos;
    }

    if ( null != hdrFtrDocumentContent.content[contentPos] ) {
        return hdrFtrDocumentContent.content[contentPos].isCursorInNewControl();
    }

    return false;
  }

  // --------------------
  // ungrouped functions
  // --------------------
  public getSectPr(): void {
    //
  }

  public setParagraphFramePr(): void {
    //
  }

  public getRevisionsChangeElement(): void {
    //
  }

  public getSelectionBounds(): IDrawSelectionBounds {
    if (this.recalcInfo.curPage !== -1) {
      return this.content.getSelectionBounds(undefined, undefined);
    }
    return null;
  }

  public getSelection(): Selection {
    return this.content.selection;
  }

  public getDocumentContent(): DocumentContent {
    return this.content;
  }

  public addPageCountElement(oElement: any): void {
    for (let nIndex = 0, nCount = this.pageCountElements.length; nIndex < nCount; ++nIndex) {
      if (oElement === this.pageCountElements[nIndex]) {
        return;
      }
    }
    this.pageCountElements.push(oElement);
  }

  public havePageCountElement(): boolean {
    return this.pageCountElements.length > 0 ? true : false;
  }

  public clearPageCountElements(): void {
    this.pageCountElements = [];
  }

  public updatePageCountElements(nPageCount: number): void {
    for (let nIndex = 0, nCount = this.pageCountElements.length; nIndex < nCount; ++nIndex) {
      this.pageCountElements[nIndex].setNumValue(nPageCount); // ?
    }
  }

  public forceRecalculate(nPageAbs: number): void {
    this.recalcInfo.needRecalc[nPageAbs] = true;
  }

  public getAllContentControls(): void {
    //
  }

  public getContent(): DocumentContent {
    return this.content;
  }

  public getTrueContent(): DocumentContentElementBase[] {
    return this.content.content;
  }

  /**
   * check if headerFooter has only 1 paraend paragraph
   */
  public isEmptyHeaderFooter(): boolean {
    const headerFooterContent = this.content.content;
    if (headerFooterContent.length === 1) {
      const thePara = headerFooterContent[0];
      if (thePara instanceof Paragraph) {
        return thePara.isEmpty();
      }
    }
    return (0 === headerFooterContent.length ? true : false);
  }

  public getAbsoluteStartPage(): number {
    return 0;
  }

  public getFocusRevision(mouseEvent: MouseEventHandler, pageIndex: number): IRevisionChange[] {
    if ( this.content ) {
      return this.content.getFocusRevision(mouseEvent.pointX, mouseEvent.pointY, pageIndex);
    }

    return null;
  }

  public acceptRevisionChanges(bAll: boolean = true): void {
    if ( this.content ) {
      return this.content.acceptRevisions(bAll);
    }
  }

  public rejectRevisionChanges(bAll: boolean = true): void {
    if ( this.content ) {
      return this.content.rejectRevisions(bAll);
    }
  }

  public getDocumentSectionType(): DocumentSectionType {
    if (this.isHeader()) {
      return DocumentSectionType.Header;
    } else {
      return DocumentSectionType.Footer;
    }
  }

  public getTableCellByXY(pointX: number, pointY: number, pageIndex: number): any {
    if ( this.content ) {
      return this.content.getTableCellByXY(pointX, pointY, pageIndex);
    }
  }

}

/**
 * footer class
 */
export class CHeaderFooterController {
  public pages: HeaderFooterPage[];
  public curHeaderFooter: HeaderFooter; // Current footer
  public curPage: number;
  public waitMouseDown: boolean;

  private id: number;
  private logicDocument: Document;
  private changeCurPageOnEnd: boolean;
  private justLeaveHeaderFooter: boolean;
  // private lock: boolean;

  private bForceRecalculate: boolean;

  constructor(logicDocument: Document) {
    this.id = idCounter.getNewId();
    this.logicDocument = logicDocument; // just document from documentCore.getDocument()
    this.curHeaderFooter = null;
    this.pages = [];
    this.curPage = 0;
    this.changeCurPageOnEnd = true;
    this.waitMouseDown = true;
    this.justLeaveHeaderFooter = false;
    this.bForceRecalculate = false;

    // Add this class to the Id table (required at the end of the constructor)
    // g_oTableId.Add( this, this.Id ); // wtf is this?
  }

  public getId(): number {
    return this.id;
  }

  public getParagraphProperty(): any {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.content.getParagraphProperty();
    }
  }

  public getSelectText(): string {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.content.getSelectText(false);
    }
  }

  public canCopy(): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.content.isCanCopy();
    }
  }

  public isNotOverNewControl(): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.content.unOverNewControl();
    }
  }

  public getNewControlBySelectArea(): string {
    return this.curHeaderFooter.getNewControlBySelectArea();
  }

  // -----------------------------------
  // Functions for working with footers
  // -----------------------------------
  public gotoNextHeaderFooter(): boolean {
    let curHeaderFooter = this.curHeaderFooter;
    if (!curHeaderFooter || curHeaderFooter.recalcInfo.curPage) {
      return;
    }
    let curPage = curHeaderFooter.recalcInfo.curPage;
    const pages = this.pages;

    if (curHeaderFooter.type === HeaderFooterType.Header && pages[curPage].footer !== undefined) {
      curHeaderFooter = pages[curPage].footer;
    } else {
      curHeaderFooter = null;
    }

    while (curHeaderFooter === null) {
      curPage++;
      if (pages[curPage] === undefined) {
        break;
      } else if (pages[curPage].header != null) {
        curHeaderFooter = pages[curPage].header;
      } else if (pages[curPage].footer != null) {
        curHeaderFooter = pages[curPage].footer;
      }
    }

    if (curHeaderFooter !== null) {
      this.curHeaderFooter = curHeaderFooter;
      curHeaderFooter.setPage(curPage);
      curHeaderFooter.content.moveCursorToStartPos(false);
      return true;
    }
    return false;
  }

  public getCurrentTextProps(): any {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.content.getCurrentTextProps();
    }
  }

  public gotoPrevHeaderFooter(): boolean {
    let curHeaderFooter = this.curHeaderFooter;
    if (!curHeaderFooter || curHeaderFooter.recalcInfo.curPage) {
      return;
    }
    let curPage = curHeaderFooter.recalcInfo.curPage;
    const pages = this.pages;

    if (curHeaderFooter.type === HeaderFooterType.Footer && pages[curPage].header !== undefined) {
      curHeaderFooter = pages[curPage].header;
    } else {
      curHeaderFooter = null;
    }

    while (curHeaderFooter === null) {
      curPage--;

      if (pages[curPage] === undefined) {
        return;
      } else if (pages[curPage].footer != null) {
        curHeaderFooter = pages[curPage].footer;
      } else if (pages[curPage].header != null) {
        curHeaderFooter = pages[curPage].header;
      }
    }

    if (curHeaderFooter !== null) {
      this.curHeaderFooter = curHeaderFooter;
      curHeaderFooter.setPage(curPage);
      curHeaderFooter.content.moveCursorToStartPos(false);
      return true;
    }
    return false;
  }

  public getCurPage(): number {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.content.getStartPage();
    }
    return 0;
  }

  // Get the header for the interface
  public getProps(): any {
    if (this.curHeaderFooter != null && this.curHeaderFooter.recalcInfo.curPage !== -1) {
      const pr: any = {};
      pr.type = this.curHeaderFooter.type;
      if (this.logicDocument.pages[this.curHeaderFooter.recalcInfo.curPage]) {
        return pr;
      }

      const index = this.logicDocument.pages[this.curHeaderFooter.recalcInfo.curPage].pos;
      const sectPr = this.logicDocument.sectionsInfo.getSectPr(index).sectProperty;

      if (pr.type === HeaderFooterType.Footer) {
        // pr.position = sectPr.getPageMarginsFooter()
      } else {
        // pr.position = sectPr.getPageMarginsHeader()
      }

      // pr.differentFirst = sectPr.getTitlePage();
      // pr.DifferentEvenOdd = EvenAndOddHeaders;

      return pr;
    }
    return null;
  }

  public recalculateCurPos(bUpdateX: boolean = true, bUpdateY: boolean = true): ICurrentCursorPos {
    if (this.curHeaderFooter) {
      return this.curHeaderFooter.recalculateCurPos(bUpdateX, bUpdateY);
    }
  }

  // -----------------------------------
  //
  // -----------------------------------
  public recalculate(pageIndex: number): boolean {
    // in cheaderfootercontroller class
    // console.trace()
    // console.log('hdrftr recalc'); // call for each page and every time document recalc() is called
    IFRAME_MANAGER.setDocId(this.logicDocument.id);
    const sectionPageInfo = this.logicDocument.getSectionPageNumInfo(pageIndex);
    // console.log(sectionPageInfo); // pageNum starts from 0

    const bFirst = sectionPageInfo.bFirst;

    const sectionsInfoElems = this.logicDocument.sectionsInfo.elements;
    let headerDiff = false; // 20)	setHeadersTextByJson
    // console.log(sectionsInfoElems)
    if (sectionsInfoElems.length > 1) {
      // console.log('20)	setHeadersTextByJson')
      headerDiff = true;
    }
    // Request the footer we need
    const sectHdrFtr = this.logicDocument.getSectionHdrFtr(pageIndex, bFirst, headerDiff);
    // console.log(sectHdrFtr);
    // console.log("headerfooter recalc");
    const header = sectHdrFtr.header;
    const footer = sectHdrFtr.footer;
    const sectProperty = sectHdrFtr.sectProperty;

    this.pages[pageIndex] = new HeaderFooterPage();
    if (this.logicDocument.getShowHeader() === true) {
      this.pages[pageIndex].header = header;
    }
    if (this.logicDocument.getShowFooter() === true) {
      this.pages[pageIndex].footer = footer;
    }

    const x = sectProperty.getPageMarginLeft();
    const xLimit = sectProperty.getPageWidth() - sectProperty.getPageMarginRight();
    // console.log(x, xLimit) // seems to be correct

    let bRecalcHeader = false;

    // const headerDrawings = null;
    // const headerTables = null;
    // const footerDrawings = null;
    // const footerTables = null;

    if (header) {
      // console.log('header exists. in header recalc');
      if (this.bForceRecalculate || header.isNeedRecalculate(pageIndex)) {
        // console.log('header recalced');
        const y = sectProperty.getPageMarginsHeader();

        // TODO: improvised solution, literally should not change yLimit
        // if do it w/o yLimit change, cannot solve multiple documentPages issue
        const yLimit = sectProperty.getPageHeight() / 2 * 10; // header cannot exceed half the page?
        // const yLimit = sectProperty.getPageHeight() / 2;

        // console.log(y, yLimit);

        header.reset(x, y, xLimit, yLimit);
        // console.log(circularParse(circularStringify(header)));
        bRecalcHeader = header.recalculate(pageIndex, sectProperty);
        // console.log(circularParse(circularStringify(header)));
      } else {
        if (header.recalcInfo.curPage === -1) {
          header.setPage(pageIndex);
        }
      }
      // headerDrawings = header.content.getAllDrawingObjects([])
      // headerTables = header.content.getAllFloatElements();
    }

    let bRecalcFooter = false;

    if (footer) {
      // console.log('footer exists. in footer recalc');
      if (this.bForceRecalculate || footer.isNeedRecalculate(pageIndex)) {
        // console.log('footer recalced');
        // We count the lower footer 2 times. First, from 0 position to calculate the total height of the colonist.
        // Based on the already known height, we have and count the footer.
        let y = 0;

        const yLimit = sectProperty.getPageHeight();
        // TODO: improvised solution, literally should not change yLimit
        // if do it w/o yLimit change, cannot solve multiple documentPages issue
        const yLimitTemp = sectProperty.getPageHeight() * 10;

        footer.reset(x, y, xLimit, yLimit);
        footer.recalculate2(pageIndex);

        const summaryHeight = footer.content.getSummaryHeight();

        // if (pageIndex === 0) {
        //   let lines = (footer.content.content[footer.content.content.length - 1] as Paragraph).lines;
        //   let lastLine = lines[lines.length - 1]
        //   console.log(lastLine)
        //   console.log(footer)
        //   console.log(footer.content.getCurLineHeight());
        //   summaryHeight += footer.content.getCurLineHeight();
        // }

        // usu. the latter one is larger
        y = Math.max( 2 * yLimit / 3, yLimit - sectProperty.getPageMarginsFooter() - summaryHeight);
        // console.log(sectProperty)

        footer.reset(x, y, xLimit, yLimitTemp);
        bRecalcFooter = footer.recalculate(pageIndex, sectProperty);
      } else {
        if (footer.recalcInfo.curPage === -1) {
          footer.setPage(pageIndex);
        }
      }
      // footerDrawings = footer.content.getAllDrawingObjects([])
      // footerTables = footer.content.getAllFloatElements();
    }

    // We adjust the positions of auto-figures taking into account the possible changed boundaries of the footers.
    // We do it for all auto-figures,
    // because the footers are calculated first on the page and there is no flow inside.
    const pagelimits = this.logicDocument.getPageContentStartPos(pageIndex);
    // TODO?

    if (bRecalcHeader === true || bRecalcFooter === true) {
      return true;
    }

    return false;
  }

  public checkRange(): void {
    //
  }

  public getParaContentByXY(mouseEvent: MouseEventHandler, pageIndex: number): any {
    if (this.curHeaderFooter) {
      return this.curHeaderFooter.getParaContentByXY(mouseEvent, pageIndex);
    }
  }

  // Request the bottom of the header for this page
  public getHeaderFooterLines(pageIndex: number): {top: number, bottom: number} {
    let header: HeaderFooter = null;
    let footer: HeaderFooter = null;

    if (this.pages[pageIndex] !== undefined) {
      header = this.pages[pageIndex].header;
      footer = this.pages[pageIndex].footer;
    }

    let top = null;
    if (header !== null && header.content.content.length) {
      top = header.getDividingLine(pageIndex);
    }

    let bottom = null;
    if (footer !== null && footer.content.content.length) {
      bottom = footer.getDividingLine(pageIndex);
    }

    return {top, bottom};
  }

  public updateCursorType(x: number, y: number, pageAbs: number): void {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.updateCursorType(x, y, pageAbs);
    }
    return null;
  }

  public isTableBorder(x: number, y: number, pageAbs: number, options?: any): TableBase {
    const headerFooter = this.internalGetContentByXY(x, y, pageAbs); // pageAbs = pageIndex?
    if (headerFooter != null) {
      return headerFooter.isTableBorder(x, y, pageAbs, options);
    }
    return null;
  }

  public isMovingTableBorder(): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.isMovingTableBorder();
    }
    return false;
  }

  public getMovingTableNewBorder(): IDrawTableNewBorder {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.getMovingTableNewBorder();
    }
    return null;
  }

  public isInTableCell(): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.isInTableCell();
    }
    return false;
  }

  public isSelectedTableCells(): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.isSelectedTableCells();
    }
    return false;
  }

  public getCurrentTable(): TableBase {
      if ( this.curHeaderFooter ) {
          return this.curHeaderFooter.getCurrentTable();
      }

      return undefined;
  }

  // may TODO
  public isInText(): void {
    //
  }

  // may TODO
  public isInDrawing(): void {
    //
  }

  public updateSelectionState(): void {
    if (this.curHeaderFooter != null) {
      // pay attention.. it's curHeaderFooter here!
      this.curHeaderFooter.updateSelectionState();
    }
  }

  public isSelectionUse(): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.isSelectionUse();
    }
    return false;
  }

  public isTextSelectionUse(): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.isTextSelectionUse();
    }
    return false;
  }

  public isUseInDocument(id: number): boolean {
    return null;
  }

  public checkPage(headerFooter: HeaderFooter, pageIndex: number): boolean {
    const header = this.pages[pageIndex].header;
    const footer = this.pages[pageIndex].footer;

    if (header === headerFooter || footer === headerFooter) {
      return true;
    }

    return false;
  }

  public getCurPosXY(): {x: number; y: number} {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.getCurrentParagraph();
    }
    return {x: 0, y: 0};
  }

  // not likely
  public getSelectedText(bClearText: boolean, oPr: any): string {
    return '';
  }

  // not likely
  public getSelectedElementsInfo(): void {
    //
  }

  public getSelectedContent(selectedContent: any): DocumentContentElementBase[] {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.getSelectedContent(selectedContent);
    }
    return null;
  }

  public getSelection(): Selection {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.getSelection();
    }
    return null;
  }

  // -----------------------------------
  // Functions for working with content
  // -----------------------------------
  public addNewParagraph(): number {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.addNewParagraph();
    }
  }

  public addInlineImage(width: number, height: number, src: string, name?: string,
                        type?: EquationType, svgElem?: any, mediaType?: ImageMediaType,
                        mediaSrc?: string, datas?: any): string {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.addInlineImage(width, height, src, name, type, svgElem,
              mediaType, mediaSrc, datas);
    }
  }

  public addInlineTable(cols: number, rows: number, tableHeaderNum?: number,
                        tableName?: string, bRepeatHeader?: boolean): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.addInlineTable(cols, rows, tableHeaderNum, tableName, bRepeatHeader);
    }

    return false;
  }

  public addToParagraph(paraItem: ParaElementBase, bRecal?: boolean): number {
    if (paraItem.type === ParaElementType.ParaNewLine) { // true === ParaItem.IsPageOrColumnBreak()
      return ResultType.UnEdited;
    }
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.addToParagraph(paraItem, bRecal);
    }
  }

  public remove(direction: number, bOnlyText: boolean, bOnlySelection?: boolean, bAddText?: boolean):
              IOperateResult {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.remove(direction, bOnlyText, bOnlySelection, bAddText);
    }
  }

  public getCursorPosXY(): ICursorProperty {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.getCursorPosXY();
    }
  }

  public moveCursorLeft(bShiftKey: boolean): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.moveCursorLeft(bShiftKey);
    }
  }

  public moveCursorRight(bShiftKey: boolean): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.moveCursorRight(bShiftKey);
    }
  }

  public moveCursorUp(bShiftKey: boolean): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.moveCursorUp(bShiftKey);
    }
  }

  public moveCursorDown(bShiftKey: boolean): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.moveCursorDown(bShiftKey);
    }
  }

  public moveCursorToEndOfLine(bAddToSelect: boolean): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.moveCursorToEndOfLine(bAddToSelect);
    }
  }

  public moveCursorToStartOfLine(bAddToSelect: boolean): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.moveCursorToStartOfLine(bAddToSelect);
    }
  }

  public moveCursorToXY(curPage: number, pointX: number, pointY: number,
                        bAddToSelect: boolean = false): void {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.moveCursorToXY(curPage, pointX, pointY, bAddToSelect);
    }
  }

  public moveCursorToStartPos(bAddToSelect: boolean): void {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.moveCursorToStartPos(bAddToSelect);
    }
  }

  public moveCursorToEndPos(bAddToSelect: boolean): void {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.moveCursorToEndPos(bAddToSelect);
    }
  }

  // may TODO
  public moveCursorToCell(bNext: boolean): any {
    //
  }

  public setParagraphAlignment(alignment: number): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.setParagraphAlignment(alignment);
    }
  }

  public setParagraphProperty(paraProperty: any): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.setParagraphProperty(paraProperty);
    }
  }

  public setParagraphContextualSpacing(bValue: boolean): void {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.setParagraphContextualSpacing(bValue);
    }
  }

  public setParagraphPageBreakBefore(bValue: boolean): void {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.setParagraphPageBreakBefore(bValue);
    }
  }

  public removeSelection(): void {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.removeSelection();
    }
  }

  public setSelectionStart(x: number, y: number, pageIndex: number, mouseEvent: MouseEventHandler, bActivate: boolean):
                                                                                                              boolean {
    // console.log(x, y, pageIndex, mouseEvent.pointX, mouseEvent.pointY, mouseEvent);

    let tempHeaderFooter = null;
    // If we get into a locked autofigure, even let it go beyond
    // may TODO

    // tslint:disable-next-line: no-conditional-assignment
    // if ((null !== (tempHeaderFooter = this.pages[pageIndex].header))
    //   // tslint:disable-next-line: no-conditional-assignment
    //   || (null !== (tempHeaderFooter = this.pages[pageIndex].footer)) ) {

    //   if (this.curHeaderFooter && ((null !== tempHeaderFooter && tempHeaderFooter !== this.curHeaderFooter)
    //     || this.curPage !== pageIndex)) {
    //     this.curHeaderFooter.removeSelection();
    //   }
    //   if (tempHeaderFooter !== null) {
    //     this.curHeaderFooter = tempHeaderFooter;
    //   }
    //   this.curPage = pageIndex;
    //   this.curHeaderFooter.setSelectionStart(x, y, pageIndex, mouseEvent);
    //   this.changeCurPageOnEnd = false;
    //   this.waitMouseDown = false;
    //   console.log("???")
    //   return true;

    // }

    this.changeCurPageOnEnd = true;
    const oldPage = this.curPage;

    // First, check to see if we’ve gotten into the content of the document. If yes, then it is necessary
    // activate the work with the document itself (just return false here)
    const pageMetrics = this.logicDocument.getPageContentStartPos(pageIndex);

    // T || F -> T
    // console.log(mouseEvent.clickCount)
    // console.log(y <= pageMetrics.y)
    // console.log(null === (tempHeaderFooter = this.pages[pageIndex].header))
    // console.log(!(y <= pageMetrics.y || (null !== (tempHeaderFooter = this.pages[pageIndex].header))));
    // console.log(!(y >= pageMetrics.yLimit || (null !== (tempHeaderFooter = this.pages[pageIndex].footer))))
    const imageFlags = this.logicDocument.getImageFlags();
    if (mouseEvent.clickCount >= 2 && imageFlags.isStartAddShape !== true &&
      // tslint:disable-next-line: no-conditional-assignment
      ( (!(y <= pageMetrics.y) && !(y >= pageMetrics.yLimit)) ||
        (true === imageFlags.isImageOnClick) ) ) { // may TODO !==
      // console.log("dare not in?")
      // Remove the select if it was
      if (this.curHeaderFooter != null) {
        this.curHeaderFooter.removeSelection();
      }
      mouseEvent.clickCount = 1;
      return false;
    }
    this.curPage = pageIndex;
    let headerFooter: HeaderFooter = null;

    // Check if we are in the footer, if any. If we hit
    // footer area, but it’s not there, then add a new footer.
    // tslint:disable-next-line: no-conditional-assignment
    // if (y <= pageMetrics.y || (null !== (tempHeaderFooter = this.pages[pageIndex].header))) {
    if (y <= pageMetrics.y) {
      if (this.pages[pageIndex].header === null) {
        // TODO: if selection is not locked
        // Change the old editing mode so that when Undo / Redo returns to document editing mode
        this.logicDocument.setDocPosType(DocCurPosType.Content);
        this.logicDocument.startAction(HistoryDescriptionType.DocumentAddHeader);
        this.logicDocument.setDocPosType(DocCurPosType.HdrFtr);
        headerFooter = this.logicDocument.createSectionHeaderFooter(HeaderFooterType.Header, pageIndex);

        // if footer is also null, create as well
        // if (this.pages[pageIndex].footer == null) {
        //   this.logicDocument.createSectionHeaderFooter(HeaderFooterType.Footer, pageIndex);
        // }

        if (this.curHeaderFooter) {
          this.curHeaderFooter.removeSelection();
        }
        this.curHeaderFooter = headerFooter;
        this.logicDocument.recalculate(); // after that, para.pages have value
        this.logicDocument.endAction();
        // return false; // -> if selection is locked
      } else {
        headerFooter = this.pages[pageIndex].header;
      }
      // console.log(headerFooter);
    // tslint:disable-next-line: no-conditional-assignment
    } else if (y >= pageMetrics.yLimit || (null !== (tempHeaderFooter = this.pages[pageIndex].footer))) {

      if (this.pages[pageIndex].footer === null) {
        // TODO: if selection is not locked
        // Change the old editing mode so that when Undo / Redo returns to document editing mode
        this.logicDocument.setDocPosType(DocCurPosType.Content);
        this.logicDocument.startAction(HistoryDescriptionType.DocumentAddFooter);
        this.logicDocument.setDocPosType(DocCurPosType.HdrFtr);
        headerFooter = this.logicDocument.createSectionHeaderFooter(HeaderFooterType.Footer, pageIndex);

        // // if header is also null, create as well
        // if (this.pages[pageIndex].header == null) {
        //   this.logicDocument.createSectionHeaderFooter(HeaderFooterType.Header, pageIndex);
        // }

        if (this.curHeaderFooter) {
          this.curHeaderFooter.removeSelection();
        }
        this.curHeaderFooter = headerFooter;
        this.logicDocument.recalculate();
        this.logicDocument.endAction();
        // return false; // -> if selection is locked
      } else {
        headerFooter = this.pages[pageIndex].footer;
      }
      // console.log(headerFooter);
    }

    if (headerFooter === null) {
      // Do nothing and disable further processing of MouseUp and MouseMove
      this.waitMouseDown = true;
      return true;
    } else {
      this.waitMouseDown = false;
    }

    // Depending on the page and position on the page, we activate (make it current)
    // corresponding footer
    const oldHeaderFooter = this.curHeaderFooter;
    this.curHeaderFooter = headerFooter;
    // console.log(this.curHeaderFooter)
    const aab = this.logicDocument.getSectionHdrFtr(pageIndex, false);
    // console.log(aab);

    if ( null != oldHeaderFooter && (oldHeaderFooter !== this.curHeaderFooter || oldPage !== this.curPage) ) {
      // Delete the select if it was on the previous footer
      oldHeaderFooter.removeSelection();
    }

    if (this.curHeaderFooter != null) {
      this.curHeaderFooter.setSelectionStart(x, y, pageIndex, mouseEvent);
      if (bActivate === true) {
        const newMouseEvent: IMouseEvent = { bShiftKey: false, bCtrlKey: false, type: null, clickCount: -1};
        newMouseEvent.type = MouseEventType.MouseButtonUp;
        newMouseEvent.clickCount = 1;
        this.curHeaderFooter.setSelectionEnd(x, y, pageIndex, newMouseEvent);
        this.curHeaderFooter.content.moveCursorToStartPos(false);
      }
    }

    return true;
  }

  public setSelectionEnd(x: number, y: number, pageIndex: number, mouseEvent: IMouseEvent): void {
    if (this.waitMouseDown === true) {
      return;
    }

    if (this.curHeaderFooter != null) {
      // A select can only occur within a single footer, and a footer
      // cannot be divided into several pages
      let resY = y; // revisedY?
      if (this.curHeaderFooter.content.getDocPosType() !== DocCurPosType.DrawingObjects) {
        if (pageIndex > this.curPage) {
          resY = this.logicDocument.getPageLimits(this.curPage).yLimit + 10;
        } else if (pageIndex < this.curPage) {
          resY = -10;
        }
        pageIndex = this.curPage;
      }

      this.curHeaderFooter.setSelectionEnd(x, resY, pageIndex, mouseEvent);
      if (this.changeCurPageOnEnd === false) {
        this.curHeaderFooter.setPage(this.curPage);
      }
    }
  }

  public checkPosInSelection(pageIndex: number, pointX: number, pointY: number, nearPos?: number): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.checkPosInSelection(pageIndex, pointX, pointY, nearPos);
    }
  }

  public isSelectionEmpty(bContainParaEnd?: boolean): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.content.isSelectionEmpty(bContainParaEnd);
    }
  }

  public selectAll(): void {
    if (this.curHeaderFooter != null) {
      this.curHeaderFooter.selectAll();
    }
  }

  public getCurrentParagraph(): Paragraph {
    return this.curHeaderFooter.getCurrentParagraph();
  }

  public startSelectionFromCurPos(): void {
    if (this.curHeaderFooter != null) {
      this.curHeaderFooter.startSelectionFromCurPos();
    }
  }

  // -------------------------------
  // Internal (auxiliary) functions
  // -------------------------------
  public internalGetContentByXY(x: number, y: number, pageIndex: number): HeaderFooter {
    let header = null;
    let footer = null;
    if (this.pages[pageIndex] !== undefined) {
      header = this.pages[pageIndex].header;
      footer = this.pages[pageIndex].footer;
    }

    const pageH = this.logicDocument.getPageLimits(pageIndex).yLimit;

    if (y <= pageH / 2 && header != null) {
      return header;
    } else if (y >= pageH / 2 && footer != null) {
      return footer;
    } else if (header != null) {
      return header;
    } else {
      return footer;
    }
    return null;
  }

  // ----------------------------------
  // Functions for working with tables
  // ----------------------------------
  public addTableRow(bBefore: boolean): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.addTableRow(bBefore);
    }
    return false;
  }

  public removeTableRow(rowIndex?: number): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.removeTableRow(rowIndex);
    }
    return false;
  }

  public removeTableColumn(): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.removeTableColumn();
    }
    return false;
  }

  public mergeTableCells(): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.mergeTableCells();
    }

    return false;
  }

  public splitTableCells(rows: number, cols: number): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.splitTableCells(rows, cols);
    }

    return false;
  }

  public removeTable(): void {
    if (this.curHeaderFooter != null) {
      this.curHeaderFooter.removeTable();
    }
  }

  public canMergeTableCells(): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.canMergeTableCells();
    }

    return false;
  }

  // -------------------------
  // The Undo / Redo function
  // -------------------------
  // may TODO
  public getSelectionState(): void {
    //
  }

  // may TODO
  public setSelectionState(): void {
    //
  }
  // ------------------------------------
  // Functions for working with comments
  // ------------------------------------
  public addComment(): void {
    //
  }

  public addNewControl(property: INewControlProperty, sText?: string): number {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.addNewControl(property, sText);
    }
  }

  public isCursorInNewControl(): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.isCursorInNewControl();
    }
  }

  public canAddComment(): boolean {
    return false;
  }

  // public isPopWinNewControl(): boolean {
  //   return this.curHeaderFooter.isPopWinNewControl();
  // }

  public canInput(): boolean {
      return this.curHeaderFooter.canInput();
  }

  public canDelete(direction?: number): boolean {
      return this.curHeaderFooter.canDelete(direction);
  }

  public canInsertNewControl(): boolean {
      return this.curHeaderFooter.canInsertNewControl();
  }

  // ------------------------------------
  // Ungrouped functions
  // ------------------------------------
  public getSelectionBounds(): IDrawSelectionBounds {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.getSelectionBounds();
    }
    return null;
  }

  public getCurHeaderFooter(): HeaderFooter  {
    return this.curHeaderFooter;
  }

  public setCurHeaderFooter(headerFooter: HeaderFooter): void {
    if (this.curHeaderFooter != null) {
      this.curHeaderFooter.removeSelection();
    }
    this.curHeaderFooter = headerFooter;
  }

  public havePageCountElement(): number {
    let nStartPage = -1;
    const nPagesCount = this.logicDocument.pages.length;

    for (let nPageAbs = 0; nPageAbs < nPagesCount; ++nPageAbs) {
      const oPage = this.pages[nPageAbs];
      if (!oPage) {
        continue;
      }
      const oHeader = oPage.header;
      const oFooter = oPage.footer;
      if (oHeader && oHeader.havePageCountElement()) {
        oHeader.forceRecalculate(nPageAbs);
        if (nStartPage === -1) {
          nStartPage = nPageAbs;
        }
      }

      if (oFooter && oFooter.havePageCountElement()) {
        oFooter.forceRecalculate(nPageAbs);
        if (nStartPage === -1) {
          nStartPage = nPageAbs;
        }
      }
    }

    return nStartPage;
  }

  public getDocumentSelection(): Selection {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.getDocumentSelection();
    }
  }

  public getContentPosByXY(pageIndex: number, pointX: number, pointY: number): number {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.getContentPosByXY(pageIndex, pointX, pointY);
    }
  }

  public isFocusInNewControl(mouseEvent?: MouseEventHandler, pageIndex?: number): boolean {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.isFocusInNewControl(mouseEvent, pageIndex);
    }

    return false;
  }

  public getFocusInNewControl(mouseEvent?: MouseEventHandler, pageIndex?: number): NewControl {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.getFocusInNewControl(mouseEvent, pageIndex);
    }

    return null;
  }

  public getCursorInNewControl(): NewControl {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.getCursorInNewControl();
    }

    return null;
  }

  //// custom functions ////
  public getJustLeaveHeaderFooter(): boolean {
    return this.justLeaveHeaderFooter;
  }

  public setJustLeaveHeaderFooter(val: boolean): void {
    this.justLeaveHeaderFooter = val;
  }

  /**
   * check if the whole doc has headerfooter at all
   */
  public isHeaderFooterExistent(): boolean {
    let bHeaderFooterPage = true;
    const pages = this.pages;
    if (pages.length > 0) {
      const firstPage = pages[0];
      if (firstPage.header == null && firstPage.footer == null) {
        bHeaderFooterPage = false;
      }
    }
    if (this.getCurHeaderFooter() == null && bHeaderFooterPage === false) {
      return false;
    }

    return true;
  }

  public getDocumentElementState(): DocumentElementState[][] {
    let state = null;
    const hdrFtrState = { curHdrFtr: this.curHeaderFooter };
    if (this.curHeaderFooter != null) {
      state = this.curHeaderFooter.content.getDocumentElementState();
    } else {
      state = [];
    }

    state.push(hdrFtrState);
    return state;
  }

  public setDocumentElementState(state: any, stateIndex: number): void {
    if ( 0 >= state.length) {
      return ;
    }

    const hdrFtrState = state[stateIndex];
    this.curHeaderFooter = hdrFtrState.curHdrFtr;
    if ( null != this.curHeaderFooter ) {
      this.curHeaderFooter.content.setDocumentState(state, stateIndex - 1);
    }
  }

  public getFocusRevision(mouseEvent: MouseEventHandler, pageIndex: number): IRevisionChange[] {
    if ( this.curHeaderFooter ) {
      return this.curHeaderFooter.getFocusRevision(mouseEvent, pageIndex);
    }

    return null;
  }

  public acceptRevisionChanges(bAll: boolean = true): void {
    if ( this.curHeaderFooter ) {
      return this.curHeaderFooter.acceptRevisionChanges(bAll);
    }
  }

  public rejectRevisionChanges(bAll: boolean = true): void {
    if ( this.curHeaderFooter ) {
      return this.curHeaderFooter.rejectRevisionChanges(bAll);
    }
  }

  public setForceRecalculate(bForce: boolean): void {
    this.bForceRecalculate = bForce;
  }

  public getDocumentSectionType(): DocumentSectionType {
    if ( this.curHeaderFooter ) {
      return this.curHeaderFooter.getDocumentSectionType();
    }

    return DocumentSectionType.Document;
  }

  public getTableCellByXY(pointX: number, pointY: number, pageIndex: number): any {
    if (this.curHeaderFooter != null) {
      return this.curHeaderFooter.getTableCellByXY(pointX, pointY, pageIndex);
    }
  }

}

// tslint:disable-next-line: max-classes-per-file
export class HeaderFooterPage {

  public header: HeaderFooter;
  public footer: HeaderFooter;

  constructor() {
    this.header = null;
    this.footer = null;
  }
}
