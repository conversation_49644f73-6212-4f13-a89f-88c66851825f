import { DocumentBorder, DocumentShadow } from '../Style';
import { TableMeasurement, TableWidthType } from '../TableProperty';
// tslint:disable-next-line: max-line-length
import { BloodPressureFormat, CellTimeFormat, FONT_MAPPING, GenericBox, ICustomProps, INISCellGridLine, INISProperty, ITableCellFormulaPar, NISTableCellType, NIS_PROPERTY_DEFAULT, ResultType, TableCellRange, VertAlignType, isMacOs } from '../../../common/commonDefines';
import History from '../History';
import { ChangeTableCellProtected } from './TableCellChanges';
import ParaProperty from '../Paragraph/ParaProperty';
import TextProperty from '../TextProperty';
import { getDefaultFont } from '../../../common/commonMethods';

export enum VerticalMergeType {
    Restart,  // 无合并
    Continue,  // 合并
}

export enum TextDirectionType {
    LRTB,
}

/**
 * 单元格属性
 */
export class TableCellProperty {
    public gridSpan: number;  // 单元格是否有左右合并情况，默认没有合并 = 1，合并后 > 1

    // 单元格边框
    public borders: GenericBox<DocumentBorder>;

    // 单元格与内容之间的间距
    public margin: GenericBox<TableMeasurement>;

    public cellWidth: TableMeasurement;  // 单元格宽度
    public verticalMerge: VerticalMergeType;  // 单元格垂直合并类型

    public textDirection: TextDirectionType;  // 单元格文字书写方向

    public shadow: DocumentShadow;  // 单元格背景
    public vertAlign: VertAlignType;
    public formula: ITableCellFormulaPar;
    public nisProperty: INISProperty;

    // 表格斜线
    public bCellLeftSlash: boolean;
    public bCellRightSlash: boolean;

    // public bReadOnly: boolean;

    private bProtected: boolean;

    constructor(prop?: TableCellProperty) {
        this.borders = new GenericBox(undefined, undefined, undefined, undefined);

        this.margin = undefined;

        this.cellWidth = new TableMeasurement(TableWidthType.Auto, 0);
        this.verticalMerge = VerticalMergeType.Restart;
        this.gridSpan = 1;
        this.textDirection = TextDirectionType.LRTB;
        this.shadow = null; // new DocumentShadow();
        // this.bReadOnly = false;
        this.bProtected = false;
        this.vertAlign = VertAlignType.Top;
        this.formula = null;
    }

    // init(): void {
    //     this.borders = new GenericBox(undefined, undefined, undefined, undefined);

    //     this.margin = undefined;

    //     this.cellWidth = new TableMeasurement(TableWidthType.Auto, 0);
    //     this.verticalMerge = VerticalMergeType.Restart;
    //     this.gridSpan = 1;
    //     this.textDirection = TextDirectionType.LRTB;
    // }

    public copyMargins(): GenericBox<TableMeasurement> {
        return new GenericBox(this.margin.bottom.copy(), this.margin.left.copy(),
                                this.margin.right.copy(), this.margin.top.copy());
    }

    public copy(): TableCellProperty {
        const prop = new TableCellProperty();

        prop.gridSpan = this.gridSpan;
        prop.borders = new GenericBox(this.borders.bottom?.copy(), this.borders.left?.copy(),
        this.borders.right?.copy(), this.borders.top?.copy());
        prop.margin = this.margin ? this.copyMargins() : null;
        prop.cellWidth = new TableMeasurement(this.cellWidth.type, this.cellWidth.width);
        prop.verticalMerge = this.verticalMerge;
        prop.textDirection = this.textDirection;
        prop.shadow = ( null === this.shadow ) ? null : this.shadow.copy();
        prop.bProtected = this.bProtected;
        prop.vertAlign = this.vertAlign;
        prop.formula = null;
        // 单元格斜线
        prop.bCellLeftSlash = this.bCellLeftSlash;
        prop.bCellRightSlash = this.bCellRightSlash;
        // prop.type = this.type;
        // prop.bDiagonalLine = this.bDiagonalLine;
        if (this.nisProperty) {
            const nisProperty = prop.nisProperty = {...this.nisProperty};
            if (nisProperty.gridLine) {
                nisProperty.gridLine = {...nisProperty.gridLine};
            }
        }

        if ( this.nisProperty ) {
            prop.nisProperty = {
                customProperty: [],
            };

            for (const key in this.nisProperty) {
                if (key === 'items') {
                    // listbox/combox items
                    const items = this.nisProperty[key];
                    if (items != null && items.length > 0) {
                        const result = [];
                        for (const item of items) {
                            // need to detach from original obj

                            const detached = {...item};
                            // reset select status
                            detached.bSelect = false;
                            result.push(detached);

                        }
                        prop.nisProperty[key] = result;
                    }
                } else if ( 'customProperty' !== key ) {
                    // general
                    prop.nisProperty[key] = this.nisProperty[key];
                }
            }
        }

        return prop;
    }

    // public setReadOnly(bReadOnly: boolean): void {
    //     this.bReadOnly = bReadOnly;
    // }

    // public getReadOnly(): boolean {
    //     return this.bReadOnly;
    // }

    public setCellProtected(bProtect: boolean, history?: History): void {
        if ( bProtect === this.bProtected || null == bProtect) {
            return ;
        }

        if ( history ) {
            history.addChange(new ChangeTableCellProtected(this, this.bProtected, bProtect));
        }

        this.bProtected = bProtect;
    }

    public getCellProtected(): boolean {
        return this.bProtected;
    }

    public isTextCell(): boolean {
        return (this.nisProperty && NISTableCellType.Text === this.nisProperty.type);
    }

    public isDateCell(): boolean {
        return (this.nisProperty && NISTableCellType.Date === this.nisProperty.type);
    }

    public isTimeCell(): boolean {
        return (this.nisProperty && NISTableCellType.Time === this.nisProperty.type);
    }

    public isBPCell(): boolean {
        return false;
        // return (this.nisProperty && NISTableCellType.BloodPressure === this.nisProperty.type);
    }

    public isListCell(): boolean {
        return (this.nisProperty && NISTableCellType.List === this.nisProperty.type);
    }

    public isSignCell(): boolean {
        return (this.nisProperty && NISTableCellType.Signature === this.nisProperty.type);
    }

    public isQuickCell(): boolean {
        return (this.nisProperty && NISTableCellType.Quick === this.nisProperty.type);
    }

    public isNumberCell(): boolean {
        return (this.nisProperty && NISTableCellType.Number === this.nisProperty.type);
    }

    public getNISProperty(): INISProperty {
        return this.nisProperty;
    }

    public setNISDefaultProps(type: number, doc: any): boolean {
        let result = false;
        if (this != null && this.nisProperty != null) {

            // clear first
            // this.nisProperty = {
            //     bDiagonalLine: false,
            //     customProperty: [],
            // };
            this.resetNISProperty(doc);
            const nisProperty = this.nisProperty;
            // nisProperty.bDiagonalLine = false;
            // nisProperty.customProperty = [];

            let bProtected = ((this.isSignCell() && type !== NISTableCellType.Signature) ?
                                false : this.getCellProtected());

            switch (type) {
                case NISTableCellType.Text: {
                    result = this.setCellType(type);
                    break;
                }
                case NISTableCellType.List: {
                    result = this.setCellType(type);
                    nisProperty.items = NIS_PROPERTY_DEFAULT.items;
                    // nisProperty.prefixContent = NIS_PROPERTY_DEFAULT.prefixContent;
                    // nisProperty.selectPrefixContent = NIS_PROPERTY_DEFAULT.selectPrefixContent;
                    nisProperty.separator = NIS_PROPERTY_DEFAULT.separator;
                    nisProperty.bRetrieve = NIS_PROPERTY_DEFAULT.bRetrieve;
                    nisProperty.bShowValue = NIS_PROPERTY_DEFAULT.bShowValue;
                    nisProperty.bCheckMultiple = NIS_PROPERTY_DEFAULT.bCheckMultiple;
                    nisProperty.selectType = NIS_PROPERTY_DEFAULT.selectType;
                    break;
                }
                case NISTableCellType.Date: {
                    result = this.setCellType(type);
                    nisProperty.dateBoxFormat = NIS_PROPERTY_DEFAULT.dateBoxFormat;
                    nisProperty.customFormat = NIS_PROPERTY_DEFAULT.customFormat;
                    nisProperty.hideDateText = NIS_PROPERTY_DEFAULT.hideDateText;
                    nisProperty.minWarn = undefined;
                    nisProperty.maxWarn = undefined;
                    break;
                }
                case NISTableCellType.Time: {
                    result = this.setCellType(type);
                    break;
                }
                case NISTableCellType.BloodPressure: {
                    result = this.setCellType(type);
                    break;
                }
                case NISTableCellType.Signature: {
                    result = this.setCellType(type);
                    this.bProtected = true;
                    bProtected = true;
                    break;
                }
                case NISTableCellType.Quick: {
                    result = this.setCellType(type);
                    break;
                }
                case NISTableCellType.Number: {
                    result = this.setCellType(type);
                    nisProperty.minValue = NIS_PROPERTY_DEFAULT.minValue;
                    nisProperty.maxValue = NIS_PROPERTY_DEFAULT.maxValue;
                    nisProperty.precision = NIS_PROPERTY_DEFAULT.precision;
                    nisProperty.unit = NIS_PROPERTY_DEFAULT.unit;
                    break;
                }
                default: {
                    break;
                }
            }

            this.setCellProtected(bProtected);
        }

        return result;
    }

    public setNISProperty(props: INISProperty): number {
        // if (this.nisProperty != null) {
        //     for (let key in props) {
        //         this.nisProperty[key] = props[key];
        //     }
        // }
        // const propKeys = Object.keys(props);
        // for (const propKey of propKeys) {
        //     this.nisProperty[propKey] = props[propKey];
        // }

        const nisProps = this.nisProperty;

        if (props == null || nisProps == null) {
            return ResultType.UnEdited;
        }

        let res = ResultType.UnEdited;
        if (props.text != null && nisProps.text !== props.text && nisProps.type !== NISTableCellType.Date) {
            // datecell shouldn't handle .text here
            res = ResultType.Success;
            nisProps.text = props.text;
        }

        if (props.serialNumber !== null && typeof props.serialNumber === 'string'
            && props.serialNumber !== nisProps.serialNumber) {
            res = ResultType.Success;
            nisProps.serialNumber = props.serialNumber;
        }

        if (props.time != null && nisProps.time !== props.time) {
            res = ResultType.Success;
            nisProps.time = props.time;
        }
        // if (props.bloodPressureFormat != null && nisProps.bloodPressureFormat !== props.bloodPressureFormat) {
        //     res = ResultType.Success;
        //     nisProps.bloodPressureFormat = props.bloodPressureFormat;
        // }
        if (props.dateBoxFormat != null && nisProps.dateBoxFormat !== props.dateBoxFormat) {
            res = ResultType.Success;
            nisProps.dateBoxFormat = props.dateBoxFormat;
        }
        if (props.dateTime != null && nisProps.dateTime !== props.dateTime) {
            res = ResultType.Success;
            nisProps.dateTime = props.dateTime;
        }
        if (props.hideDateText != null && nisProps.hideDateText !== props.hideDateText) {
            res = ResultType.Success;
            nisProps.hideDateText = props.hideDateText;
        }
        if (props.timeFormat != null && nisProps.timeFormat !== props.timeFormat) {
            res = ResultType.Success;
            nisProps.timeFormat = props.timeFormat;
        }
        if (props.customProperty != null &&
            JSON.stringify(nisProps.customProperty) !== JSON.stringify(props.customProperty)) {
            res = ResultType.Success;
            nisProps.customProperty = props.customProperty.map((item) => {
                return {...item};
            });
        }

        if (props.items != null && JSON.stringify(nisProps.items) !== JSON.stringify(props.items)) {
        // if (props.items != null && nisProps.items !== props.items) {
            res = ResultType.Success;
            nisProps.items = props.items.map((item) => {
                // console.log(item)
                return item.copy();
            });
        }

        if (props.customFormat != null && nisProps.customFormat !== props.customFormat) {
            res = ResultType.Success;
            nisProps.customFormat = props.customFormat;
        }

        if (props.codeLabel != null && nisProps.codeLabel !== props.codeLabel) {
            res = ResultType.Success;
            nisProps.codeLabel = props.codeLabel;
        }

        if (props.valueLabel != null && nisProps.valueLabel !== props.valueLabel) {
            res = ResultType.Success;
            nisProps.valueLabel = props.valueLabel;
        }

        if (props.bShowCodeAndValue != null && nisProps.bShowCodeAndValue !== props.bShowCodeAndValue) {
            res = ResultType.Success;
            nisProps.bShowCodeAndValue = props.bShowCodeAndValue;
        }

        if (props.prefixContent != null && nisProps.prefixContent !== props.prefixContent) {
            res = ResultType.Success;
            nisProps.prefixContent = props.prefixContent;
        }

        if (props.selectPrefixContent != null && nisProps.selectPrefixContent !== props.selectPrefixContent) {
            res = ResultType.Success;
            nisProps.selectPrefixContent = props.selectPrefixContent;
        }

        if (props.separator != null && nisProps.separator !== props.separator) {
            res = ResultType.Success;
            nisProps.separator = props.separator;
        }

        if (props.bRetrieve != null && nisProps.bRetrieve !== props.bRetrieve) {
            res = ResultType.Success;
            nisProps.bRetrieve = props.bRetrieve;
        }

        if (props.bShowValue != null && nisProps.bShowValue !== props.bShowValue) {
            res = ResultType.Success;
            nisProps.bShowValue = props.bShowValue;
        }

        if (props.bCheckMultiple != null && nisProps.bCheckMultiple !== props.bCheckMultiple) {
            res = ResultType.Success;
            nisProps.bCheckMultiple = props.bCheckMultiple;
        }

        if (props.selectType != null && nisProps.selectType !== props.selectType) {
            res = ResultType.Success;
            nisProps.selectType = props.selectType;
        }

        if (props.minValue != null && nisProps.minValue !== props.minValue) {
            res = ResultType.Success;
            nisProps.minValue = props.minValue;
        }

        if (props.maxValue != null && nisProps.maxValue !== props.maxValue) {
            res = ResultType.Success;
            nisProps.maxValue = props.maxValue;
        }

        if (props.precision != null && nisProps.precision !== props.precision) {
            res = ResultType.Success;
            nisProps.precision = props.precision;
        }

        if (props.unit != null && nisProps.unit !== props.unit) {
            res = ResultType.Success;
            nisProps.unit = props.unit;
        }
        if (props.minWarn != null && nisProps.minWarn !== props.minWarn) {
            res = ResultType.Success;
            nisProps.minWarn = props.minWarn;
        }

        if (props.maxWarn != null && nisProps.maxWarn !== props.maxWarn) {
            res = ResultType.Success;
            nisProps.precision = props.maxWarn;
        }

        const newGridLine: INISCellGridLine = props.gridLine;
        if (newGridLine != null) {
            let bChange = false;
            const gridLine: INISCellGridLine = (nisProps.gridLine || {}) as any;
            if (newGridLine.height != null && newGridLine.height !== gridLine.height) {
                gridLine.height = newGridLine.height;
                bChange = true;
            }

            if (newGridLine.visible != null && newGridLine.visible !== gridLine.visible) {
                gridLine.visible = newGridLine.visible;
                bChange = true;
            }

            if (newGridLine.alignment != null && newGridLine.alignment !== gridLine.alignment) {
                gridLine.alignment = newGridLine.alignment;
                bChange = true;
            }

            if (newGridLine.bPrint != null && newGridLine.bPrint !== gridLine.bPrint) {
                gridLine.bPrint = newGridLine.bPrint;
                bChange = true;
            }
            if (bChange) {
                nisProps.gridLine = gridLine;
                res = ResultType.Success;
            }
        }

        // if (props.customProperty != null &&
        //     JSON.stringify(nisProps.customProperty) !== JSON.stringify(props.customProperty)) {
        //     res = ResultType.Success;
        //     nisProps.customProperty = props.customProperty;
        // }

        if (props.fontFamily != null && nisProps.fontFamily !== props.fontFamily) {
            res = ResultType.Success;
            nisProps.fontFamily = props.fontFamily;
            if (nisProps.fontFamily === '宋体' && isMacOs) {
                nisProps.fontFamily = FONT_MAPPING['宋体'].mac;
            }
        }

        if (props.fontSize != null && nisProps.fontSize !== props.fontSize) {
            res = ResultType.Success;
            nisProps.fontSize = props.fontSize;
        }

        if (props.paraSpacing != null && nisProps.paraSpacing !== props.paraSpacing) {
            res = ResultType.Success;
            nisProps.paraSpacing = props.paraSpacing;
        }

        if (props.alignType != null && nisProps.alignType !== props.alignType) {
            res = ResultType.Success;
            nisProps.alignType = props.alignType;
        }

        if (props.range != null && nisProps.range !== props.range) {
            res = ResultType.Success;
            nisProps.range = props.range;
        }

        if (props.bDiagonalLine != null && nisProps.bDiagonalLine !== props.bDiagonalLine) {
            res = ResultType.Success;
            nisProps.bDiagonalLine = props.bDiagonalLine;
        }

        return res;
    }

    public setNISProperty2(props: INISProperty): void {
        if (this.nisProperty && props.customProperty && props.customProperty.length) {
            this.nisProperty.customProperty = this.nisProperty.customProperty ? this.nisProperty.customProperty : [];
            props.customProperty.forEach((item) => {
                const index = this.nisProperty.customProperty.findIndex((obj) => {
                    return obj.name === item.name;
                });

                if (-1 === index) {
                    const prop: ICustomProps = {
                        type: item.type,
                        value: item.value,
                        targetValue: item.targetValue,
                        name: item.name,
                    };
                    this.nisProperty.customProperty.splice(this.nisProperty.customProperty.length - 1, 0, prop);
                } else {
                    this.nisProperty.customProperty[index].type = item.type;
                    this.nisProperty.customProperty[index].value = item.value;
                    this.nisProperty.customProperty[index].targetValue = item.targetValue;
                }
            });
        }
    }

    public setCellType(type: number): boolean {
        if ( !this.nisProperty || type === this.nisProperty.type ) {
            return false;
        }
        this.nisProperty.type = type;
        if (this.isTimeCell()) {
            this.nisProperty.timeFormat = CellTimeFormat.HM;
        } else if (this.isBPCell()) {
            this.nisProperty.bloodPressureFormat = BloodPressureFormat.ASlashB;
        }
        return true;
    }

    public getCellType(): number {
        return (this.nisProperty && this.nisProperty.type);
    }

    public isDiagonalLineCell(): boolean {
        return (this.nisProperty && this.nisProperty.bDiagonalLine);
    }

    public setDiagonalLine(): boolean {
        if ( !this.nisProperty ) {
            return ;
        }

        this.nisProperty.bDiagonalLine = !this.nisProperty.bDiagonalLine;
        return true;
    }

    public setDiagonalLine2(bLine: boolean): void {
        if (!this.nisProperty) {
            return;
        }
        this.nisProperty.bDiagonalLine = bLine;
    }

    public getCustomProperty(): ICustomProps[] {
        return (this.nisProperty ? this.nisProperty.customProperty.map((item) => {
            return {...item};
        }) : []);
    }

    /**
     * 新建单元格时，初始化单元格属性
     */
    public initNISProperty(doc: any): void {
        this.nisProperty = {
            type: NISTableCellType.Text,
            bDiagonalLine: false,
            customProperty: [],
        };

        this.resetContentNISProps(doc);
    }

    /**
     * 切换单元格类型时，重置单元格属性
     */
    public resetNISProperty(doc: any): void {
        this.nisProperty.bDiagonalLine = false;
        this.nisProperty.customProperty = [];

        this.resetContentNISProps(doc);
    }

    public getSerialNumber(): string {
        return this.nisProperty ? this.nisProperty.serialNumber : undefined;
    }

    /**
     * 重置单元格内容格式属性
     */
    private resetContentNISProps(doc: any): void {
        const paraProperty = new ParaProperty();
        const textProperty = new TextProperty(getDefaultFont(doc));

        this.nisProperty.fontFamily = textProperty.fontFamily;
        if (this.nisProperty.fontFamily === '宋体' && isMacOs) {
            this.nisProperty.fontFamily = FONT_MAPPING['宋体'].mac;
        }
        this.nisProperty.fontSize = textProperty.fontSize;
        this.nisProperty.paraSpacing = paraProperty.paraSpacing.lineSpacingType,
        this.nisProperty.alignType = 0; // 左对齐
        this.nisProperty.range = TableCellRange.Current;
    }
}
