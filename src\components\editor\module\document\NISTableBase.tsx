import React from 'react';
import { numtoFixed, numtoFixed2 } from '../../../../common/commonDefines';
import Content from './Content';

interface IProps {
    content: any;
    width: number;
    height: number;
    viewBox: string;
    cursorType: string;
    scale?: number;
    index?: number;
    host?: any;
    minPageIndex?: number;
    documentCore?: any;
    webModel?: boolean;
}

export default class NISTableBase extends React.Component<IProps, {}> {
    private _zIndex: any;
    private _minY: any;
    private _subHeaderY: any;
    private _left: any;
    private _right: any;
    private _header: any;
    private _leftHeader: any;
    private _scrollFn: (e: any) => void;
    private _rightHeader: any;
    private _pageHeight: number;
    private _bWebModel: boolean;
    private _contentRef: any;
    private _content: any;
    private _newData: any;
    private _cleft: any;
    private _cright: any;
    private _crightHeader: any;
    private _cleftHeader: any;
    private _cheader: any;
    constructor(props: IProps) {
        super(props);

        this._left = React.createRef();
        this._right = React.createRef();
        this._header = React.createRef();
        this._leftHeader = React.createRef();
        this._rightHeader = React.createRef();
        this._contentRef = React.createRef();
        this._cleft = React.createRef();
        this._cright = React.createRef();
        this._cheader = React.createRef();
        this._cleftHeader = React.createRef();
        this._crightHeader = React.createRef();
    }

    public componentDidMount(): void {
        setTimeout(() => {
            const dom = this.props.host.currentRef.current;
            if (!dom) {
                return;
            }
            // this._clientWidth = dom.clientWidth;
            // console.dir(dom);
            let scrollLeft: number = 0;
            let page; // = this.props.documentCore.getPageProperty();
            let minValue; // = page.paddingLeft;
            const current = this._left;
            const right = this._right;
            const header = this._header;
            const leftHeader = this._leftHeader;
            const rightHeader = this._rightHeader;
            let oldSubLeft: number = 0;
            let rightMaxValue;
            let oldSubTop: number = 0;
            let oldSubRight: number = 0;
            this._subHeaderY = 0;
            const scrollFn = this._scrollFn = (e: any) => {
                const target = e.target;
                const left = target.scrollLeft;
                // 头部冻结列向下移动
                if (left === scrollLeft) {
                    if (this._minY === undefined) {
                        return;
                    }
                    const minY = this._minY - this._subHeaderY;
                    if (minY === undefined || isNaN(minY)) {
                        return;
                    }
                    const scrollTop = target.scrollTop;
                    let subTop = scrollTop - minY;
                    // if (subTop < 0) {
                    //     return;
                    // }
                    const minPageIndex = this.props.minPageIndex;
                    let minYValue = 0;
                    if (minPageIndex !== 0) {
                        let marginHeight = 0;
                        if (this._bWebModel !== true) {
                            marginHeight = 20 * minPageIndex;
                            minYValue = -minY;
                        }
                        subTop -= minPageIndex * this._pageHeight + marginHeight;
                    }
                    if (subTop < minYValue) {
                        subTop = minYValue;
                    }
                    if (Math.abs(oldSubTop - subTop) < 1.5) {
                        return;
                    }
                    oldSubTop = subTop;
                    if (leftHeader.current) {
                        leftHeader.current.style.top = subTop + 'px';
                    }
                    if (header.current) {
                        header.current.style.top = subTop + 'px';
                    }
                    if (rightHeader.current) {
                        rightHeader.current.style.top = subTop + 'px';
                    }
                    return;
                }

                if (page === undefined) {
                    page = this.props.documentCore.getPageProperty();
                    minValue = page.paddingLeft;
                    rightMaxValue = dom.clientWidth - page.width + page.paddingRight - 16;
                    setTimeout(() => { // 防止每次滚动条移动都进行更新，这个只有窗口变动的时候才需要变动，但是现在为了兼容
                        page = undefined;
                    }, 10000);
                }

                scrollLeft = left;
                // 右固定列向右边移动
                if (right.current || rightHeader.current) {
                    let subWidth = left + rightMaxValue;
                    if (subWidth > 0) {
                        subWidth = 0;
                    }
                    if (Math.abs(oldSubRight - subWidth) > 1.5) {
                        oldSubRight = subWidth;
                        if (right.current) {
                            right.current.style.left = subWidth + 'px';
                        }
                        if (rightHeader.current) {
                            rightHeader.current.style.left = subWidth + 'px';
                        }
                    }
                }

                // 左冻结列向右边移动
                let val = scrollLeft - minValue;
                if (!current.current && !leftHeader.current) {
                    return;
                }

                if (val < 0) {
                    val = 0;
                }

                if (Math.abs(oldSubLeft - val) < 1.5) {
                    return;
                }

                oldSubLeft = val;
                if (current.current) {
                    current.current.style.left = val + 'px';
                }
                if (leftHeader.current) {
                    leftHeader.current.style.left = val + 'px';
                }
            };
            dom.firstChild.addEventListener('scroll', scrollFn);
        }, 10);
    }

    public componentWillUnmount(): void {
        const dom = this.props.host.currentRef.current;
        if (!dom) {
            return;
        }
        dom.firstChild.removeEventListener('scroll', this._scrollFn);
    }

    public render(): any {
        const {height, index, width, cursorType, viewBox, webModel} = this.props;
        let content = this.props.content;
        if (this._content) {
            content = this._content;
        }
        this._zIndex = 0;
        this._pageHeight = height;
        this._bWebModel = webModel;
        const res = this.setFixedTableContent(content, width, height, viewBox, cursorType, index);
        if (!res) {
            return null;
        }

        return res;
    }

    private refresh(options: any): boolean {
        if (!options) {
            return false;
        }
        if (options.ids && options.ids.length > 1) {
            // const id = options.ids[1];
            const ref = this.getRef(options.ids[1]);
            if (ref) {
                // const content = options.content;
                return ref.refresh(options);
            }
            return false;
        }
        this._content = options.content;
        this.setState({}, () => {
            this._content = null;
        });
        return true;
    }

    private getRef(id: number): any {
        const datas = this._newData;
        if (!datas) {
            return;
        }

        if (datas.tables && datas.tables.length && datas.tables[0].tableCells.find((item) => item.cellId === id)) {
            return this._cleft.current;
        }

        if (datas.rightTables && datas.rightTables.length &&
            datas.rightTables[0].tableCells.find((item) => item.cellId === id)) {
            return this._cright.current;
        }

        if (datas.headerTables && datas.headerTables.length &&
            datas.headerTables[0].tableCells.find((item) => item.cellId === id)) {
            return this._cheader.current;
        }

        const rightHeaderTables = datas.rightHeaderTables;
        if (rightHeaderTables && rightHeaderTables.length &&
            rightHeaderTables[0].tableCells.find((item) => item.cellId === id)) {
            return this._crightHeader.current;
        }

        const leftHeaderTables = datas.leftHeaderTables;
        if (leftHeaderTables && leftHeaderTables.length &&
            leftHeaderTables[0].tableCells.find((item) => item.cellId === id)) {
            return this._cleftHeader.current;
        }
    }

    /**
     * 设置表格的冻结列
     * @param contents 表格内容
     * @param width 宽度
     * @param height 高度
     * @param viewBox 画布
     * @param cursorType 光标类型
     * @param index 页索引
     * @returns 冻结内容
     */
    private setFixedTableContent(content: any, width: number, height: number, viewBox: string,
                                 cursorType: string, index: number): any {
        const newData = this.getFixedCell(content);
        let newTableContent: any[];
        if (newData) {
            // console.log(newData);
            newTableContent = [];
            const options: any = {id: newData.id, width, height, viewBox,
                cursorType, index, name: undefined, left: undefined};
            options.name = 'right';
            const position = this.getRightFixedPosition(height);
            const curLeft = options.left = position.x2;
            newTableContent.push(this.renderFixedSvg(newData.rightTables, options));
            options.name = 'left';
            options.left = position.x1;
            newTableContent.push(this.renderFixedSvg(newData.tables, options));
            options.name = 'header';
            options.left = undefined;
            options.top = position.y1;
            newTableContent.push(this.renderFixedSvg(newData.headerTables, options));
            options.left = curLeft;
            options.name = 'rightHeader';
            newTableContent.push(this.renderFixedSvg(newData.rightHeaderTables, options));
            options.name = 'leftHeader';
            options.left = position.x1;
            newTableContent.push(this.renderFixedSvg(newData.leftHeaderTables, options));
            // newTableContent.push(
            //     <svg key='selecton-svg' className='selecton-svg' width={width} height={height} viewBox={viewBox}/>
            // );
        }
        this._newData = newData;
        return newTableContent;
    }

    private renderFixedSvg(tables: any, options: any): any {
        if (!tables || tables.length === 0) {
            return;
        }
        const{width, height, index, cursorType, name, top} = options;
        let viewBox = options.viewBox;
        const tableOption = tables[0].option;
        let svgWidth = width;
        let svgHeight = height;
        if (tableOption) {
            const maxWidth = tableOption.maxWidth;
            const maxHeight = tableOption.maxHeight;
            const viewBoxs = viewBox.split(' ');
            // viewBoxs[0] = position.x;
            if (maxWidth > 0) {
                svgWidth = viewBoxs[2] = numtoFixed(maxWidth, 0);
            }

            if (maxHeight) {
                svgHeight = viewBoxs[3] = numtoFixed(maxHeight, 0);
            }
            viewBox = viewBoxs.join(' ');
        }

        const newTables = this.renderFixedCellContent({tables, paragraphs: [], name, id: options.id});
        const styles: any = {position: 'absolute', left: options.left, top: top || 0,
        marginTop: 0, zIndex: ++this._zIndex};

        return (
            <svg
                style={styles}
                ref={this['_' + name]}
                width={svgWidth}
                height={svgHeight}
                page-id={index}
                viewBox={viewBox}
                type='page'
                name={name}
                key={name}
                className='page-container'
                cursor={cursorType}
                preserveAspectRatio={'xMaxYMax slice'}
            >
                {newTables}
            </svg>
        );
    }

    private createMask(option: any, name: string): any {
        let height: any;
        switch (name) {
            case 'rightHeader': {
                height = option.maxHeight;
                break;
            }
            case 'right': {
                height = option.height;
                break;
            }
            default: {
                return;
            }
        }

        return (
            <defs>
                <clipPath id={'mask-' + name}>
                    <rect
                        x={option.minX - 1}
                        y={option.y}
                        width={option.maxWidth - option.minX + 1}
                        height={height + 1}
                        fill={'#fff'}
                    />
                </clipPath>
            </defs>
        );
    }

    private getRightFixedPosition(height: number): any {
        const obj = {x1: 0, x2: 0, y1: 0};
        const dom = this.props.host.currentRef.current;
        if (!dom) {
            return obj;
        }
        const first = dom.firstChild;
        if (!first) {
            return obj;
        }

        const page = this.props.documentCore.getPageProperty();
        const scrollLeft = first.scrollLeft;
        const scrollTop = first.scrollTop;
        const rightMaxValue = dom.clientWidth - page.width + page.paddingRight - 16;
        let x2 = scrollLeft + rightMaxValue;
        if (x2 > 0) {
            x2 = 0;
        }

        const minValue = page.paddingLeft;
        let x1 = scrollLeft - minValue;
        if (x1 < 0) {
            x1 = 0;
        }
        obj.x1 = x1;
        obj.x2 = x2;

        if (this._minY !== undefined) {
            const minY = this._minY - this._subHeaderY;

            let subTop = scrollTop - minY;
            const minPageIndex = this.props.minPageIndex;
            if (minPageIndex !== 0) {
                subTop -= minPageIndex * height;
            }
            if (subTop < minY) {
                subTop = 0;
            }
            obj.y1 = subTop;
        }

        // this._clientWidth = dom.clientWidth;
        // const subWidth = this._clientWidth - page.width - scrollLeft;

        return obj; // subWidth + page.paddingRight;
    }

    private renderFixedCellContent(tables: any): any {
        const {host, documentCore, index} = this.props;
        const id = tables.id;
        tables.tables.forEach((table) => {
            table.id = id;
            table.bNISTable = true;
        });
        return (
            <Content
                content={tables}
                pageIndex={index}
                documentCore={documentCore}
                host={host}
                ref={this['_c' + tables.name]}
                url={tables.url}
                key={tables.name + index}
                // bFromHeaderFooter={false}
                bFixedTable={true}
            />
        );
    }

    /**
     * 头部冻结列
     * @param lines 列
     * @param cells 单元格
     * @param option 其他参数
     * @returns 新的列
     */
    // private getFixedLine1(lines: any[], cells: any[], option: any): any[] {
    //     if (!lines || lines.length === 0) {
    //         return [];
    //     }

    //     let minY: number = 99999999;
    //     let maxY: number = 0;
    //     let minX = 99999999;
    //     let maxX = 0;

    //     if (cells && cells.length > 0) {
    //         let maxWidth = 0;
    //         cells.forEach((cell) => {
    //             const cutY1 = cell.y;
    //             const cutY2 = cutY1 + cell.height;

    //             if (cutY1 < minY) {
    //                 minY = cutY1;
    //             }

    //             if (cutY2 > maxY) {
    //                 maxY = cutY2;
    //             }

    //             const cutX1 = cell.x;
    //             const cutX2 = cell.width + cutX1;
    //             if (cutX1 < minX) {
    //                 minX = cutX1;
    //             }

    //             if (cutX2 > maxWidth) {
    //                 maxX = maxWidth = cutX2;
    //             }
    //         });
    //         option.maxY = maxY;
    //         option.maxHeight = maxY;
    //         option.maxWidth = maxWidth;
    //         option.minX = minX;
    //         option.y = minY - 1;
    //         this._minY = minY;
    //         maxY++;
    //         minX--;
    //         maxX++;
    //     } else {
    //         return;
    //     }

    //     const len = lines.length - 1;
    //     // let line = lines[len];
    //     // const x1 = line.x1 + '';
    //     // const x2 = line.x2 + '';
    //     // const xs = [x1, x2];
    //     const newLines = [];
    //     // line = lines[len - 4];
    //     // const x3 = line.x2 + '';
    //     // const rightLines = [];
    //     // const leftLines = [];
    //     const type = option.type;
    //     let bChanged = false;
    //     for (let index = len; index >= 0; index--) {
    //         const curLine = lines[index];
    //         const y = curLine.y2;
    //         if (y > maxY) {
    //             continue;
    //         }

    //         const actX1 = curLine.x1;
    //         const actX2 = curLine.x2;
    //         const subX1 = actX1 - minX;
    //         const subX2 = maxX - actX2;
    //         if (subX1 > 0 && subX2 > 0) {
    //             newLines.push(curLine);
    //             lines.splice(index, 1);
    //             // 左冻结列的右边需要阴影
    //             if (type === 'leftHeader' && subX2 < 1.1 && actX2 - actX1 < 0.5) {
    //                 curLine.bLeftLineGap = true;
    //                 if (bChanged === false) {
    //                     bChanged = true;
    //                     option.maxWidth += 8;
    //                 }
    //             // 右冻结列的左侧需要阴影
    //             } else if (type === 'rightHeader' && subX1 < 1.1 && actX2 - actX1 < 0.5) {
    //                 curLine.bRightLineGap = true;
    //             }

    //             if (maxY - y < 1.5 && maxY - curLine.y1 < 1.5) {
    //                 curLine.className = ' header-bottom';
    //             }
    //         }

    //         // newLines.push(curLine);
    //         // lines.splice(index, 1);
    //     }

    //     return newLines;
    // }

    // private getFixedLine(lines: any[], cells: any[], option: any): any[] {
    //     if (!lines || lines.length === 0) {
    //         return [];
    //     }

    //     const len = lines.length - 1;
    //     // let line = lines[len];
    //     // const x1 = line.x1 + '';
    //     // const x2 = line.x2 + '';
    //     // const xs = [x1, x2];
    //     const newLines = [];
    //     // line = lines[len - 4];
    //     // const x3 = line.x2 + '';
    //     // const rightLines = [];
    //     // const leftLines = [];
    //     // const type = option.type;
    //     // let bChanged = false;
    //     // for (let index = len; index >= 0; index--) {
    //     //     const curLine = lines[index];
  
    //     //     if (subX1 > 0 && subX2 > 0) {
    //     //         newLines.push(curLine);
    //     //         lines.splice(index, 1);
    //     //         // 左冻结列的右边需要阴影
    //     //         if (type === 'left' && subX2 < 1.1 && actX2 - actX1 < 0.5) {
    //     //             curLine.bLeftLineGap = true;
    //     //             if (bChanged === false) {
    //     //                 bChanged = true;
    //     //                 option.maxWidth += 8;
    //     //             }
    //     //         // 右冻结列的左侧需要阴影
    //     //         } else if (type === 'right' && subX1 < 1.1 && actX2 - actX1 < 0.5) {
    //     //             curLine.bRightLineGap = true;
    //     //         }
    //     //     }
    //     // }

    //     return newLines;
    // }

    private getFixedCell(data: any): any {
        const tables = data.tables;
        if (!tables || tables.length === 0) {
            return;
        }
        this._minY = undefined;
        // console.log(tables);
        const leftHeaderTables = [];
        const rightHeaderTables = [];
        let headerTables = [];
        const leftTables = [];
        const rightTables = [];
        let id: number;
        tables.forEach((table) => {
            const fixedLeft = table.fixedLeft || 0;
            const fixedRight = table.fixedRight || 0;
            if (fixedLeft === 0 && fixedRight === 0 && !table.bFixedHeader) {
                return;
            }
            id = table.id;
            const cells = table.tableCells;
            let newCells = [];
            // let x1: number = 0;
            // let x2: number = 999999999;
            // if (fixedLeft > 0 && cells[fixedLeft - 1]) {
            //     x1 = cells[fixedLeft - 1].x + 0.5;
            // }
            // if (fixedRight > 0 && cells[cells.length - fixedRight]) {
            //     x2 = cells[cells.length - fixedRight].x - 0.5;
            // }
            newCells = [];
            const cells1 = [];
            const cells2 = [];
            let bHeader: boolean = false;
            const bFixedHeader = table.bFixedHeader;
            const bHasHeader = headerTables.length > 0;
            const props = {
                left: {maxWidth: 0, maxHeight: 0, x1: 0, y1: 0},
                right: {maxWidth: 0, maxHeight: 0, x1: 9999999999999, y1: 0},
                header: {maxWidth: 0, maxHeight: 0, x1: 0, y1: 0},
                rightHeader: {maxWidth: 0, maxHeight: 0, x1: 0, y1: 0},
                leftHeader: {maxWidth: 0, maxHeight: 0, x1: 0, y1: 0},
            };
            const left: any = props.left;
            const right: any = props.right;
            const header: any = props.header;
            const leftHeader: any = props.leftHeader;
            const rightHeader: any = props.rightHeader;

            // let maxLeftWidth: number = 0;
            // let maxRightWidth: number = 0;
            let maxHeaderHeight: number = 0;
            // let maxHeaderWidth: number = 0;
            // let maxLeftHeight: number = 0;

            for (let index = 0, len = cells.length; index < len; index++) {
                const cell = cells[index];
                const x = cell.x;
                const y = cell.y;
                const height = cell.height;
                const width = cell.width;
                let bChange = false;
                const bCellHeader = cell.bHeader;

                // 统计右固定列最大宽度
                if (bFixedHeader && bCellHeader === true && !bHasHeader) {
                    bHeader = true;
                    headerTables.unshift(cell);
                    // cells.splice(index, 1);
                    bChange = true;
                    // if (cell.fixedType === 1) {
                    //     // leftHeader.maxWidth += width;
                    //     // leftHeader.maxHeight += height;
                    // } else if (cell.fixedType === 2) {
                    //     if (rightHeader.x1 === 0) {
                    //         rightHeader.x1 = x;
                    //     } else if ( rightHeader.x1 > x) {
                    //         rightHeader.x1 = x;
                    //     }
                    //     // rightHeader.maxWidth += width;
                    //     // rightHeader.maxHeight += height;
                    // } else {
                    //     if (header.x1 === 0) {
                    //         header.x1 = x;
                    //     } else if ( header.x1 > x) {
                    //         header.x1 = x;
                    //     }
                    //     // 表头宽度收集
                    //     const curWidth = x + width - header.x1;
                    //     if (maxHeaderWidth - curWidth < -0.25) {
                    //         maxHeaderWidth = curWidth;
                    //     }
                    //     // header.maxWidth += width;
                    //     // header.maxHeight += height;
                    // }
                } else if (cell.fixedType === 1) {
                    newCells.push(cell);
                    // cells.splice(index, 1);
                    cells1.unshift(cell);
                    bChange = true;
                    // if (left.y1 !== 0) {
                    //     left.y1 = y;
                    // }
                    // left.maxWidth += width;
                    // left.maxHeight += height;
                } else if (cell.fixedType === 2) {
                    newCells.push(cell);
                    cells2.unshift(cell);
                    // cells.splice(index, 1);
                    bChange = true;
                    // if (right.y1 !== 0) {
                    //     right.y1 = y;
                    // }
                    // if (right.x1 === 0) {
                    //     right.x1 = x;
                    // } else if ( right.x1 > x) {
                    //     right.x1 = x;
                    // }
                    // right.maxWidth += width;
                    // right.maxHeight += height;
                }

                // 统计header 最大高度
                if (bCellHeader) {
                    if (header.y1 === 0) {
                        header.y1 = y;
                    } else if (header.y1 > y) {
                        header.y1 = y;
                    }
                    const curHeight = y + height - header.y1;
                    if (maxHeaderHeight - curHeight < -0.25) {
                        maxHeaderHeight = curHeight;
                    }
                }

                // 统计左固定列最大宽度 (高度：去除表头行)
                const fixedType = cell.fixedType;
                if (fixedType === 1) {
                    // if (left.y1 === 0) {
                    //     left.y1 = y;
                    // }
                    const leftX = x + width;
                    if (left.x1 < leftX) {
                        left.x1 = leftX;
                    }
                    // const curWidth = x + width - left.x1;
                    // if (maxLeftWidth - curWidth < -0.25) {
                    //     maxLeftWidth = curWidth;
                    // }
                } else if (fixedType === 2) {
                    if (right.x1 > x) {
                        right.x1 = x;
                    }
                    // const curWidth = x + width - right.x1;
                    // if (maxRightWidth - curWidth < -0.25) {
                    //     maxRightWidth = curWidth;
                    // }
                }

                if (bChange && cell.cellBackground === 'none') {
                    cell.cellBackground = '#ffffff';
                }
            }

            let actTable: any = table;
            if (bHeader === false && !bHasHeader && table.bFixedHeader &&
                this.props.minPageIndex === this.props.index) {
                const newHeaders = this.props.documentCore.getTableHeaderContent(table.id);
                if (newHeaders) {
                    headerTables = newHeaders.tableCells;
                    bHeader = true;
                    actTable = newHeaders;
                }
            }
            const lineObj = this.getFixedLines(table.tableBorderLines, left.x1, right.x1, bFixedHeader);
            if (bHeader) {
                this._minY = header.y1;
                const lTables = [];
                const rTables = [];
                maxHeaderHeight += header.y1;
                for (let headerIndex = headerTables.length - 1; headerIndex >= 0; headerIndex--) {
                    const cell = headerTables[headerIndex];
                    const x = cell.x;
                    if (cell.fixedType === 1) {
                        lTables.unshift(cell);
                        headerTables.splice(headerIndex, 1);
                    } else if (cell.fixedType === 2) {
                        rTables.unshift(cell);
                        headerTables.splice(headerIndex, 1);
                    }
                }
                let current: any;
                // 头部左边冻结列
                current = this.addFixedTables(lTables, lineObj.leftHeader, 'leftHeader');
                if (current) {
                    // leftHeader.maxWidth = maxLeftWidth;
                    leftHeader.maxHeight = maxHeaderHeight;
                    leftHeader.type = current.option.type;
                    current.option = leftHeader;
                    leftHeaderTables.push(current);
                }
                // 头部右边冻结列
                current = this.addFixedTables(rTables, lineObj.rightHeader, 'rightHeader');
                if (current) {
                    // rightHeader.maxWidth = maxRightWidth;
                    rightHeader.maxHeight = maxHeaderHeight;
                    // rightHeader.x1 = right.x1;
                    rightHeader.type = current.option.type;
                    current.option = rightHeader;
                    rightHeaderTables.push(current);
                }
                // 头部冻结列
                current = this.addFixedTables(headerTables, lineObj.header, 'header');
                headerTables = [];
                if (current) {
                    // header.maxWidth = maxHeaderWidth + header.x1;
                    header.maxHeight = maxHeaderHeight;
                    header.type = current.option.type;
                    current.option = header;
                    headerTables.push(current);
                }
            }
            // 左边冻结列
            let item = this.addFixedTables(cells1, lineObj.left, 'left');
            if (item) {
                left.type = item.option.type;
                // left.maxWidth = maxLeftWidth;
                // left.maxHeight = maxLeftHeight;
                item.option = left;
                leftTables.push(item);
            }

            // 右边冻结列
            item = this.addFixedTables(cells2, lineObj.right, 'right');
            // this.updateRightCellX(item.tableCells, item.tableBorderLines);
            if (item) {
                // right.maxWidth = maxRightWidth;
                // right.maxHeight = maxLeftHeight;
                right.type = item.option.type;
                item.option = right;
                rightTables.push(item);
            }

            // 左右列
            // let other = {left: cells1, right: cells2, rightLines: undefined};
            // const lines = this.getFixedLine(table.tableBorderLines, cells1);
            // asideTables.push({tableBorderLines: lines, tableCells: newCells,
            //     tableBackground: [], other});
            // this.updateRightCellX(other.right, other.rightLines);
        });
        if (!leftTables.length && !rightTables.length && !headerTables.length && !leftHeaderTables.length
            && !rightHeaderTables.length) {
            return;
        }
        return {tables: leftTables, headerTables, leftHeaderTables, rightHeaderTables, rightTables, id};
    }

    private getFixedLines(lines: any[], maxLeftX: number, minRightX: number, bFixedHeader: boolean): any {
        const left = [];
        const right = [];
        const header = [];
        const leftHeader = [];
        const rightHeader = [];
        // let maxLeftX: number = 0;
        // let minRightX: number = 99999999999;
        lines.forEach((line) => {
            const fixedType = line.fixedType;
            if (line.bFixed) {
                if (bFixedHeader) {
                    if (fixedType === 1) {
                        leftHeader.push(line);
                    } else if (fixedType === 2) {
                        rightHeader.push(line);
                    } else {
                        header.push(line);
                    }
                } else {
                    if (fixedType === 1) {
                        left.push(line);
                    } else if (fixedType === 2) {
                        right.push(line);
                    }
                }
            }

            if (![1, 2].includes(fixedType)) {
                if (Math.abs(maxLeftX - line.x2) < 0.25) {
                    if (bFixedHeader && line.bHeader) {
                        leftHeader.push(line);
                    } else {
                        left.push(line);
                    }
                }
            }
        });
        if (left[0]) {
            left[0].maxLeftX = maxLeftX;
        }
        if (leftHeader[0]) {
            leftHeader[0].maxLeftX = maxLeftX;
        }
        if (right[0]) {
            right[0].minRightX = minRightX;
        }
        if (rightHeader[0]) {
            rightHeader[0].minRightX = minRightX;
        }

        return {left, right, header, leftHeader, rightHeader};
    }

    private addFixedTables(cells: any[], lines: any[], type: string): any {
        if (cells.length === 0) {
            return;
        }

        const option = {
            type,
        };

        return {tableBorderLines: lines, tableCells: cells, tableBackground: [], option};
    }
}
