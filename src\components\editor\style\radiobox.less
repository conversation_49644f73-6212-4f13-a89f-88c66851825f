@import './global.less';
.hz-editor-container  .editor-radiobox {
    display: inline-block;
    // padding-top: 4px;
}
.hz-editor-container  .editor-radiobox .radiobox-item {
    display: inline-block;
    position: relative;
    margin-right: 15px;
    font-family: @fontFamily;
    vertical-align: middle;
    box-sizing: border-box;
    width: 100%;
    // padding: 5px 0;

    & > * {
        vertical-align: middle;
    }
    
    & > input[type='radio'] {
        position: absolute;
        top: 0;
        width: 100%;
        // height: 14px;
        height: 100%;
        padding: 0;
        margin: 0;
        z-index: 2;
        cursor: pointer;
        opacity: 0;
    }

    & > i {
        display: inline-block;
        width: 14px;
        height: 14px;
        background: #fff;
        border: 1px solid @borderColor;
        font-style: normal;
        vertical-align: middle;
        border-radius: 50px;
        // top: 5px;
    }

    // & > label.radio-label {
    //     display: inline-block;
    //     line-height: 14px;
    //     font-size: 14px;
    //     vertical-align: top;
    //     cursor: pointer;
    //     white-space: normal;
    //     position: relative;
    //     top: 5px;
    // }

    &.disabled  {
        & > input[type=radio] {
            display: none;
        }
        & > label {
            cursor: default;
        }

        & > i {
            background-color: @disabledColor;
        }
    }

    .radiobox-icon-checked {
        border-color: #009fff;
    }

    .radiobox-icon-checked:before {
        content: '';
        pointer-events: none;
        position: absolute;
        color: white;
        border: 1px solid;
        background-color: white;
    }

    .radiobox-icon-checked:before {
        display: inline-block;
        position: relative;
        width: 8px;
        height: 8px;
        bottom: 3px;
        left: 2px;
        background: #009FFF;
        font-style: normal;
        border-radius: 50px;
    }
}
