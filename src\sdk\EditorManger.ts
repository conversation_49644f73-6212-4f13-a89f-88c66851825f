import IAsyncExternalInterface, { IExternalEvent } from '../common/external/IAsyncExternalInterface';
import client from './client';

interface IInitOptions {
  dom: Element;
  src: string;
}

interface IEditor extends IAsyncExternalInterface {
  id: string;
}

export class EditorManger {

  private iframe: HTMLIFrameElement;

  public async init(options: IInitOptions): Promise<this> {
    const { dom, src } = options;
    const iframe = document.createElement('iframe');
    iframe.width = '100%';
    iframe.height = '100%';
    iframe.frameBorder = '0';
    iframe.src = src;
    iframe.scrolling = 'no';
    dom.appendChild(iframe);

    //iframe加载完成
    await new Promise<void>(function (resolve, reject) {
      iframe.onload = () => resolve();
    });
    this.iframe = iframe;

    //初始化编辑器
    await this.invork(null, 'init');

    return this;
  }

  public async createEditor(options: {bShowMenu: boolean; bShowToolbar: boolean;}): Promise<IEditor> {
    const id: string = await this.invork(null, 'createEditor', [options]);
    const editorProxy =  new Proxy({} as IEditor, {
      get: (target, method: string) => {
        //修复async函数自动调用then
        if(method === 'then') {
          return;
        }
        if(method === 'id') {
          return id;
        } else {
          return async (...params) => {
            return await this.invork(id, method, params);
          }
        }
      }
    });
    return editorProxy;
  }

  public async setCurrentEditor(id: string): Promise<boolean> {
    const currentId: string = await this.invork(null, 'setCurrentEditor', [id]);
    return id === currentId;
  }

  public async removeEditor(id: string): Promise<boolean> {
    const deletedId: string = await this.invork(null, 'removeEditor', [id]);
    this.removeEvent(id);
    return id === deletedId;
  }

  public setEvent(editorId: string, events: IExternalEvent): void {
    client.setEditorEvent(editorId, events);
  }

  public removeEvent(editorId: string, sNames?: string[]): void {
    client.removeEditorEvent(editorId, sNames);
  }


  private async invork(editorId: string, method: string, params: any[] = []): Promise<any> {
    return await client.invork(this.iframe, editorId, method, params);
  }

}
