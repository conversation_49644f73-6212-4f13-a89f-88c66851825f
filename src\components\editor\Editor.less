
body {
  background: #F0F2F5;
  text-align: center;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

input {
  padding: 2px;
  border: 1px solid #bbb;
}

svg {
  display: block;
  background: white;
  margin: 0px auto;
  // cursor: text;
}

svg+svg {
  margin-top: 20px;
}

.page-list::-webkit-scrollbar{
  display: none; 
}

.page-wrapper svg.print-style {
  margin: initial; /* make sure svg container always left aligned */
}

/*添加页面间的margin*/
.page-wrapper:first-child {
  margin-top:0;
}
.page-wrapper {
  /* background-color: #fff; */
  box-shadow:0px 0px 8px 0px rgba(0,0,0,0.16);
  /* text-align: left;  */
}

/*text {*/
  /*font-size: 0;*/
/*}*/

/*text tspan {*/
  /*font-size: 14px;*/
  /*font-family: Arial, 'Times New Roman', 宋体;*/
/*}*/

.hz-editor-config {
  width: 200px;
  text-align: left;
  background: white;
  font-size: 12px;
  padding: 15px;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  box-shadow: -3px 5px 10px rgba(0, 0, 0, 0.36);
  z-index: 99;
}

.menu-panel {
  position: fixed;
  top: 0;
  z-index: 99;
  width: 100%;
  user-select: none;
}

.hz-editor-config>div {
  margin-bottom: 10px;
}

.hz-editor-config>div>div+div {
  margin-top: 8px;
}

/* background-color is in config */
.hz-editor-menu {
  width: 100%;
  min-width: 820px;
  /* margin-left: 200px; */
  /* background-color: #F5F6F7; */
  /* background-color: #f3f2f1; */
  /* padding: 10px; */
  padding: 0.2cm 0.5cm;
  /* margin-bottom: 10px; */
  /* margin-bottom: 0.5cm */
  /* box-sizing: border-box; */
}

.hz-editor-menu .menu-tab {
  display: inline-block;
  text-align:left;
  width: 100px;
  /* border: 1px solid; */
}

div[aria-label='grid'] {
  outline: none;
  /* overflow: visible !important; */
  /* margin-top: 0.5cm; */
  /* margin-top: 105px; */
}

.menu-tab {
  position: relative;
}

/* color set in config */
.menu-tab .dropdown {
  position: absolute;
  z-index: 99;
  left: -10px;
  top: 30px;
  /* background-color: #F5F6F7; */
  /* background-color: #f3f2f1; */
  /* pointer-events: none; */
}

.menu-tab .dropdown div {
  padding: 10px;
  width: 80px;
}

/* color set in config */
.hz-editor-toolbar {
  width: 100%;
  /* margin-left: 200px; */
  /* background-color: #F5F6F7; */
  /* background-color: #f3f2f1; */
  /* padding: 10px; */
  padding: 0.2cm;
  /* margin-bottom: 10px; */
  /* margin-bottom: 0.5cm; */
  align-items: center; /* 垂直居中 */
  /* text-align: left;
  padding-left: 20px; */
  /* box-sizing: border-box; */
}

.hz-editor-toolbar-single > div{
  display: inline-block;
  /* font-size: 12px; */
  margin-right: 10px;

  position: relative;
  /* 230/2 */
  right: 115px; 
}

.hz-editor-toolbar .toolbar-icon{
  position: relative;
}

.hz-editor-toolbar .italic_icon {
  position: absolute;
  left: 20px;
  top: 0;
}

.hz-editor-toolbar .underline_icon {
  position: absolute;
  left: 40px;
  top: 0;
}

.hz-editor-toolbar .superscript_icon {
  position: absolute;
  left: 60px;
  top: 0;
}

.hz-editor-toolbar .subscript_icon {
  position: absolute;
  left: 80px;
  top: 0;
  padding-right: 10px;
  border-right: 1px solid;
}

.hz-editor-toolbar .alignleft_icon {
  position: absolute;
  left: 115px;
  top: 0;
}

.hz-editor-toolbar .alignright_icon {
  position: absolute;
  left: 135px;
  top: 0;
}

.hz-editor-toolbar .justify_icon {
  position: absolute;
  left: 155px;
  top: 0;
  padding-right: 10px;
  border-right: 1px solid;
}

.hz-editor-toolbar .font-color_icon {
  position: absolute;
  left: 190px;
  top: 0;
}

.background-color_icon {
  position: absolute;
  left: 210px;
  top: 0;
  /* padding-right: 10px;
  border-right: 1px solid; */
}

.hz-editor-toolbar svg+svg {
  margin-top: 0;
}

/* #hz-editor-app {
  margin-right: 230px;
} */
tspan {
  -moz-user-select:none;/*火狐*/
  -webkit-user-select:none;/*webkit浏览器*/
  -ms-user-select:none;/*IE10*/
  user-select:none;
}
.config-title {
  font-size: 10px;
  color: #555;
  text-align: right;
}

#textarea_input {
  position: absolute;
  z-index: 0;
  width: 0.5em;
  height: 1em;
  opacity: 0;
  background: transparent;
  -moz-appearance: none;
  appearance: none;
  border: none;
  resize: none;
  outline: none;
  overflow: hidden;
  font: inherit;
  padding: 0 1px;
  margin: 0 -1px;
  contain: strict;
  -ms-user-select: text;
  -moz-user-select: text;
  -webkit-user-select: text;
  user-select: text;
  /* white-space: pre !important; */
  pointer-events:none;
}
tspan, text {
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    user-select:none;
}

#input_display_win {
  position: absolute;
  z-index: 0;
  min-height: 16px;
//   width: 100%;
//   opacity: 0;
  display: inline-block;
  background: white;
  -moz-appearance: none;
  appearance: none;
  border: none;
  resize: none;
  outline: none;
  overflow: hidden;
  font: inherit;
  padding: 0 1px;
  margin: 0 -1px;
  -ms-user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  /* white-space: pre !important; */
  pointer-events:none;
}

.selection {
  fill: white;
  opacity: 0;
}
.selection-selected {
  /* fill: cornflowerblue; */
  fill: #009FFF;
  opacity: 0.5;
}
.newcontrol-focus {
  /* fill: cornflowerblue; */
  fill: #009FFF;
  opacity: 0.2;
}

.hz-editor-menu  .config-window-container {
  height: 100vh;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.02);
}

.hz-editor-menu .config-window {
  background-color: #F5F6F7;
  width: 500px;
  height: 250px;
  position: absolute;   
  top: 50%;
  left: 50%;
  border: 1px solid;
  transform: translate(-50%, -50%);
  text-align: left;
  padding: 20px;
}

form input:invalid {
  border: 1px solid red;
  /* background-color: pink; */
}

form input:focus { 
  outline: none; 
}

.hz-editor-menu .equation-window {
  height: 300px;
  width: 410px;
}

.equation-window .left-cell {
  width: 25%;
  display:inline-block;
}

.equation-window .preview-label {
  position: relative;
  top: -60px;
}

.equation-window .right-cell {
  margin-left: 10px;
  width: 68%;
}

.equation-window .preview-image {
  height: 120px;
}

.equation-window .error-popup, .config-window-container .error-popup {
  text-align: center;
  color: red;
}

.eqution-edit-window .config-descriptor {
  margin-bottom: 40px;
  height: 17px;
}

.new-control .config-descriptor {
  margin-bottom: 15px;
}

.new-control .detail-block-container {
  border-top: 1px solid;
  position: relative;
  margin-top: 20px;
  padding-top: 10px;
}

.new-control .new-control-label {
  position: absolute;
  top: -10px;
  background-color: #F5F6F7;
}

.new-control .new-control-descriptor {
  display: inline-block;
  width: 60px;;
}

.new-control .new-control-date-format {
  background-color: white;
  overflow-y: scroll;
  font-size: 13px;
  margin-top: 15px;
  margin-left: 15px;
  height: 100px;
}

.date-format-selected {
  background-color: lightblue;
}

.eqution-edit-window .left-item {
  padding-right: 10px;
  border-right: 1px solid;
  padding-left: 20px;
}

.eqution-edit-window .left-item.top-item {
  border-bottom: 1px solid;
}

.eqution-edit-window .left-item.bottom-item {
  border-top: 1px solid;
}

.eqution-edit-window .right-item {
  padding-left: 30px;
  border-left: 1px solid;
}

.eqution-edit-window .right-item.top-item {
  border-bottom: 1px solid;
}

.eqution-edit-window .right-item.bottom-item { 
  border-top: 1px solid;
}

.eqution-edit-window .top-item {
  padding-bottom: 10px;
}

.eqution-edit-window .bottom-item {
  padding-top: 10px;
}

.eqution-edit-window input {
  width: 120px;
}

.hz-editor-menu .input-window {
  height: 400px;
  overflow-y: scroll;
}

.eqution-edit-window .menstruation-checkbox {
  width: 15px;
  margin-bottom: 30px;
}

.eqution-edit-window .menstruation-text, .eqution-edit-window .menstruation-date {
  margin-bottom: 10px;
}

.eqution-edit-window .menstruation-upper-block, .eqution-edit-window .menstruation-bottom-block {
  position: relative;
  top: 10px;
  padding-left: 5px;
}

.eqution-edit-window .menstruation-upper-block {
  border-bottom: 1px solid;
  padding-bottom: 5px;
}

.menstruation-block div {
  padding-left: 10px;
}

.eqution-edit-window .menstruation-bottom-block {
  padding-top: 10px;
}

.button-area {
  position: relative;
  height: 160px;
  margin-top: 30px;
}

.input-area .del-button {
  margin-left: 20px;
}

.config-window .config-format-container, .config-window .config-spacing-container {
  width: 50%;
  float: left;
}

.button-container {
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.button-container  .button {
  margin-right: 20px;
  font-size: 13px;
}

.config-window .detail-block {
  margin: 10px;
}

.config-window-container .fraction-block {
  text-align: center;
  padding: 20px 0;
  margin: 0;
}

.config-window-container .fraction-block:nth-child(2) {
  border-bottom: 1px solid;
}

.config-window-container .menstruation-block {
  width: 33%;
  height: 200px;
  display: inline-block;
}

.config-window-container .menstruation-block:nth-child(4) {
  box-sizing: border-box;
  padding-left: 10px;
}

.equation-detail-block:nth-child(2) {
  margin-bottom: 0;
}

.equation-detail-block:nth-child(3) {
  margin-top: 0;
}

.config-window .image-detail-block {
  margin-top: 20px;
}

.detail-block .detail-item {
  width: 50%;
  display: inline-block;
}

.eqution-edit-window .detail-block .detail-item {
  width: 46%;
  box-sizing: border-box;
}

.detail-block .detail-item label {
  width: 80px;
  display: inline-block;
}

.image-detail-block .image-checkbox {
  font-size: 30px;
  margin-left: 2px;
}

.image-detail-block .text-disabled {
  opacity: 0.5;
}

.test-text {
  width: 100%;
}

.paragraph-window .left-cell {
  width: 30%;
}

.paragraph-window label.left-cell {
  display: inline-block
}

.paragraph-window select.left-cell {
  font-size: 14px
}

.paragraph-window .right-cell {
  margin-left: 10px
}

.image-processor-nw {
  cursor: nw-resize;
}

.image-processor-ne {
  cursor: ne-resize;
}

.image-processor-sw {
  cursor: sw-resize;
}

.image-processor-se {
  cursor: se-resize;
}

.image-processor-n {
  cursor: n-resize;
}

.image-processor-s {
  cursor: s-resize;
}

.image-processor-e {
  cursor: e-resize;
}

.image-processor-w {
  cursor: w-resize;
}

image {
  cursor: all-scroll;
}

image.select-button {
  cursor: pointer;
}

.opacity {
  opacity: 0.5;
  pointer-events: none;
}

.header-footer-enabled {
  opacity: 0.5;
  pointer-events: none;
}

.hide, .hidden {
  display: none;
}

.right-menu {
  display: none;
}

.paragraphline-container {
  pointer-events: none;
}

.table .table-container, .table .tablecell-container {
  pointer-events: none;
}

/* .right-menu-box > ul > li:nth-child(3) {
  position: relative;
}

.right-menu-box > ul > li:nth-child(3) > input {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  background-color: transparent;
  border: none;
}

.right-menu-box > ul > li:nth-child(3) > input:focus {
  outline: none;
} */

.right-menu-box.right-menu-box-disabled > ul > .cut,
.right-menu-box.right-menu-box-disabled > ul > .copy,
.right-menu-box.right-menu-box-disabled > ul > .delete,
.right-menu-box.un-access > ul > li.format,
.right-menu-box:not(.ownerEditor) > ul > li.unformat {
  color: #ccc;
  cursor: default;
  background: none;
}


.right-menu-box  li:hover {
  background: #eee;
}

.editor-copy-input-container {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: -10%;
  width: 1px;
  height: 1px;
  background-color: #fff;
  opacity: 0;
}

.editor-copy-input-container > div {
  position: relative;
  z-index: 999;
}

.right-menu-repaste {
  display: none;
  position: absolute;
  left: 100px;
  top: 80px;
  z-index: 10;
  min-width: 40px;
  /* height: 138px; */
  padding: 0;
  margin: 0;
  font-size: 12px;
  font-family: '微软雅黑';
  text-align: left;
  box-shadow: 0 0 3px #333;
}
.right-menu-repaste.active {
  display: block;
}
.right-menu-repaste > .repaste-ctr {
  height: 20px;
  padding-right: 6px;
  line-height: 20px;
  text-align: right;
  cursor: pointer;
  background: #fff;
}
.right-menu-repaste > .repaste-ctr > span {
  display: inline-block;
  transform: rotate(180deg);
}
.right-menu-repaste > .repaste-btns {
  display: none;
  padding: 0 3px;
  line-height: 20px;
  background: #fff;
}
.right-menu-repaste > .repaste-btns.active {
  display: block;
}

.right-menu-repaste > .repaste-btns > span {
  cursor: pointer;
}

.right-menu-redialog {
  display: none;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  width: 100%;
  height: 100%;
}

.right-menu-redialog.active {
  display: block;
}
.right-menu-redialog-box {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  width: 250px;
  height: 245px;
  margin: auto;
  padding: 10px 20px;
  font-size: 12px;
  font-family: '微软雅黑';
  text-align: left;

  background: #fff;
  box-shadow: 0 0 3px #333;
}
.right-menu-redialog-box > .text-dec > p {
  height: 28px;
  margin: 0;
  padding: 0;
  line-height: 28px;
}

.right-menu-redialog-box > .dialog-content {
  height: 80px;
  padding-bottom: 15px;
  border: 1px solid #aaa;
  overflow: hidden;
}

.right-menu-redialog-box > .dialog-content .Embed-Data {
  display: none;
}
.right-menu-redialog-box > .text-dec > p:nth-child(2) > span:nth-child(2) {
  padding-left: 60px;
}

.right-menu-redialog-box > .dialog-btn {
  padding: 10px 0;
  text-align: right;
}

.right-menu-redialog-box > .dialog-btn > button {
  padding: 2px 20px;
  cursor: pointer;
  border: 1px solid #ccc;
}

.right-menu-redialog-box > .dialog-btn > button:hover {
  background-color: #f2f2f2;
}

.contextMenu-container{
  background-color: azure;
  padding: 5px 0;
  /* position: relative; */
  position: absolute;
  width: 80px;
  height: 100%;
}

.contextMenu-container .contextMenu-item {
  margin-top: 10px;
  cursor: pointer;
}

/* table pop menu */
.table-pop-menu {
  /* display: block; */
  position: fixed;
  /* top: 50%;
  left: 50%; */
  width: 100%;
  height: 100%;
}

/* modal */
.table-pop-menu  .config-window-container {
  height: 100vh;
  width: 100%;
  position: relative;
}

.table-pop-menu .config-window {
  background-color: #F5F6F7;
  width: 200px;
  height: 180px;
  position: absolute;   
  top: 50%;
  left: 50%;
  border: 1px solid;
  transform: translate(-50%, -50%);
  text-align: left;
  padding: 20px;
}
.toolbar-icon > svg:hover {
  background: #ccc;
  cursor: pointer;
}

.new-control {
  /* display: block; */
  position: fixed;
  /* top: 50%;
  left: 50%; */
  width: 100%;
  height: 100%;
}

/* modal */
.new-control  .config-window-container {
  height: 100vh;
  width: 100%;
  position: relative;
}

.new-control .config-window {
  background-color: #F5F6F7;
  width: 280px;
  height: 450px;
  position: absolute;   
  top: 50%;
  left: 50%;
  border: 1px solid;
  transform: translate(-50%, -50%);
  text-align: left;
  padding: 10px;
  font-size: 12px;
}

.new-control-combox {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.new-control-combox  .config-window-container {
  height: 100vh;
  width: 100%;
  position: relative;
}

.new-control-combox .config-window {
  background-color: #F5F6F7;
  width: 350px;
  height: 540px;
  position: absolute;   
  top: 40%;
  left: 50%;
  border: 1px solid;
  transform: translate(-50%, -50%);
  text-align: left;
  padding: 10px;
  font-size: 12px;
}

.new-control-combox .detail-item {
  width: 30%;
  display: inline-block;
}

.new-control-combox .detail-block {
  margin: 5px;
}

.new-control-combox .right-cell {
  margin-left: 10px;
  width: 25%;
}

.new-control-combox .table-container {
  width: 180px;
  height: 130px;
  overflow-y: auto;
}

.new-control-combox .table {
  border-collapse: collapse;
  letter-spacing: 1px;
  font-family: sans-serif;
  font-size: .8rem;
}

.new-control-combox .table-th {
  width: 100px;
}

.add-new-control-item {
  /* display: block; */
  position: fixed;
  /* top: 50%;
  left: 50%; */
  width: 100%;
  height: 100%;
  z-index: 3;
}

/* modal */
.add-new-control-item  .config-window-container {
  height: 100vh;
  width: 100%;
  position: relative;
}

.add-new-control-item .config-window {
  background-color: #F5F6F7;
  width: 300px;
  height: 150px;
  position: absolute;   
  top: 50%;
  left: 50%;
  border: 1px solid;
  transform: translate(-50%, -50%);
  text-align: left;
  padding: 20px;
}

.new-control-combox-list {
    position: absolute;
    display: inline-block;
    visibility: visible;
    min-width: 150px;
    background-color: #F0F2F5;
    /* color: #A8A8A8; */
    text-align: left;
    font-size: 12px;
    padding: 3px;
    border: 1px solid #000000;
    box-shadow: -3px 5px 10px rgba(0, 0, 0, 0.36);
    /* border-radius: 6px; */

    pointer-events: visible;
    z-index: 1;
}

/* .new-control-combox-list .combox-list {
    visibility: visible;
    min-width: 150px;
    background-color: #F0F2F5;
    text-align: left;
    font-size: 12px;
    padding: 3px;
    border: 1px solid #000000;
    box-shadow: -3px 5px 10px rgba(0, 0, 0, 0.36);

    pointer-events: visible;

    position: absolute;
    z-index: 1;
} */

.new-control-combox-list .combox-item-list {
  max-height: 200px;
  overflow-y: auto;
}

.new-control-combox-list .combox-item {
  width: auto;
  height: auto;
  margin-top: 1px;
  margin-bottom: 1px;
}

.combox-list-button-container {
  /* margin-top: 5px; */
  /* bottom: 20px; */
  margin-left: 5px;
}

.new-control-combox-list button {
  margin-left: 5px;
  font-size: 12px;
}

.new-control-combox-list input[type="checkbox"] {
  margin: 0 3px 0 0;
  vertical-align: sub;
}

.new-control-combox-drop-button, .new-control-drop-button {
  position: absolute;
  /* display: inline-block; */
  /* background-color: blue; */
  
  width: 10px;
  pointer-events: visible;
  z-index: 1;

  background: #009FFF;
}

.new-control-drop-button .drop-button-triangle {
  width: 0; 
  height: 0; 
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  
  border-top: 8px solid #000;
  position: relative;
  top: 40%;
  left: 1px;
}

/* .new-control-datebox-widget {
  position: absolute; 
  border: 1px solid;
  background-color: #F5F6F7;
} */
/* .new-control-combox-drop-button .drop-button-container {
  visibility: visible;
  width: 8px;
  pointer-events: visible;
  background-color: #000000;
  position: absolute;
  z-index: 1;
} */
/* .new-control-tooltip {
  width: 100%;
  height: 100%;
  position: absolute;
}

.new-control-tooltip .tooltiptext {
  width: 100px;
  height: 30px;
  text-align: left;
  background-color: white;
  transform: translate(-50%, -50%);
  font-size: 12px;
} */

/* Tooltip 容器 */
.new-control-tooltip {
    position: absolute;
    display: inline-block;
}

/* Tooltip 文本 */
.new-control-tooltip .tooltiptext {
    visibility: visible;
    min-width: 160px;
    height: auto;
    background-color: #F0F2F5;
    color: #A8A8A8;
    text-align: left;
    padding: 6px;
    border-radius: 6px;

    pointer-events: none;

    /* 定位 */
    position: absolute;
    z-index: 1;
}

/* 鼠标移动上去后显示提示框 */
/* .new-control-tooltip:hover .tooltiptext {
    visibility: visible;
} */

@media print {
  /* .noprint {
    display:none;
  } */

  .hz-editor-container {
    padding-top: 0 !important
  }

  /* .print-me, .print-me * {
    display: block;
  }
  body * {
    display: none;
  } */

  .print-style, .print-style * {
    visibility: visible;
  }
  body * {
    visibility: hidden;
  }
  
  svg {
    transform: scale(1.5) translate(85px, 120px);
    /* margin-left: 113px;
    margin-right: 113px; */
    margin: 0 0
  }

  


}
.printFrame-box {
  position: fixed;
  left: -300%;
  top: 85px;
  /* width: 146.6mm;
  height: 246.2mm;
  margin-top: -2.54cm;
  margin-left: -3.17cm; */
  width: 21cm;
  height: 29.7cm;
  opacity: 0;
  background: #fff;
}

.print-dialog {
  display: none;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  background: rgba(0, 0, 0, 0.1);
}
.print-dialog.active {
  display: block;
}
.print-dialog.print-hide {
  left: -500%;
  opacity: 0;
}
.print-dialog-box {
  position: absolute;
  left: 0;
  right: 0;
  top: 5%;
  height: 92%;
  margin: auto;
  background: #fff;
  border: 1px solid #999;
  border-radius: 4px;
}
.print-dialog-box-header {
  height: 50px;
}
.print-dialog-box-header > div:first-child {
  line-height: 50px;
  margin-left: 10px;
  text-align: left;
}
.print-dialog-box-header-clinc {
  display: inline-block;
  line-height: 20px;
  font-size: 12px;
}
.print-dialog-box-body {
  height: calc(100% - 50px);
}

.print {
  width: 100%;
  overflow: auto;
}
.print .ReactVirtualized__Grid {
  max-width: auto !important;
  overflow: hidden !important;
}
.print .ReactVirtualized__Grid__innerScrollContainer {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}
.print .page-wrapper {
  position: static !important;
  margin-top: 0;
  margin-bottom: 20px;
  background: none;
}

.print .page-wrapper svg {
  transform-origin: 0 0;
}

.print .scale-200 .page-wrapper svg {
  float: left;
  transform: scale(2, 2);
}

.print .scale-150 .page-wrapper svg {
  float: left;
  transform: scale(1.5, 1.5);
}
.print .scale-75 .page-wrapper {
  float: none;
  margin-left: auto;
  margin-right: auto;
}
.print .scale-75 .page-wrapper svg {
  transform: scale(0.75, 0.75);
}

.print .scale-50 .page-wrapper, .print .scale-25 .page-wrapper {
  float: left;
}

.print .scale-50 .page-wrapper:nth-child(2n) {
  margin-left: 10px;
}

.print .scale-50 .page-wrapper svg {
  transform: scale(0.50, 0.50);
}

.print .scale-25 .page-wrapper {
  margin-left: 10px;
}

.print .scale-25 .page-wrapper:nth-child(4n + 1) {
  margin-left: 0;
}

.print .scale-25 .page-wrapper svg {
  transform: scale(0.25, 0.25);
}

.toolbar-color {
  display: none;
  position: absolute;
  left: 110px;
  top: 32px
}

.toolbar-color.active {
  display: block;
}


ul, li, p {
  margin: 0;
  padding: 0;
  list-style: none;
}

.font-set 

.font-middle > div > span {
  margin-right: 15px;
}

.font-footer {
  padding-top: 4px;
}
.font-footer > div {
  height: 60px;
  line-height: 60px;
  text-align: center;
  border: 1px solid #999;
  overflow: hidden;
}

.paragraph-set {
  padding: 0 10px;
}

.paragraph-set select {
  width: 80px;
  background-color: #ffffff;
  background-image: none !important;
  filter: none !important;
  border: 1px solid #ccc;
  outline: none;
  height: 20px !important;
  line-height: 22px;
  font-size: 12px;
}
.paragraph-set input[type="checkbox"], .paragraph-set input[type="radio"] {
  margin: 0 3px 0 0;
  vertical-align: sub;
}

.paragraph-label {
  display: inline-block;
  width: 90px;
  height: 24px;
  line-height: 24px;
}
.paragraph-input {
  display: inline-block;
  position: relative;
  width: calc(100% - 90px);
  height: 24px;
  line-height: 24px;
}

.paragraph-input > input {
  width: 100%;
  height: 20px;
  line-height: 20px;
  padding-right: 30px;
  border: 1px solid #ccc;
}

.paragraph-input > label {
  position: absolute;
  z-index: 2;
  right: 0;
  width: 30px;
  height: 24px;
  line-height: 24px;
  text-align: center;
}

.hz-editor-container .common-label {
  display: inline-block;
  width: 60px;
}

.hz-editor-container .common-title {
  font-size: 13px;
  font-weight: bold;
}

.hz-editor-container .full-line {
  height: 30px;
  line-height: 30px;
}
.paragraph-set .radio-box label {
  margin-right: 20px;
}

.hz-editor-container .inline-block {
  display: inline-block;
  width: 48%;
}
.hz-editor-container .inline-block > span {
  display: inline-block;
  width: 65px;
}

.hz-editor-container .inline-block > label {
  display: inline-block;
  width: 50px;
  margin-right: 5px;
}

.hz-editor-container .inline-block > label + input {
  width: calc(100% - 60px);
}

.hz-editor-container .inline-block + .inline-block {
  width: 48%;
  margin-left: 4%;
}

.hz-editor-container .inline-block > span + input {
  width: calc(100% - 67px);
}

.editor-tabs ul > li {
  float: left;
  width: 80px;
  height: 22px;
  font-size: 14px;
  line-height: 22px;
  text-align: center;
  border: 1px solid #ddd;
}

.editor-tabs ul > li.active, .editor-tabs ul > li:hover {
  color: blue;
  cursor: pointer;
}

.editor-tabs-content {
  clear: both;
  padding-top: 10px;
}

.editor-tabs-content ul > li {
  display: none;
}

.editor-tabs-content ul > li.active {
  display: block;
}

.editor-tabs-content ul > li:nth-of-type(1) {
  margin-left: 20px;
}
.editor-tabs-content ul > li:nth-of-type(2) {
  margin-left: 30px;
}
.editor-tabs-content ul > li:nth-of-type(3) {
  margin-left: 40px;
}

.table-set .table-label {
  display: inline-block;
  width: 65px;
}

.table-set .table-inline-block {
  display: inline-block;
  width: 96px;
}

.table-set .table-btn {
  width: 35px;
  margin-left: 5px;
  padding: 0px 3px;
  font-size: 12px;
  cursor: pointer;
  border: 1px solid;
}

.char-title-container > ul {
  overflow: hidden;
}

.char-title-container > ul > li {
  float: left;
  width: 80px;
  height: 30px;
  margin: 0 auto;
  line-height: 30px;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  background-color: #eee;
}

.char-title-container > ul > li.active {
  color: blue;
  cursor: default;
  font-weight: bold;
  background-color: #fff;
}

.char-content-container > span {
  display: inline-block;
  width: 62px;
  margin: 4px 2px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
}

.editor-message {
  display: none;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.02);
}

.editor-message.active {
  display: block;
}

.editor-message-box {
  position: relative;
  left: 0;
  right: 0;
  top: 50%;
  z-index: 1;
  width: 200px;
  margin: 0 auto;
  padding: 5px 10px;
  pointer-events: none;
  font-size: 13px;
  font-family: 微软雅黑;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.36);
  background-color: #fff;
  border-radius: 3px;
  border: 1px solid #eee;
  transform: translateY(-50%);
}

.editor-message-title {
  height: 22px;
  padding-bottom: 10px;
  text-align: left;
  font-size: 14px;
  line-height: 22px;
}

.editor-message-body {
  padding: 0 5px;
  text-align: center;
  line-height: 24px;
}

.editor-message-btns {
  margin-top: 20px;
  padding: 5px 0;
  text-align: center;
  line-height: 22px;
  cursor: pointer;
}

.editor-message-btns > span {
  margin-right: 20px;
  padding: 2px 3px;
  border: 1px solid #999;
  border-radius: 3px;
}

.editor-message-btns > span:last-child {
  margin-right: 0;
}

.custom-prop-content > ul > li {
  display: flex;
  height: 28px;
  line-height: 28px;
  border-bottom: 1px solid #ccc;
}
.custom-prop-content > ul > li:nth-child(1) {
  border-top: 1px solid #ccc;
}
.custom-prop-content > ul > li > div {
  padding: 0 10px;
  border-left: 1px solid #ccc;
}
.custom-prop-content > ul > li > div:last-child {
  border-right: 1px solid #ccc;
}
.custom-prop-content > ul > li > div:nth-child(1) {
  width: 50px;
  padding: 0;
}

.custom-prop-content > ul > li > div:nth-child(2) {
  width: 130px;
}
.custom-prop-content > ul > li > div:nth-child(3) {
  width: 80px;
}
.custom-prop-content > ul > li > div:nth-child(4) {
  flex-grow: 2;
}

.custom-prop-content input {
  width: 100%;

  &.warning {
    border-color: red;
  }
}

.custom-prop-btns > span {
  display: inline-block;
  padding: 0 3px;
  margin-right: 5px;
  cursor: pointer;
  font-size: 18px;
}

.custom-prop-btns > label {
  display: inline-block;
  padding: 0 3px;
  cursor: pointer;
  font-size: 18px;
}

.custom-prop-name > span {
  display: none;
}

.custom-prop-content > .custom-prop-add, .editor-dialog .custom-prop-add {
  margin: 0 auto;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  border: 1px solid #ccc;
  background: #fcfcfc;
}

.detail-item-custom-prop {
  padding: 0px 6px;
  margin: 0;
  font-size: 12px;
  border-width: 1px;
  cursor: pointer;
}


.table-tr > td label {
  line-height: 19px;
}