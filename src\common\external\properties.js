/**
 * @api {get} /properties/组1 区域属性
 * @apiVersion 1.0.0
 * @apiName Get组1Properties
 * @apiGroup Properties
 * @apiDescription 区域属性的详细说明
 *
 * @apiParam {String} properties.name 属性名
 * @apiParam {String} properties.internalName 内部属性名
 * @apiParam {String} properties.description 说明
 *
 * @apiSuccessExample {json} Response Example:
 * {
 *     "properties": [
 *         {
 *             "name": "名称",
 *             "internalName": "Name",
 *             "description": "元素内部id",
 *         },
 *         {
 *             "name": "显示名称",
 *             "internalName": "SerialNumber",
 *             "description": "业务使用的id",
 *         },
 *         {
 *             "name": "标识符",
 *             "internalName": "Identifier",
 *             "description": "业务使用的id",
 *         },
 *         {
 *             "name": "提示信息",
 *             "internalName": "Helptip",
 *             "description": "提示输入文本",
 *         },
 *         {
 *             "name": "标题",
 *             "internalName": "Title",
 *             "description": "显示在文本内容最前面",
 *         },
 *         {
 *             "name": "隐藏",
 *             "internalName": "Hidden",
 *             "description": "是否隐藏结构化元素",
 *         },
 *         {
 *             "name": "无法删除",
 *             "internalName": "DeleteProtect",
 *             "description": "无法删除元素",
 *         },
 *         {
 *             "name": "不可编辑",
 *             "internalName": "EditProtect",
 *             "description": "无法编辑元素内容",
 *         },
 *         {
 *             "name": "反向编辑",
 *             "internalName": "ReverseEdit",
 *             "description": "父元素无法编辑时，本元素可以编辑",
 *         },
 *         {
 *             "name": "最大长度",
 *             "internalName": "MaxLength",
 *             "description": "最大内容长度",
 *         },
 *         {
 *             "name": "自定义属性",
 *             "internalName": "CustomProperty",
 *             "description": "业务使用的其他属性"
 *         }
 *     ]
 * }
 */

function get组1Properties() {}

/**
 * @api {get} /properties/组2 元素通用属性
 * @apiVersion 1.0.0
 * @apiName Get组2Properties
 * @apiGroup Properties
 * @apiDescription 元素通用属性的详细说明
 *
 * @apiParam {String} properties.name 属性名
 * @apiParam {String} properties.internalName 内部属性名
 * @apiParam {String} properties.description 说明
 *
 * @apiSuccessExample {json} Response Example:
 * {
 *     "properties": [
 *         {
 *             "name": "名称",
 *             "internalName": "Name",
 *             "description": "元素内部id",
 *         },
 *         {
 *             "name": "显示名称",
 *             "internalName": "SerialNumber",
 *             "description": "业务使用的id",
 *         },
 *         {
 *             "name": "标识符",
 *             "internalName": "Identifier",
 *             "description": "业务使用的id",
 *         },
 *         {
 *             "name": "提示信息",
 *             "internalName": "Helptip",
 *             "description": "提示输入文本",
 *         },
 *         {
 *             "name": "隐藏",
 *             "internalName": "Hidden",
 *             "description": "是否隐藏结构化元素",
 *         },
 *         {
 *             "name": "无法删除",
 *             "internalName": "DeleteProtect",
 *             "description": "无法删除元素",
 *         },
 *         {
 *             "name": "不可编辑",
 *             "internalName": "EditProtect",
 *             "description": "无法编辑元素内容",
 *         },
 *         {
 *             "name": "反向编辑",
 *             "internalName": "ReverseEdit",
 *             "description": "父元素无法编辑时，本元素可以编辑",
 *         },
 *         {
 *             "name": "边框显示",
 *             "internalName": "ShowBorder",
 *             "description": "显示左右边框"[]"",
 *         },
 *         {
 *             "name": "背景色隐藏",
 *             "internalName": "HiddenBackground",
 *             "description": "隐藏背景色",
 *         },
 *         {
 *             "name": "按键跳转",
 *             "internalName": "IsKeyJump",
 *             "description": "按Tab可跳转到下一个元素",
 *         },
 *         {
 *             "name": "自定义属性",
 *             "internalName": "CustomProperty",
 *             "description": "业务使用的其他属性"
 *         },
 *         {
 *             "name": "外部数据源",
 *             "internalName": "ExternalDataBind",
 *             "description": "外部数据源属性"
 *         }
 *     ]
 * }
 */

function get组2Properties() {}

/**
 * @api {get} /properties/组3 文本框特有属性
 * @apiVersion 1.0.0
 * @apiName Get组3Properties
 * @apiGroup Properties
 * @apiDescription 文本框特有属性的详细说明
 *
 * @apiParam {String} properties.name 属性名
 * @apiParam {String} properties.internalName 内部属性名
 * @apiParam {String} properties.description 说明
 *
 * @apiSuccessExample {json} Response Example:
 * {
 *     "properties": [
 *         {
 *             "name": "占位符",
 *             "internalName": "Placeholder",
 *             "description": "为空时，显示的文本",
 *         },
 *         {
 *             "name": "标题",
 *             "internalName": "Title",
 *             "description": "显示在文本内容最前面",
 *         },
 *         {
 *             "name": "必填项",
 *             "internalName": "IsMustInput",
 *             "description": "为空时，边框和占位符显示为默认的红色",
 *         },
 *         {
 *             "name": "带框字符",
 *             "internalName": "TextBorder",
 *             "description": "整个元素外显示矩形边框",
 *         },
 *         {
 *             "name": "仅标题时隐藏",
 *             "internalName": "HideHasTitle",
 *             "description": "当文本框只有标题时，不打印",
 *         },
 *         {
 *             "name": "保密显示",
 *             "internalName": "DisplayType",
 *             "description": "不保密-字正常显示；全部保密-字全用*替换；部分保密-只保留第一个和最后一个字，其他用*替换",
 *         },
 *         {
 *             "name": "最大长度",
 *             "internalName": "MaxLength",
 *             "description": "最大内容长度",
 *         },
 *         {
 *             "name": "固定长度",
 *             "internalName": "FixedLength",
 *             "description": "固定长度，超出后不再约束",
 *         },
 *         {
 *             "name": "内部对齐",
 *             "internalName": "Alignments",
 *             "description": "需先设置固定长度。本元素内的对齐方式"
 *         }
 *     ]
 * }
 */

function get组3Properties() {}

/**
 * @api {get} /properties/组4 节特有属性
 * @apiVersion 1.0.0
 * @apiName Get组4Properties
 * @apiGroup Properties
 * @apiDescription 节特有属性详细说明
 *
 * @apiParam {String} properties.name 属性名
 * @apiParam {String} properties.internalName 内部属性名
 * @apiParam {String} properties.description 说明
 *
 * @apiSuccessExample {json} Response Example:
 * {
 *     "properties": [
 *         {
 *             "name": "占位符",
 *             "internalName": "Placeholder",
 *             "description": "为空时，显示的文本",
 *         },
 *         {
 *             "name": "标题",
 *             "internalName": "Title",
 *             "description": "显示在文本内容最前面",
 *         },
 *         {
 *             "name": "必填项",
 *             "internalName": "IsMustInput",
 *             "description": "为空时，边框和占位符显示为默认的红色"
 *         }
 *     ]
 * }
 */

function get组4Properties() {}

/**
 * @api {get} /properties/组5 数值框特有属性
 * @apiVersion 1.0.0
 * @apiName Get组5Properties
 * @apiGroup Properties
 * @apiDescription 数值框特有属性的详细说明
 *
 * @apiParam {String} properties.name 属性名
 * @apiParam {String} properties.internalName 内部属性名
 * @apiParam {String} properties.description 说明
 *
 * @apiSuccessExample {json} Response Example:
 * {
 *     "properties": [
 *         {
 *             "name": "占位符",
 *             "internalName": "Placeholder",
 *             "description": "为空时，显示的文本",
 *         },
 *         {
 *             "name": "必填项",
 *             "internalName": "IsMustInput",
 *             "description": "为空时，边框和占位符显示为默认的红色",
 *         },
 *         {
 *             "name": "带框字符",
 *             "internalName": "TextBorder",
 *             "description": "整个元素外显示矩形边框",
 *         },
 *         {
 *             "name": "强制校验",
 *             "internalName": "ForceValidate",
 *             "description": "只允许录入数字，录入非数字后，光标无法离开本数值框",
 *         },
 *         {
 *             "name": "保密显示",
 *             "internalName": "DisplayType",
 *             "description": "不保密-字正常显示；全部保密-字全用*替换",
 *         },
 *         {
 *             "name": "最大值",
 *             "internalName": "MaxValue",
 *             "description": "允许输入的最大值",
 *         },
 *         {
 *             "name": "最小值",
 *             "internalName": "MinValue",
 *             "description": "允许输入的最小值",
 *         },
 *         {
 *             "name": "精度",
 *             "internalName": "Precision",
 *             "description": "控制小数点后的位数",
 *         },
 *         {
 *             "name": "单位",
 *             "internalName": "Unit",
 *             "description": "录入数字后，光标离开，自动补全单位",
 *         },
 *         {
 *             "name": "固定长度",
 *             "internalName": "FixedLength",
 *             "description": "固定长度，超出后不再约束",
 *         },
 *         {
 *             "name": "内部对齐",
 *             "internalName": "Alignments",
 *             "description": "需先设置固定长度。本元素内的对齐方式"
 *         }
 *     ]
 * }
 */

function get组5Properties() {}

/**
 * @api {get} /properties/组6 复选框特有属性
 * @apiVersion 1.0.0
 * @apiName Get组6Properties
 * @apiGroup Properties
 * @apiDescription 复选框特有属性的详细说明
 *
 * @apiParam {String} properties.name 属性名
 * @apiParam {String} properties.internalName 内部属性名
 * @apiParam {String} properties.description 说明
 *
 * @apiSuccessExample {json} Response Example:
 * {
 *     "properties": [
 *         {
 *             "name": "内容",
 *             "internalName": "通过特定接口设置",
 *             "description": "选择框的内容，通过setCheckboxCaption设置",
 *         },
 *         {
 *             "name": "属性值",
 *             "internalName": "LabelCode",
 *             "description": "业务用到的属性值",
 *         },
 *         {
 *             "name": "组",
 *             "internalName": "Group",
 *             "description": "设为同一组的复选框，勾选一个，其他会变为未勾选",
 *         },
 *         {
 *             "name": "勾选框居右",
 *             "internalName": "ShowRight",
 *             "description": "选择框显示在内容的右侧",
 *         },
 *         {
 *             "name": "勾选",
 *             "internalName": "IsChecked",
 *             "description": "勾选选择框",
 *         },
 *         {
 *             "name": "勾选才打印",
 *             "internalName": "PrintSelected",
 *             "description": "勾选时才打印复选框"
 *         }
 *     ]
 * }
 */

function get组6Properties() {}

/**
 * @api {get} /properties/组7 下拉框特有属性
 * @apiVersion 1.0.0
 * @apiName Get组7Properties
 * @apiGroup Properties
 * @apiDescription 下拉框特有属性的详细说明
 *
 * @apiParam {String} properties.name 属性名
 * @apiParam {String} properties.internalName 内部属性名
 * @apiParam {String} properties.description 说明
 *
 * @apiSuccessExample {json} Response Example:
 * {
 *     "properties": [
 *         {
 *             "name": "占位符",
 *             "internalName": "Placeholder",
 *             "description": "为空时，显示的文本",
 *         },
 *         {
 *             "name": "标题",
 *             "internalName": "Title",
 *             "description": "显示在文本内容最前面",
 *         },
 *         {
 *             "name": "必填项",
 *             "internalName": "IsMustInput",
 *             "description": "为空时，边框和占位符显示为默认的红色",
 *         },
 *         {
 *             "name": "索引检查",
 *             "internalName": "Retrieve",
 *             "description": "在下拉选项上方出现检索输入框",
 *         },
 *         {
 *             "name": "展现属性值",
 *             "internalName": "ShowCodeAndValue",
 *             "description": "默认下拉选项只显示内容，勾选后同时显示内容和属性值",
 *         },
 *         {
 *             "name": "带框字符",
 *             "internalName": "TextBorder",
 *             "description": "整个元素外显示矩形边框",
 *         },
 *         {
 *             "name": "自定义下拉标题项",
 *             "internalName": "不支持",
 *             "description": "下拉选择框，选项上方默认没有标题，可自定义设置",
 *         },
 *         {
 *             "name": "显示名",
 *             "internalName": "不支持",
 *             "description": "接口里下拉选项的code，如男性",
 *         },
 *         {
 *             "name": "属性值",
 *             "internalName": "不支持",
 *             "description": "接口里下拉选项的value，如男性对应的1",
 *         },
 *         {
 *             "name": "选中项前缀",
 *             "internalName": "SelectPrefixContent",
 *             "description": "在选中项内容前面显示",
 *         },
 *         {
 *             "name": "未选中项前缀",
 *             "internalName": "PrefixContent",
 *             "description": "在未选中项内容前面显示",
 *         },
 *         {
 *             "name": "内容分隔符",
 *             "internalName": "Separator",
 *             "description": "选中多个选项时，如A,B,显示为"A，B"",
 *         },
 *         {
 *             "name": "显示value值",
 *             "internalName": "ShowValue",
 *             "description": "如选项为男性（1），选择男性后，下拉框显示1",
 *         },
 *         {
 *             "name": "固定长度",
 *             "internalName": "FixedLength",
 *             "description": "固定长度，超出后不再约束",
 *         },
 *         {
 *             "name": "内部对齐",
 *             "internalName": "Alignments",
 *             "description": "需先设置固定长度。本元素内的对齐方式"
 *         }
 *     ]
 * }
 */

function get组7Properties() {}

/**
 * @api {get} /properties/组8 日期框特有属性
 * @apiVersion 1.0.0
 * @apiName Get组8Properties
 * @apiGroup Properties
 * @apiDescription 日期框特有属性的详细说明
 *
 * @apiParam {String} properties.name 属性名
 * @apiParam {String} properties.internalName 内部属性名
 * @apiParam {String} properties.description 说明
 *
 * @apiSuccessExample {json} Response Example:
 * {
 *     "properties": [
 *         {
 *             "name": "占位符",
 *             "internalName": "Placeholder",
 *             "description": "为空时，显示的文本",
 *         },
 *         {
 *             "name": "起始时间",
 *             "internalName": "StartDate",
 *             "description": "允许输入的最早时间",
 *         },
 *         {
 *             "name": "截止时间",
 *             "internalName": "EndDate",
 *             "description": "允许输入的最晚时间",
 *         },
 *         {
 *             "name": "必填项",
 *             "internalName": "IsMustInput",
 *             "description": "为空时，边框和占位符显示为默认的红色",
 *         },
 *         {
 *             "name": "日期格式",
 *             "internalName": "DateType",
 *             "description": "图中4种格式",
 *         },
 *         {
 *             "name": "自定义日期格式",
 *             "internalName": "CustomDateFormat",
 *             "description": "可自定义需要的日期格式",
 *         },
 *         {
 *             "name": "若日期框格式为"月-日"，要获取完整时间",
 *             "internalName": "-",
 *             "description": "可调用editor.getDateTimeBoxValueEx('datetimebox1', 'Alldate')"
 *         }
 *     ]
 * }
 */

function get组8Properties() {}

/**
 * @api {get} /properties/组9 选择按钮特有属性
 * @apiVersion 1.0.0
 * @apiName Get组9Properties
 * @apiGroup Properties
 * @apiDescription 选择按钮特有属性的详细说明
 *
 * @apiParam {String} properties.name 属性名
 * @apiParam {String} properties.internalName 内部属性名
 * @apiParam {String} properties.description 说明
 *
 * @apiSuccessExample {json} Response Example:
 * {
 *     "properties": [
 *         {
 *             "name": "必填项",
 *             "internalName": "IsMustInput",
 *             "description": "为空时，边框和选择项显示为默认的红色",
 *         },
 *         {
 *             "name": "选择框居右",
 *             "internalName": "不支持",
 *             "description": "选择框显示在内容的右侧",
 *         },
 *         {
 *             "name": "显示名",
 *             "internalName": "不支持",
 *             "description": "接口里选项的code，如男性",
 *         },
 *         {
 *             "name": "属性值",
 *             "internalName": "不支持",
 *             "description": "接口里选项的value，如男性对应的01",
 *         },
 *         {
 *             "name": "值间距",
 *             "internalName": "不支持",
 *             "description": "选项之间的间距",
 *         },
 *         {
 *             "name": "未被勾选时不被打印",
 *             "internalName": "PrintSelected",
 *             "description": "只打印勾选的选项"
 *         }
 *     ]
 * }
 */

function get组9Properties() {}

/**
 * @api {get} /properties/组10 签名控件特有属性
 * @apiVersion 1.0.0
 * @apiName Get组10Properties
 * @apiGroup Properties
 * @apiDescription 签名控件特有属性的详细说明
 *
 * @apiParam {String} properties.name 属性名
 * @apiParam {String} properties.internalName 内部属性名
 * @apiParam {String} properties.description 说明
 *
 * @apiSuccessExample {json} Response Example:
 * {
 *     "properties": [
 *         {
 *             "name": "无法拷贝",
 *             "internalName": "CopyProtect",
 *             "description": "无法复制粘贴签名元素",
 *         },
 *         {
 *             "name": "子元素边框显示",
 *             "internalName": "ShowSignBorder",
 *             "description": "显示签名子元素的边框",
 *         },
 *         {
 *             "name": "签名个数",
 *             "internalName": "不支持",
 *             "description": "签名子元素的个数",
 *         },
 *         {
 *             "name": "常驻显示",
 *             "internalName": "不支持",
 *             "description": "默认显示的子元素个数，超出则不约束",
 *         },
 *         {
 *             "name": "前字符",
 *             "internalName": "不支持",
 *             "description": "显示在第一个子元素前的文本",
 *         },
 *         {
 *             "name": "间隔字符",
 *             "internalName": "不支持",
 *             "description": "显示在子元素间的文本",
 *         },
 *         {
 *             "name": "尾字符",
 *             "internalName": "不支持",
 *             "description": "显示在最后一个子元素后的文本",
 *         },
 *         {
 *             "name": "签名占位符",
 *             "internalName": "不支持",
 *             "description": "签名子元素为空时，子元素中显示的文本"
 *         }
 *     ]
 * }
 */

function get组10Properties() {}

/**
 * @api {get} /properties/组11 地址框特有属性
 * @apiVersion 1.0.0
 * @apiName Get组11Properties
 * @apiGroup Properties
 * @apiDescription 地址框特有属性的详细说明
 *
 * @apiParam {String} properties.name 属性名
 * @apiParam {String} properties.internalName 内部属性名
 * @apiParam {String} properties.description 说明
 *
 * @apiSuccessExample {json} Response Example:
 * {
 *     "properties": [
 *         {
 *             "name": "占位符",
 *             "internalName": "Placeholder",
 *             "description": "为空时，显示的文本",
 *         },
 *         {
 *             "name": "层级",
 *             "internalName": "不支持",
 *             "description": "1层为省，2层为省市，3层为省市区",
 *         },
 *         {
 *             "name": "必填项",
 *             "internalName": "IsMustInput",
 *             "description": "为空时，边框和占位符显示为默认的红色"
 *         }
 *     ]
 * }
 */

function get组11Properties() {}

