import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import { message } from '../../../../common/Message';
import { IParaButtonProp, ResultType, isValidName } from '@/common/commonDefines';
import InputUI from '../../ui/Input';
import CheckboxItem from '../../ui/CheckboxItem';

interface IProps {
    documentCore: any;
    visible: boolean;
    id: string;
    property?: IParaButtonProp;
    close: (name: string | number, bRefresh?: boolean) => void;
}

export default class ParaButton extends React.Component<IProps> {
    private data: IParaButtonProp;
    private visible: boolean;

    constructor(props: IProps) {
        super(props);
        this.data = {} as any;
        this.visible = props.visible;
    }

    UNSAFE_componentWillReceiveProps(nextProps: IProps): void {
        this.visible = nextProps.visible;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={350}
                open={this.open}
                title='按钮'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>名称</div>
                        <div className='right-auto'>
                            <InputUI
                                value={this.data.name}
                                name='name'
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>按钮内容</div>
                        <div className='right-auto'>
                            <InputUI
                                value={this.data.content}
                                name='content'
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    {/* <div className='editor-line'>
                        <div className='w-70'>背景色</div>
                        <div className='right-auto'>
                            <InputUI
                                value={this.data.color}
                                name='color'
                                disabled={true}
                                onChange={this.onChange}
                            />
                        </div>
                    </div> */}
                    <div className='editor-line'>
                        <CheckboxItem name='bPrint' value={this.data.bPrint} onChange={this.onChange}>打印显示</CheckboxItem>
                    </div>
                </div>
            </Dialog>
        );
    }

    public selectText = (e: any) => {
        e.target.select();
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={() => this.close()}>取消</Button>
                <Button type='primary' onClick={this.confirm}>
                    确认
                </Button>
            </span>
        );
    }

    private open = (): void => {
        const {property, documentCore} = this.props;
        let data = this.data;
        if (!property) {
            data.name = documentCore.getParaButtonName();
            data.bPrint = undefined;
            data.color = '#ddd';
            data.content = '事件按钮';
            this.data = data;
        } else {
            this.data = Object.assign({}, property);
        }
        this.setState({});
    }

    private onChange = (value: any, name: string): void => {
        this.data[name] = value;
        this.setState({});
    }

    private confirm = (id?: any): void => {
        const {documentCore, property} = this.props;
        const data = this.data;

        if (!data.content) {
            message.error('按钮内容不能为空');
            return;
        }

        if (!data.name) {
            message.error('按钮名称不能为空');
            return;
        }

        if (!isValidName(data.name)) {
            message.error('按钮名称命名异常');
            return;
        }

        if (property && data.name !== property.name && !documentCore.checkParaButtonName(data.name)) {
            message.error('按钮名称不能重复');
            return;
        }
        let res;
        if (property) {
            res = documentCore.setParaButtonProps(property.name, data);
            this.close(res === ResultType.Refresh);
            return;
        }
        res = documentCore.addParaButton(data);
        this.close(!!res);
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({});
    }
}
