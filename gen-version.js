const fs = require('fs');
const path = require('path');

// 定义目标文件路径
const versionFilePath = path.join(__dirname, 'src/version.js');

// 初始化版本号
let version = 1000;

// 检查文件是否存在并读取现有版本号
if (fs.existsSync(versionFilePath)) {
    const versionData = fs.readFileSync(versionFilePath, 'utf8');
    const match = versionData.match(/export const VERSION = '1\.1\.(\d{4})';/);
    if (match) {
        version = parseInt(match[1], 10) + 1;
    }
}

// 限制版本号在四位数字范围内
if (version > 9999) {
    version = 1000; // 可选：重置版本号或采取其他处理措施
}

// 写入新的版本号
const versionContent = `export const VERSION = '1.1.${version.toString().padStart(4, '0')}';\n`;
fs.writeFileSync(versionFilePath, versionContent);

console.log(`Generated version: 1.1.${version.toString().padStart(4, '0')}`);
