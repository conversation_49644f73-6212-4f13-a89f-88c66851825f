import Print from './Print';
import { IPrintOutpatient } from '../commonDefines';
import { getPxForMM } from '../../model/core/util';
import { doPrintStyle } from '../css/style';
export class PrintOutpatient extends Print {
    private _testIframe: any;
    constructor(host: any) {
        super(host);
    }

    public close(): void {
        ;
    }

    // public print(printProp: IPrintOutpatient): void {

    // }

    /**
     * 获取门诊打印文档
     * @param printProp 打印参数
     */
    public getPrintOutpatientContent(printProp: IPrintOutpatient): Promise<boolean> {
        return new Promise((resolve, reject) => {
            // this.documentCore.removeAllPlaceholderNewControls();
            this.bSelected = false;
            // this.documentCore.setPropsToPrint();
            this.setPageProps(printProp);
            switch (printProp.pageType) {
                case 1:
                    this.pagePosition = 'top';
                    this.setPageSpace(printProp.firstPageDistance);
                    this.setPageSpace(printProp.pageMidDistance, 1);
                    this.setPageSpace(printProp.lastPageDistance, 2);
                    this.setPageTwoHeight(printProp);
                    break;
                case 2:
                    this.pagePosition = 'bottom';
                    this.setPageSpace(printProp.firstPageDistance, 0, 'addPageBreak');
                    this.setPageSpace(printProp.lastPageDistance, 2);
                    this.setPageSpace(printProp.pageMidDistance, 3);
                    this.setPageTwoHeight(printProp);
                    break;
                default:
                    resolve(false);
                    return;
            }
            this.host.bPrint = true;
            this.host.refreshByAsync()
            .then(() => {
                this.host.bPrint = false;
                this.pagePosition = 'top';
                const html = this.getPageStr();
                const iframe = this.getPintIframe();
                const dom = iframe.contentDocument.lastChild;
                dom.innerHTML = html;
                iframe.contentWindow.print();
                resolve(true);
                // if (this.nextPageHtml) {
                //     setTimeout(() => {
                //         dom.innerHTML = this.nextPageHtml;
                //         iframe.contentWindow.print();
                //     }, 10000);
                // }
            });
        });
    }

    private getPageStr(): string {
        const texts = this.getContents();
        // const pager = document.createElement('div');
        let pagers = '';
        if (this.pagePosition) {
            pagers = this.setPageContent1(texts);
        } else {
            pagers = this.setPageContent(texts);
        }
        // pager.innerHTML = pagers;
        // const childWindow = this.getFrameWindow();
        // const body = childWindow.document.body;
        // body.innerHTML = '';
        // body.appendChild(pager);
        return `
            <!DOCTYPE html>
            <html>
                <head>
                    <style type="text/css">${doPrintStyle()}</style>
                    <meta charset="utf-8">
                </head>
                <body class='print-interface'>
                    ${pagers}
                </body>
            </html>
        `;
    }

    private setPageTwoHeight(printProp: IPrintOutpatient): void {
        let height: number;
        if (printProp.pageHeight === undefined) {
            const { pageProperty } = this.documentCore.render();
            height = pageProperty.height;
        } else {
            height = getPxForMM(printProp.pageHeight);
        }
        height = height * 2;

        this.documentCore.setPageProperty({
            height,
        });
    }

    private setPageSpace(space: number, nPos?: number, action?: string): void {
        if (space === undefined) {
            return;
        }
        space = getPxForMM(space);
        this.documentCore.addFixedHeightPara(space, nPos, action);
    }

    private setPageProps(printProp: IPrintOutpatient): void {
        let width: number;
        let height: number;
        let paddingTop: number;
        let paddingBottom: number;
        // let paddingLeft: number;
        // let paddingRight: number;
        const { pageProperty } = this.documentCore.render();
        // let pageSpacing: number;
        if (printProp.pageWidth !== undefined) {
            width = getPxForMM(printProp.pageWidth);
        } else {
            width = pageProperty.width;
        }
        if (printProp.pageHeight !== undefined) {
            height = getPxForMM(printProp.pageHeight);
        } else {
            height = pageProperty.height;
        }

        // height = height * 2;
        // if (printProp.pageMidDistance !== undefined) {
        //     pageSpacing = printProp.pageMidDistance;
        // }
        paddingTop = 0;
        paddingBottom = 0;
        // paddingLeft = 0;
        // paddingRight = 0;
        this.documentCore.setPageProperty({
            width,
            height,
            paddingTop,
            paddingBottom,
        });
    }

    private getPintIframe(): any {
        let iframe = this._testIframe;
        if (!iframe) {
            const doc = this.host.myRef.current.ownerDocument;
            const dom = doc.createElement('iframe');
            dom.setAttribute('style', 'position: fixed; top: 300%; left: 300%; width: 1px; height: 1px; opacity: 0;');
            doc.body.appendChild(dom);
            this._testIframe = iframe = dom;
        }

        return iframe;
    }
}
