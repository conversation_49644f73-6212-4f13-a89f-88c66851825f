import ParaPage from './ParaPage';
import Paragraph from '../Paragraph';
import ParaLine from './ParaLine';
import PortionRecalculateObject from './PortionRecalculateObject';

/**
 * 段落改变时保存的对象，用于redo/undo
 */
export default class ParagraphRecalculateObject {
    public x: number = 0;
    public y: number = 0;
    public xLimit: number = 0;
    public yLimit: number = 0;

    public pages: ParaPage[] = [];
    public lines: ParaLine[] = [];
    public content: PortionRecalculateObject[] = [];

    constructor() {
        //
    }

    public save( para: Paragraph ): void {
        this.x = para.x;
        this.y = para.y;
        this.xLimit = para.xLimit;
        this.yLimit = para.yLimit;

        this.pages = para.pages;
        this.lines = para.lines;

        const content = para.content;

        for (let index = 0, count = content.length; index < count; index++) {
            this.content[index] = content[index].saveRecalculateObject();
        }
    }

    public load( para: Paragraph ): void {
        para.x = this.x;
        para.y = this.y;
        para.xLimit = this.xLimit;
        para.yLimit = this.yLimit;

        para.pages = this.pages;
        para.lines = this.lines;

        const content = para.content;

        for (let index = 0, count = content.length; index < count; index++) {
            para.content[index].loadRecalculateObject(this.content[index]);
        }
    }
}
