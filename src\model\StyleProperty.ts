import { TextDecorationLineType } from './core/TextProperty';
import { FontStyleType } from './core/Style';

/**
 * 页面属性
 */
export class PageProperty {
    public pageFormat?: string;
    public width?: number; // 210mm
    public height?: number; // 297mm
    public paddingTop?: number; // 25.4mm
    public paddingBottom?: number; // 25.4mm
    public paddingRight?: number; // 31.7mm
    public paddingLeft?: number; // 31.7mm

    constructor() {
        //
    }
}

/**
 * 文本属性
 */
export class TextProperty {
    public textDecorationLine: TextDecorationLineType;
    public lineHeight?: number;
    public fontSize: number;
    public fontFamily: string;
    public fontWeight: number;
    public fontStyle: FontStyleType;
    public color: string;
    public backgroundColor: string;
    public vertAlign: number;
}

export const TEXT_SCARE_NUM = 1.20;
