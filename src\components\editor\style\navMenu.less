@import './global.less';
@localLineHeight: 28px; //行高
.hz-editor-container .nav-menu {
    line-height: @localLineHeight;
    color: @color;
    font-size: @fontSize;
    font-family: @fontFamily;

    .title {
        font-weight: bold;
        padding-left: 12px;
        &.icon {
            padding-left: 0px;
        }
        &.icon::before {
            display: inline-block;
            width: 0px;
            height: 0px;
            cursor: pointer;
            border: 6px solid transparent;
            border-left-color:#999;
            content: ' ';
        }

        &.active::before {
            margin-right: 5px;
            border-top-color:#999;
            border-left-color: transparent;
            transform: translateY(4px);
        }

        .editor-checkbox {
            float: right;
        }
    }

    .item {
        display: none;
        padding-left: 20px;
        & > span {
            display: block;
        }
        & > span + ul {
            padding-left: 36px;
            display: block;
        }
    }

    .content {
        display: none;
        padding-left: 15px;
        transition: all 0.5s;

        ul.content {
            padding-left: 28px;
        }

        li {
            cursor: pointer;
            &:hover, &.active {
                color: @activeColor;
            }

            white-space: pre-wrap;
        }

        li.value::before, li.noValue::before {
            content: '';
            display: block;
            margin-left: -16px;
            float: left;
            margin-top: (@localLineHeight - 8) / 2;
            border-radius: 5px;
            border: 4px solid;
        }
        li.value::before {
            border-color: lime;
        }

        li.noValue::before {
            border-color: red;
        }
    }

    .title.active + .content, .title.active + .item{
        display: block;
    }

    .operate {
        display: none;
        position: absolute;
        left: 50px;
        top: 50px;
        z-index: 5;
        width: 50px;
        height: 70px;
        padding: 10px;
        box-shadow: 0 0 3px #bbb;
        background: #fff;

        &.active {
            display: block;
        }

        > div {
            height: 28px;
            line-height: 28px;
            cursor: pointer;
            &:hover {
                color: @activeColor;
            }
        }

        > div:first-child {
            border-bottom:  1px solid #ccc;
        }
    }

    .search {
        position: relative;
        width: 100%;
        height: 28px;
        margin-bottom: 8px;
        line-height: 28px;
        vertical-align: middle;
        font-family: 'PingFang SC', 'Microsoft YaHei', 'Microsoft Jhenghei', sans-serif;
        font-size: 14px;
        color: #171819;

        i {
            position: absolute;
            // left: 2px;
            // top: 2px;
            display: inline-block;
            width: 23px;
            height: 23px;
            margin-top: 3px;
            margin-left: 3px;
            // text-align: center;
            // line-height: 24px;
            cursor: pointer;
            &.disabled {
                cursor: default;
                color: #ccc;
                background: url(../images/search-disable.png) no-repeat center;
            }

            &.active {
                background: url(../images/search1.png) no-repeat center;
            }
        }

        input {
            width: 100%;
            height: 100%;
            padding-left: 28px;
            padding-right: 28px;
            font-size: 12px;
            outline: none;
            border: 1px solid @borderColor;
            &:active, &:hover, &:focus{
                border-color: @activeColor;
            }

            &::placeholder {
                color: @disabledColor;
            }
        }
    }

}
