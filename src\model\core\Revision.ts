import { RevisionStyle, RevisionChangeType, ReviewType, IRevisionSetting,
    REVISION_REPLACE_COLOR} from '../../common/commonDefines';
import DocumentContentElementBase from './DocumentContentElementBase';
import Document from './Document';
import { ParagraphContentPos } from './Paragraph/ParagraphContent';

export class Revision {
    private userName: string;
    private userId: string;
    private time: Date;
    private description: string;
    private style: RevisionStyle;
    private color: string;
    private level: number;

    private type: RevisionChangeType;
    private value: string;
    private element: DocumentContentElementBase;
    private startPos: ParagraphContentPos;
    private endPos: ParagraphContentPos;

    constructor(name: string, id: string, style?: RevisionStyle, color?: string,
                level?: number, desc?: string, time?: Date) {
        this.userName = name;
        this.userId = id;
        this.style = style;
        this.color = color;
        this.level = level;
        this.time = time ? time : new Date(); // .getTime();
        this.description = desc;

        this.startPos = null;
        this.startPos = null;
        this.value = '';
        this.type = RevisionChangeType.Unknown;
    }

    public setUserName(name: string): boolean {
        this.userName = name;
        return true;
    }

    public getUserName(): string {
        return this.userName;
    }

    public setUserId(id: string): boolean {
        this.userId = id;
        return true;
    }

    public getUserId(): string {
        return this.userId;
    }

    public setDescription(description: string): void {
        this.description = description;
    }

    public getDescription(): string {
        return this.description;
    }

    public setElement(element: DocumentContentElementBase): void {
        this.element = element;
    }

    public getElement(): DocumentContentElementBase {
        return this.element;
    }

    public setStyle(style: RevisionStyle): void {
        this.style = style;
    }

    public getStyle(): RevisionStyle {
        return this.style;
    }

    public setLevel(level: number): void {
        this.level = level;
    }

    public getLevel(): number {
        return this.level;
    }

    public setColor(color: string): void {
        this.color = color;
    }

    public getColor(): string {
        return this.color;
    }

    public setType(type: RevisionChangeType): void {
        this.type = type;
    }

    public getType(): RevisionChangeType {
        return this.type;
    }

    public setStartPos(startPos: ParagraphContentPos): void {
        this.startPos = startPos;
    }

    public getStartPos(): ParagraphContentPos {
        return this.startPos;
    }

    public setEndPos(endPos: ParagraphContentPos): void {
        this.endPos = endPos;
    }

    public getEndPos(): ParagraphContentPos {
        return this.endPos;
    }

    public setTime(time: Date): void {
        this.time = time;
    }

    public getTime(): Date {
        return this.time;
    }

    public setValue(value: any): void {
        this.value = value;
    }

    public getValue(): any {
        return this.value;
    }
}

export class ReviewInfo {
    private doc: Document;
    private userName: string;
    private userId: string;
    private time: Date;
    private description: string;
    private level: number;
    private color: string;
    private style: number;
    private prevType: ReviewType;
    private prevInfo: ReviewInfo;
    private savedCount: number;         // 保存count
    private deleteInfo: ReviewInfo;     // 新增后的删除记录
    private preRecord: IRevisionSetting[];         // 日期框元素专用

    constructor(doc: Document) {
        this.doc = doc;
        this.userId = '';
        this.userName = '';
        this.time = null;
        this.description = '';
        this.level = 0;
        this.color = '';
        this.style = RevisionStyle.SingleLine;
        this.prevInfo = null;
        this.prevType = ReviewType.DEFAULT;
        this.savedCount = 0;
        this.deleteInfo = null;
    }

    public update(): void {
        if ( this.doc && this.doc.isTrackRevisions() && this.doc.getRevisionsManager() ) {
            const revisionManager = this.doc.getRevisionsManager();
            this.userName = revisionManager.getCurrentUserName();
            this.userId = revisionManager.getCurrentUserId();
            this.description = revisionManager.getCurrentDescription();
            this.level = revisionManager.getCurrentLevel();
            this.color = revisionManager.getCurrentColor();
            this.style = revisionManager.getCurrentStyle();
            this.time = new Date();
        }
    }

    public copy(): ReviewInfo {
        const info = new ReviewInfo(this.doc);
        info.userName = this.userName;
        info.userId = this.userId;
        info.time = this.time;
        info.description = this.description;
        info.level = this.level;
        info.color = this.color;
        info.style = this.style;
        info.prevInfo = this.prevInfo ? this.prevInfo.copy() : null;
        info.prevType = this.prevType;
        info.savedCount = this.savedCount;
        info.deleteInfo = this.deleteInfo ? this.deleteInfo.copy() : null;
        info.preRecord = this.preRecord ? this.preRecord.slice() : null;
        if (this['firstDate']) {
            info['firstDate'] = this['firstDate'];
        }

        if (this['bFirstSet']) {
            info['bFirstSet'] = true;
        }

        return info;
    }

    public getUserId(): string {
        return this.userId;
    }

    public getUserName(): string {
        return this.userName;
    }

    public getTime(): Date {
        return this.time;
    }

    public getColor(): string {
        // if ( (!this.userId || '' === this.userId)
        //     && (!this.userName || '' === this.userName) ) {
        //     return REVISION_COLOR.get('红色');
        // }

        // if ( this.doc && this.doc.getRevisionsManager() ) {
        //     const revisionManager = this.doc.getRevisionsManager();

        //     return revisionManager.getCurrentColor();
        // }
        return this.color;
    }

    public getDescription(): string {
        return this.description;
    }

    public getLevel(): number {
        return this.level;
    }

    public getStyle(): RevisionStyle {
        return this.style;
    }

    public setDoc(doc: Document): void {
        if ( doc && (null == this.doc || doc !== this.doc) ) {
            this.doc = doc;
            if ( doc.getRevisionsManager() ) {
                const revisionManager = this.doc.getRevisionsManager();
                this.userName = revisionManager.getCurrentUserName();
                this.userId = revisionManager.getCurrentUserId();
                this.description = revisionManager.getCurrentDescription();
                this.level = revisionManager.getCurrentLevel();
                this.color = revisionManager.getCurrentColor();
                this.style = revisionManager.getCurrentStyle();
            }
        }
    }

    public savePrev(type: ReviewType): void {
        this.prevType = type;
        this.prevInfo = this.copy();
    }

    public getRevisionSetting(): IRevisionSetting {
        return {
            userId: this.userId,
            userName: this.userName,
            description: this.description,
            style: this.style,
            color: this.color,
            level: this.level,
            date: this.time,
            savedCount: this.savedCount,
        };
    }

    public setRevisionSetting(setting: IRevisionSetting): void {
        if ( setting ) {
            this.userName = setting.userName ? setting.userName : '';
            this.userId = setting.userId ? setting.userId : '';
            this.description = setting.description ? setting.description : '';
            this.level = setting.level;
            this.color = REVISION_REPLACE_COLOR[setting.color] || setting.color;
            this.style = setting.style;
            this.time = setting.date;
            this.savedCount = setting.savedCount ? setting.savedCount : 1;
            // this.deleteInfo = setting.deleteInfo ? setting.deleteInfo.copy() : null;
        }
    }

    /**
     * 重置info
     * 同时也需要将reviewType重置
     */
    public reset(): void {
        this.userId = '';
        this.userName = '';
        this.time = null;
        this.description = '';
        this.level = 0;
        this.color = '';
        this.style = RevisionStyle.SingleLine;
        this.prevInfo = null;
        this.prevType = ReviewType.DEFAULT;
        this.savedCount = 0;
        this.deleteInfo = null;
        this.preRecord = null;
        if (this['firstDate']) {
            this['firstDate'] = undefined;
        }

        if (this['bFirstSet']) {
            this['bFirstSet'] = false;
        }
    }

    public getSavedCount(): number {
        return this.savedCount;
    }

    public setSavedCount(savedCount: number): void {
        this.savedCount = savedCount;
    }

    public getDeleteInfo(): ReviewInfo {
        return this.deleteInfo;
    }

    public isSecondInfo(): boolean {
        return !!this.deleteInfo;
    }

    public setDeleteInfo(info: ReviewInfo): void {
        this.deleteInfo = info;
    }

    public isEqual(info: ReviewInfo): boolean {
        return (!!info && this.getUserId() === info.userId &&
                this.getUserName() === info.userName &&
                this.getLevel() === info.level &&
                this.getColor() === info.getColor() &&
                this.getSavedCount() === info.getSavedCount()
                );
    }

    public getSavedRecord(): IRevisionSetting[] {
        return this.preRecord;
    }

    public setSavedRecord(record: IRevisionSetting[]): void {
        if (record?.length) {
            this.preRecord = record;
        }
    }
}
