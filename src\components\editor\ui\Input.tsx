import * as React from 'react';
import '../style/input.less';

interface IState {
    isHasCloseBtn: boolean;
    value: string;
}

interface IProps {
    onChange?: (value: any, name?: string, e?: any) => void;
    onPressEnter?: (option?: any, e?: any) => void;
    onBlur?: (name?: string, input?: any) => void;
    focus?: (name?: string, input?: any) => void;
    renderAppend?: () => any;
    clickcallback?: (name?: string) => void;
    prefix?: any;
    suffix?: any;
    addonBefore?: any;
    addonAfter?: any;
    type?: string;
    value: any;
    size?: string;
    className?: string;
    placeholder?: string;
    disabled?: boolean;
    readonly?: boolean;
    unDeleted?: boolean;
    name?: string;
    step?: number; // 步值
    min?: number; // 最小值
    max?: number; // 最大值
    precision?: number; // 小数位数
    spellCheck?:string;
}

const styles = {
    addonAfterGroupWrapper: 'addonAfterGroupWrapper',
    isString: 'isString',
    addonBeforeGroupWrapper: 'addonBeforeGroupWrapper',
    inputWrapper: 'inputWrapper',
    prefix: 'prefix',
    closeBtn: 'closeBtn',
    suffix: 'suffix',
    inputGroupWrapper: 'inputGroupWrapper',
    input: 'input',
    small: 'small',
    large: 'large',
    default: 'default',
};

const propNames = [
    'size',
    'addonAfter',
    'addonBefore',
    'prefix',
    'suffix',
    'type',
    'onPressEnter',
    'className',
    'onChange',
    'readonly',
    'name',
    'unDeleted',
    'onBlur',
    'focus',
    'renderAppend',
    'clickcallback',
];

export default class InputUI extends React.Component<IProps, IState> {
    private input: any;
    private propNames: string[];
    private containerRef: any;
    private bContinue: boolean;
    private bCloseIcon: boolean;
    constructor(props: any) {
        super(props);
        let value: boolean = true;
        if (props.value === undefined || props.value === null || props.value === '') {
            value = false;
        }
        this.state = {
            isHasCloseBtn: value,
            value: props.value,
        };
        this.bCloseIcon = true;
        this.input = React.createRef();
        this.containerRef = React.createRef();
        this.propNames = propNames;
    }

    public render(): any {
        const { readonly, disabled, name} = this.props;
        let className = 'editor-input';
        if (readonly) {
            className += ' readonly';
        }
        if (disabled) {
            className += ' disabled';
        }
        if (name === 'startDate' || name === 'endDate') {
            className += ' trigger-datebox';
        }
        return (<div className={className} ref={this.containerRef}>{this.renderLabeledInput(this.renderInput())}</div>);
    }

    // public commentWillMounted(): void {
    //     this._value = this.props.value;
    //     this.set
    // }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        if (nextProps.value !== this.state.value) {
            const value = fixControlledValue(nextProps.value);
            this.setState({value: nextProps.value, isHasCloseBtn: !!value});
        }
    }

    public componentDidMount(): void {
        this.addEvent();
    }

    public componentWillUnmount(): void {
        this.removeEvent();
    }

    public onFocus = (): void => {
        this.input.current.focus();
    }

    private emptyValue = (e: any): void => {
        // this.input.current.value = '';
        this.input.current.focus();
        this.setState({
            value: '',
            isHasCloseBtn: false,
        });

        if (typeof this.props.onChange === 'function') {
            let str = '';
            // if (this.props.type === 'number') {
            //     str = null;
            // }
            this.props.onChange(str, this.props.name, e);
        }
    }

    private domClick = (e: any): void => {
        const target = e.target;
        const className = target.className || '';
        if (className.indexOf(styles.closeBtn) > -1) {
            this.emptyValue(e);
        } else if (className.indexOf('number-icon-') > -1) {
            const activeIndex = className.split('-')[2];
            this.stepClick(activeIndex, e);
        }
        if (typeof this.props.clickcallback === 'function') {
            this.props.clickcallback(this.props.name);
        }
    }

    private stepClick = (index: string, e: any): void => {
        const value: any = this.state.value;
        const props = this.props;
        const min = props.min;
        let num = 0;
        let noValue = false;
        if (typeof value === 'number' && !isNaN(value)) {
            num = value;
        } else if (typeof min === 'number') {
            num = min;
            noValue = true;
        }
        let step = this.props.step;
        if (step === undefined) {
            step = 1;
        }
        switch (index) {
            case '1': {
                num += step;
                break;
            }
            case '2': {
                num -= step;
                break;
            }
            default: {
                return;
            }
        }

        const precision = props.precision;
        if (typeof precision === 'number' && precision > -1) {
            num = parseFloat(num.toFixed(precision));
        } else {
            const stepStr = step.toString()
            .split('.')[1];
            let count = 0;
            if (stepStr) {
                count = stepStr.length;
            }
            num = parseFloat(num.toFixed(count));
        }

        const max = props.max;
        if (typeof min === 'number' && props.min > num) {
            if (!(noValue && num + step - min < 0.0001)) {
                return;
            }
            num = min;
        }

        if (typeof max === 'number' && props.max < num) {
            return;
        }

        props.onChange(num, props.name, e);
        this.setState({value: num as any});
    }

    private addEvent(): void {
        const input = this.input.current;
        input.addEventListener('input', this.onChange);
        input.addEventListener('keydown', this.keyDown);
        input.addEventListener('compositionstart', this.onCompositionStart);
        input.addEventListener('compositionend', this.onCompositionEnd);
        this.containerRef.current.addEventListener('click', this.domClick);
    }

    private removeEvent(): void {
        const input = this.input.current;
        if (!input) {
            console.log('undelete event input');
            return;
        }
        input.removeEventListener('input', this.onChange);
        input.removeEventListener('keydown', this.keyDown);
        input.removeEventListener('compositionstart', this.onCompositionStart);
        input.removeEventListener('compositionend', this.onCompositionEnd);
        this.containerRef.current?.removeEventListener('click', this.domClick);
    }

    private keyDown = (e: any): void => {
        if (e.keyCode === 8) {
            const props = this.props;
            if (props.disabled === true || props.readonly === true) {
                e.preventDefault();
            }
        }
        e.stopPropagation();
    }

    /**
     * 中文输入开始
     */
    private onCompositionStart = (event: any) => {
        event.stopPropagation();
        this.bContinue = false;
    }

    private onCompositionEnd = (event: any) => {
        event.stopPropagation();
        this.bContinue = true;
        this.onChange(event);
    }

    private onChange = (e: any): void => {
        let value = e.target.value;
        if (this.bContinue === false) {
            this.setState({value});
            return;
        }
        const onChange = this.props.onChange;
        const oldValue = this.state.value;
        if (value) {
            if (!oldValue) {
                this.setState({
                    isHasCloseBtn: true,
                });
            }
        } else {
            this.setState({
                isHasCloseBtn: false,
            });
        }

        if (typeof onChange === 'function') {
            if (this.props.type === 'number') {
                if (value) {
                    const min = this.props.min;
                    const max = this.props.max;
                    value = value || '';
                    if (value.search(/\d+\.[0]+/) !== -1) {
                        // value = oldValue;
                        this.setState({value});
                        return;
                    }
                    if (!isNaN(value = Number(value))) {
                        if (typeof min === 'number' && value < min) {
                            value = oldValue;
                            this.setState({value});
                            return;
                        }
                        if (typeof max === 'number' && value > max) {
                            value = oldValue;
                            this.setState({value});
                            return;
                        }
                    } else {
                        value = null;
                    }
                } else {
                    value = null;
                }
            }

            onChange(value, this.props.name, e);
        }

        this.setState({value});
    }

    private handleOnPressEnter = (e: any): void => {
        if (e.key === 'e' && this.props.type === 'number') {
            e.preventDefault();
            return;
        }

        if (e.key === 'Enter') {
            const { onPressEnter } = this.props;
            if (typeof onPressEnter === 'function') {
                onPressEnter(
                    {
                        value: e.target.value,
                    },
                    e,
                );
            }
        }
    }

    private onBlur = (e: any): void => {
        const props = this.props;
        const precision = props.precision;
        let value: any = this.state.value;
        let bChange = false;
        if (typeof precision === 'number' && precision > -1) {
            const sub = (value + '').split('.')[1];
            if (sub && sub.length > precision) {
                value = parseFloat(value.toFixed(precision));
                props.onChange(value, props.name, e);
                this.setState({value});
                bChange = true;
            }
        }
        if (props.type === 'number' && bChange === false && typeof value === 'string') {
            if (value) {
                value = parseFloat(value);
            }
            this.setState({value});
            props.onChange(value, props.name, e);
        }
        const onBlur = this.props.onBlur;
        if (typeof onBlur === 'function') {
            onBlur(this.props.name, e.target);
        }
    }

    private _onFocus = (e: any): void => {
        const focus = this.props.focus;
        if (typeof focus === 'function') {
            focus(this.props.name, e);
        }
    }

    private renderNumberIcon(): any {
        const props = this.props;

        if (props.type !== 'number' || typeof props.step !== 'number' || props.disabled || props.readonly) {
            return null;
        }
        this.bCloseIcon = false;
        return (<span className='number-icon'><i className='number-icon-1'/><i className='number-icon-2'/></span>);
    }

    private renderLabeledInput(children: any): any {
        const { addonBefore, addonAfter, renderAppend } = this.props;

        let addChild;
        if (!addonBefore && !addonAfter && !renderAppend) {
            addChild = children;
        } else {
            addChild = React.cloneElement(children);
        }

        // const styles = inputStyle['editor-input'];

        const addonAfterGroupWrapperCls = classNames({
            [styles.addonAfterGroupWrapper]: true,
            [styles.isString]: isString(addonAfter),
        });

        const addonBeforeGroupWrapper = classNames({
            [styles.addonBeforeGroupWrapper]: true,
            [styles.isString]: isString(addonBefore),
        });

        const before = addonBefore ? (
            <span className={addonBeforeGroupWrapper}>{addonBefore}</span>
        ) : null;

        const after = addonAfter ? (
            <span className={addonAfterGroupWrapperCls}>{addonAfter}</span>
        ) : null;

        const className: string = styles.inputWrapper;

        let appendContent = null;
        if (typeof renderAppend === 'function') {
            const content = renderAppend();
            if (content) {
                appendContent = (<span className={addonAfterGroupWrapperCls}>{content}</span>);
            }
        }

        return (
                <span className={className}>
                    {before}
                    {addChild}
                    {after}
                    {appendContent}
                </span>
        );
    }

    private renderLabeledIcon = (children: any): any => {
        const { prefix, suffix, unDeleted, readonly, disabled } = this.props;
        const actPrefix = prefix ? (
            <span className={styles.prefix} onClick={this.emptyValue}>
                {prefix}
            </span>
        ) : null;

        const closeBtn = unDeleted === true ? null : (
            <i className={styles.closeBtn}/>
        );

        const actSuffix = this.state.isHasCloseBtn && this.bCloseIcon ? (
            closeBtn
        ) : (
            <span className={styles.suffix}>{suffix ? suffix : null}</span>
        );
        const className = styles.inputGroupWrapper;
        // if (readonly) {
        //     className += ' readonly';
        // }
        // if (disabled) {
        //     className += ' disabled';
        // }

        return (
            <span className={className}>
                {actPrefix}
                {React.cloneElement(children)}
                {this.renderNumberIcon()}
                {actSuffix}
            </span>
        );
    }

    private renderInput = (): any => {
        const { type, value, size = 'default', readonly, placeholder } = this.props;
        // 这里只对text和password做处理，因为其他type会自带一些功能，像number、date可以基于这个基础input开发
        const actType = type === 'password' ? 'password' : type;

        // 控制input的尺寸，提高了small、large、default, 具体大小
        const inputCls = classNames({
            [styles.input]: true,
            [styles.small]: size === 'small',
            [styles.large]: size === 'large',
            [styles.default]: size === 'default',
        });

        // 定义了getOhterProps方法，用来获取除了第二个参数包含的其他props
        const otherProps = getOtherProps(this.props, this.propNames);

        if ('value' in otherProps) {
            otherProps['value'] = fixControlledValue(this.state.value);
        }

        let placeholderStr: string;
        if (placeholder === undefined) {
            placeholderStr = '请输入';
        } else {
            placeholderStr = placeholder;
        }
        // if (this.props.readonly === true) {
        //     otherProps['readonly'] = true;
        // }
        const inputDom = (
            <input
                className={inputCls}
                type={actType}
                onKeyPress={this.handleOnPressEnter}
                onBlur={this.onBlur}
                onFocus={this._onFocus}
                onChange={this.empty}
                placeholder={placeholderStr}
                {...otherProps}
                readOnly={readonly}
                ref={this.input}
            />
        );

        return this.renderLabeledIcon(inputDom);
    }

    private empty = (e: any): void => {
        // todo;
    }
}

// import React, { PureComponent } from 'react';
// import classNames from 'classnames';
// import { getOtherProps } from 'utils/tool';
// import styles from './Input.less';

// export default class Input extends PureComponent {

// }

function classNames(obj: object): string {
    let className = '';
    if (!obj) {
        return className;
    }
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            if (obj[key]) {
                className += key + ' ';
            }
        }
    }

    if (className) {
        className = className.slice(0, -1);
    }

    return className;
}

function isFunction(el: any): boolean {
    if (getType(el) === '[object Function]') {
        return true;
    }
    return false;
}

function isString(el: any): boolean {
    if (getType(el) === '[object String]') {
        return true;
    }
    return false;
}

function getType(el: any): string {
    return Object.prototype.toString.call(el);
}

function fixControlledValue(value: any): any {
    if (typeof value === 'undefined' || value === null) {
        return '';
    }
    return value + '';
}

/**
 * 过滤props
 * @param {object} 原props对象
 * @param {Array}  需要过滤的props
 * @return 过滤后的结果
 */
function getOtherProps(sourceProp: any, filterProp: any): any {
    if (!sourceProp) {
        return;
    }
    const otherProps = {};

    Object.keys(sourceProp)
    .forEach((item) => {
        if (filterProp.indexOf(item) === -1) {
            otherProps[item] = sourceProp[item];
        }
    });
    return otherProps;
}
