import { EmrEditor } from '../../components/editor/Main';
import { DocumentCore } from '../../model/DocumentCore';
import { TextProperty } from '../../model/StyleProperty';
import { TextVertAlign } from '../../model/core/TextProperty';
import { LineSpacingType, ResultType, EquationType, ToolbarIndex, DEFAULT_TEXT_PROPERTY } from '../commonDefines';
import { IMenuItem, IMenuSelectResult, MenuItemIndex, IPropKey, IParaSpacing } from './toolbarDefines';
import { IParaProperty } from '../../model/ParaProperty';
import { PasteBtn } from './PasteBtn';
import { ParagraphContentPos } from '../../model/core/Paragraph/ParagraphContent';
import Paragraph from '../../model/core/Paragraph';
import ParaPortion from '../../model/core/Paragraph/ParaPortion';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../GlobalEvent';

export class MenuAction {
    private documentCore: DocumentCore;
    private host: EmrEditor;
    private copyPaste: PasteBtn;
    constructor(host: EmrEditor) {
        this.host = host;
        this.documentCore = host.state.documentCore;
    }

    public menuItemClick(item: IMenuItem, oldValue?: IMenuSelectResult, itemValue?: any): boolean {
        let res: boolean;
        switch (item.index) {
            case MenuItemIndex.Bold: {
                res = this.setBold(oldValue);
                break;
            }
            case MenuItemIndex.Italic: {
                res = this.setItalic(oldValue);
                break;
            }
            case MenuItemIndex.Underline: {
                res = this.setUnderline(oldValue);
                break;
            }
            // case MenuItemIndex.JustifyAlign:
            // case MenuItemIndex.RightAlign:
            // case MenuItemIndex.CenterAlign:
            // case MenuItemIndex.LeftAlign: {
            //     res = this.setParagraphAlignment(item.value) === 0;
            //     break;
            // }
            case MenuItemIndex.LeftIndent:
            case MenuItemIndex.RightIndent:
                res = this.setParaProps(item.index);
                break;
            case MenuItemIndex.Sup:
                res  = this.setSuperscript();
                break;
            case MenuItemIndex.Sub:
                res  = this.setSubscript();
                break;
            case MenuItemIndex.Undo:
                res = this.undo();
                break;
            case MenuItemIndex.Redo:
                res = this.redo();
                break;
            case MenuItemIndex.Cut:
                // in case
                this.createCopyPasteSingleton();

                this.copyPaste.cutEvent();
                res = true;
                break;
            case MenuItemIndex.Copy:
                // in case
                this.createCopyPasteSingleton();

                this.copyPaste.copyEvent();
                res = true;
                break;
            case MenuItemIndex.Paste:
                // in case
                this.createCopyPasteSingleton();

                // paste type based on content source
                this.copyPaste.pasteText()
                .then((res) => {
                    this.host.handleRefresh();
                });

                res = false;
                break;
            case MenuItemIndex.SpecialCharacter: {
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, ToolbarIndex.SpecialCharacter);
                break;
            }
            case MenuItemIndex.BreakPage: {
                this.documentCore.addPageBreak();
                break;
            }
            case MenuItemIndex.AICheck:{
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, ToolbarIndex.AICheck);
                break;
            }
            case MenuItemIndex.Print: {
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, ToolbarIndex.Print);
                break;
            }
            default: {
                res = false;
                break;
            }
        }
        return res;
    }

    public getSelectMenuItems(): IMenuSelectResult {
        const documentCore = this.documentCore;
        const attr = (documentCore.getCurrentTextProps() || {}) as TextProperty;
        const paraProps = undefined; // this.getParaProps(documentCore);
        const arrs: IPropKey[] = [];
        arrs.push({name: MenuItemIndex.Bold, value: attr.fontWeight});
        arrs.push({name: MenuItemIndex.Italic, value: attr.fontStyle});
        arrs.push({name: MenuItemIndex.Sub, value: attr.vertAlign === TextVertAlign.Sub});
        arrs.push({name: MenuItemIndex.Sup, value: attr.vertAlign === TextVertAlign.Super});
        arrs.push({name: MenuItemIndex.Underline, value: attr.textDecorationLine});
        arrs.push({name: MenuItemIndex.Color, value: attr.color});
        arrs.push({name: MenuItemIndex.BackgroundColor, value: attr.backgroundColor});

        const font = {
            fontSize: attr.fontSize || DEFAULT_TEXT_PROPERTY.fontSize,
            fontFamily: attr.fontFamily || DEFAULT_TEXT_PROPERTY.fontFamily,
            color: attr.color || '#000',
            backgroundColor: attr.backgroundColor || '#fff',
        };

        return {
            items: arrs,
            font,
            paraProps,
        };
    }

    public setTextFontFamily(fontFamily: string): number {
        return this.documentCore.setTextFontFamily(fontFamily);
    }

    public setTextFontSize(fontSize: number): number {
        return this.documentCore.setTextFontSize(fontSize);
    }

    public setParagraphAlignment(alignment: number): number {
        return this.documentCore.setParagraphAlignment(alignment);
    }

    public setParaProps(iconIndex: MenuItemIndex, paraSpacingObj?: IParaSpacing): boolean {
        const paraProps = this.getParaProps(this.documentCore);

        // why the heck must need this?
        if (paraProps.paraLeftInd !== undefined ) {
            paraProps.paraLeftInd *= 10;
        }

        if ( null != paraProps.paraInd ) {
            paraProps.paraInd = paraProps.paraInd * 10;
        }

        if (iconIndex === MenuItemIndex.LeftIndent) {
            paraProps.paraLeftInd -= 10;
            if (paraProps.paraLeftInd < 0) {
                paraProps.paraLeftInd = 0;
            }
        } else if (iconIndex === MenuItemIndex.RightIndent) {
            paraProps.paraLeftInd += 10;
        } else if (iconIndex === MenuItemIndex.LineHeight) {
            if (paraSpacingObj) {
                paraProps.paraSpacing = paraSpacingObj.paraSpacing;
                paraProps.paraSpacingType = paraSpacingObj.paraSpacingType;
            }
        }

        return this.documentCore.setParagraphProperty1(paraProps) === ResultType.Success;
    }

    public setTextColor(color: string): void {
        this.documentCore.setTextColor(color);
    }

    public setBackgroundColor(color: string): void {
        this.documentCore.setTextBackgrounColor(color);
    }

    public handleChangeImage = (inputTarget: HTMLInputElement): Promise<number> => {
        const file = inputTarget.files[0];
        const fileReader = new FileReader();

        return new Promise((resolve, reject) => {
            fileReader.readAsDataURL(file);

            fileReader.onload = () => {

                const imgSrc = fileReader.result;

                const img = new Image();
                img.src = imgSrc as string;

                const maxWidth = this.documentCore.getMaxWidth(true);
                const maxHeight = this.documentCore.getMaxHeight(true);
                // console.log(maxWidth, maxHeight);

                img.onload = () => {

                    // if image width/height exceeds limits, shrink them to fit
                    if (img.width > maxWidth) {
                        const wRatio = maxWidth / img.width;
                        const hRatio = maxHeight / img.height;
                        if (img.height * wRatio > maxHeight) {
                            img.height = maxHeight;
                            img.width *= hRatio;
                        } else {
                            img.width = maxWidth;
                            img.height *= wRatio;
                        }
                    } else if (img.height > maxHeight) {
                        const wRatio = maxWidth / img.width;
                        const hRatio = maxHeight / img.height;
                        if (img.width * hRatio > maxWidth) {
                            img.width = maxWidth;
                            img.height *= wRatio;
                        } else {
                            img.height = maxHeight;
                            img.width *= hRatio;
                        }
                    }

                    this.addInlineImage(img.width, img.height, img.src);

                    // trick input field to "onChange" every time
                    inputTarget.value = '';

                };

                // console.log(this.props.documentCore.document)
                resolve(ResultType.Success);
            };
        });

    }

    public insertEquation(): void {
        gEvent.setEvent(this.host.docId, gEventName.DialogEvent, ToolbarIndex.MedEquation);
    }

    public getCursorPortion(): ParaPortion {
        let curPortion: ParaPortion = null;

        const curPara = this.documentCore.getCurrentParagraph() as Paragraph;
        const contentPos = curPara.getParaContentPos(false, false);

        const searchPos = {
            pos: new ParagraphContentPos(),
            bFound: false,
            bSelection: false,
        };

        curPara.getLeftPos(searchPos, contentPos);

        let portionIndex = null;
        if (searchPos.bFound) {
            // portion index
            portionIndex = searchPos.pos.get(0);
        } else {
            // get right char
            curPara.getRightPos(searchPos, contentPos);
            if (searchPos.bFound) {
                // portion index
                portionIndex = searchPos.pos.get(0);
            } else {
                // empty para, default color

            }
        }

        // portion content index
        // const portionContentIndex = (searchPos.pos).get(searchPos.pos.depth);

        // console.log(portionIndex, portionContentIndex)
        // console.log(curPara)

        if (portionIndex != null) {
            curPortion = curPara.content[portionIndex];
        } else {
            //
        }

        return curPortion;
    }

    public getCopyPaste(): PasteBtn {
        return this.copyPaste;
    }

    public async isPasteEnabled(): Promise<boolean> {
        if (this.createCopyPasteSingleton()) {
            return this.copyPaste.isActivePaste();
        }
        return false;
    }

    // create only one instance of CopyModel
    public createCopyPasteSingleton(): boolean {
        if (!this.copyPaste) {
            if (this.host) {
                // console.log(this.host)
                const copy = this.host.getCopyPaste();
                if (!copy) {
                    return false;
                }
                this.copyPaste = new PasteBtn(copy);
                return true;
            }

            return false;
        }
        return true;
    }

    private addInlineImage = (width: number, height: number, src: string,
                              name?: string, type?: EquationType, svgElem?: any) => {
        this.documentCore.addInlineImage(width, height, src, name, type, svgElem);
        this.host.handleRefresh();
    }

    private getParaProps(documentCore: DocumentCore): IParaProperty {
        const paraProps: IParaProperty = documentCore.getSelectedParaPro();
        if (paraProps.paraLeftInd !== undefined) {
            paraProps.paraLeftInd = paraProps.paraLeftInd / 10 || 0;
        }

        if (paraProps.paraInd !== undefined) {
            paraProps.paraInd = paraProps.paraInd / 10 || 0;
        }

        // 悬挂缩进
        if (paraProps.paraIndType === 2) {
            paraProps.paraLeftInd += paraProps.paraInd;
            paraProps.paraInd = -paraProps.paraInd;
        }

        let lineSpaceDisabled = true;
        let cell: string = '倍';
        switch (paraProps.paraSpacingType) {
            case LineSpacingType.Single:
            case LineSpacingType.Min:
                paraProps.paraSpacing = 1;
                break;

            case LineSpacingType.SingeHalf:
                paraProps.paraSpacing = 1.5;
                break;

            case LineSpacingType.Double:
                paraProps.paraSpacing = 2;
                break;

            case LineSpacingType.Multi:
                lineSpaceDisabled = false;
                // need to convert to times
                // let ratio = paraProps.paraSpacing;
                // ratio = (ratio - 1) / 2 / 0.15;
                // paraProps.paraSpacing = Math.round(ratio * 100) / 100;
                break;

            case LineSpacingType.Fixed:
                lineSpaceDisabled = false;
                cell = 'cm';
                // para.paraSpacing = para.paraSpacing;
                break;
            default:
                paraProps.paraSpacingType = -1;
                cell = '';
                // para.paraSpacing = 1;
                break;
        }

        return paraProps;
    }

    private setBold(oldValue: IMenuSelectResult): boolean {
        // let bold = 1;
        // if (oldValue && oldValue.items) {
        //     const boldKey = MenuItemIndex.Bold;
        //     const obj = oldValue.items.find((item) => item.name === boldKey);
        //     bold = obj.value === 1 ? 0 : 1;
        // }

        const res = this.documentCore.setTextBold();

        return res === ResultType.Success;
    }

    private setItalic(oldValue: IMenuSelectResult): boolean {
        // let italic = 1;
        // if (oldValue && oldValue.items) {
        //     const italicKey = MenuItemIndex.Italic;
        //     const obj = oldValue.items.find((item) => item.name === italicKey);
        //     italic = obj.value === 1 ? 0 : 1;
        // }

        const res = this.documentCore.setTextItalic();

        return res === ResultType.Success;
    }

    private setUnderline(oldValue: IMenuSelectResult): boolean {
        // let underLine = 1;
        // if (oldValue && oldValue.items) {
        //     const underLineKey = MenuItemIndex.Underline;
        //     const obj = oldValue.items.find((item) => item.name === underLineKey);
        //     underLine = obj.value === 1 ? 0 : 1;
        // }

        const res = this.documentCore.setTextUnderline();

        return res === ResultType.Success;
    }

    private setSuperscript(oldValue?: IMenuSelectResult): boolean {
        const res = this.documentCore.setTextSuperscript();
        return res === ResultType.Success;
    }

    private setSubscript(oldValue?: IMenuSelectResult): boolean {
        const res = this.documentCore.setTextSubscript();
        return res === ResultType.Success;
    }

    private undo(): boolean {
        const res = this.documentCore.undo();
        return true;
    }

    private redo(): boolean {
        const res = this.documentCore.redo();
        return true;
    }

}
