import { NewControl } from './NewControl';
import DocumentContentBase from '../DocumentContentBase';
import { AlignType, ErrorTextColor, ICascadeEvent, INewControlProperty,
    ResultType } from '../../../common/commonDefines';
// tslint:disable-next-line: max-line-length
import { ChangeNewControlContentMaxLength, ChangeNewControlContentFixedLength, ChangeNewControlContentHideHasTitle, ChangeNewControlPrintChecked } from './NewControlChange';

/**
 * 结构化元素: 文本框
 */
export class NewControlText extends NewControl {
    private maxLength: number;
    private fixedLength: number;
    private bMoreThenMaxLength: boolean;
    private bTextBorder: boolean;
    private hideHasTitle: boolean;
    // private eventInfo: ICascadeEvent;
    private alignments: AlignType;
    private regExp: string;
    private bPrintSelected: boolean;
    // private title: string;
    // private minLength: number;

    constructor(parent: DocumentContentBase, name: string, property: INewControlProperty, sText?: string ) {
        super(parent, name, property, sText);
        this.maxLength = property.newControlMaxLength;
        this.fixedLength = property.newControlFixedLength;
        this.bTextBorder = property.bTextBorder;
        this.alignments = property.alignments;
        this.regExp = property.regExp;
        this.bPrintSelected = property.printSelected === undefined ? false : property.printSelected;
        if (property && property.hideHasTitle != null) {
            this.hideHasTitle = property.hideHasTitle;
        } else {
            this.hideHasTitle = false;
        }
        this.setEvent(property.eventInfo);

        // this.title = property.newControlTitle;

        // if ( this.title && 0 !== this.title.length ) {
        //     const startPortion = this.getStartBorderPortion();
        //     startPortion.addText(this.title);
        // }
    }

    public getMaxLength(): number {
        return this.maxLength;
    }

    public isErrorStatus(): boolean {
        return this.bMoreThenMaxLength;
    }

    // public addContent(): void {
    //     if (typeof this.fixedLength !== 'number' || this.fixedLength <= 0) {
    //         return;
    //     }

    //     this.recalculate();
    // }

    public setAlignments(type: AlignType): number {
        if (type === undefined || type === this.alignments) {
            return ResultType.UnEdited;
        }
        this.alignments = type;
        return ResultType.Success;
    }

    public getAlignments(): AlignType {
        return this.alignments;
    }

    public setEvent(event: ICascadeEvent): number {
        if (event === undefined) {
            return ResultType.UnEdited;
        }

        const cascadeManager = this.getCascadeManager();
        if (cascadeManager) {
            return cascadeManager.setEvent(event, this);
        }

        return ResultType.Failure;
    }

    public getEvent(): any {
        const cascadeManager = this.getCascadeManager();
        if (cascadeManager) {
            return cascadeManager.getEvent(this);
        }
        return;
    }

    public setMaxLengthBgColor(color?: string): boolean {
        const startPortion = this.getStartBorderPortion();
        const endPortion = this.getEndBorderPortion();
        const contents = startPortion.paragraph.content;
        let bStart: boolean = false;
        for (let index = 0, length = contents.length; index < length; index++) {
            const portion = contents[index];
            if (portion === startPortion) {
                bStart = true;
                continue;
            }
            if (portion === endPortion) {
                break;
            }
            if (bStart) {
                portion.textProperty.setMaxLengthBackgroundColor(color);
            }
        }
        this.bMoreThenMaxLength = color !== undefined;
        return true;
    }

    /**
     * 超过最大长度，设置错误提示信息
     * @param type 1: 失去颜色，2：设置颜色
     * @return false: 不需要刷新，true: 需要刷新
     */
    public setTextErrorBgColor(type: number): boolean {
        if (type === 1) {
            if (this.bMoreThenMaxLength) {
                this.setMaxLengthBgColor();
                return true;
            }
            return false;
        }
        const bMore = this.isMoreThanMaxLength1() || this.matchRegExp() === ResultType.Failure;
        if (bMore) {
            this.setMaxLengthBgColor(ErrorTextColor.Default);
            return true;
        } else if (this.bMoreThenMaxLength) {
            this.setMaxLengthBgColor();
            return true;
        }
        return false;
    }

    /**
     * 设置校验表达式
     * @param regExp 正则表达式
     * @returns 
     */
    public setRegExp(regExp: string): number {
        if (regExp == null || this.regExp === regExp) {
            return ResultType.UnEdited;
        }
        try {
            new RegExp(regExp)
        } catch (error) {
            console.warn(error);
            return ResultType.ParamError;
        }
        this.regExp = regExp;
        return ResultType.Success;
    }

    // 获取正则表达式
    public getRegExp(): string {
        return this.regExp;
    }

    public matchRegExp(): number {
        if (!this.regExp) {
            return ResultType.Success;
        }
        const startPortion = this.getStartBorderPortion();
        const endPortion = this.getEndBorderPortion();
        const para = startPortion.paragraph;
        const contents = para.content;
        const startIndex = contents.findIndex((item) => item === startPortion);
        if (startIndex < 0) {
            return ResultType.UnEdited;
        }
        let text = '';
        for (let index = startIndex + 1, len = contents.length; index < len; index++) {
            const item = contents[index];
            if (item === endPortion) {
                break;
            }
            const tx = item.getSelectText(true);
            if (tx) {
                text += tx;
            }
        }
        if (!text) {
            return ResultType.Success;
        }
        return new RegExp(this.regExp).test(text) === false ? ResultType.Failure : ResultType.Success;
    }

    public matchRegExp2(): string {
        if (!this.regExp) {
            return;
        }
        const startPortion = this.getStartBorderPortion();
        const endPortion = this.getEndBorderPortion();
        const para = startPortion.paragraph;
        const contents = para.content;
        const startIndex = contents.findIndex((item) => item === startPortion);
        if (startIndex < 0) {
            return;
        }
        let text = '';
        for (let index = startIndex + 1, len = contents.length; index < len; index++) {
            const item = contents[index];
            if (item === endPortion) {
                break;
            }
            const tx = item.getSelectText(true);
            if (tx) {
                text += tx;
            }
        }
        if (!text || new RegExp(this.regExp).test(text)) {
            return;
        }
        let match = /\(\?\!([\s\S]+?)\)/.exec(this.regExp);
        let text1;
        if (match) {
            match = new RegExp(`(${match[1]})`).exec(text);
            if (!match) {
                return;
            }
            text = match[1];
            text1 = '不可输入限制字:';
            // return `"${match[1]}"`;
        } else {
            text = this.regExp;
            text1 = '必须包含:';
        }

        const res = {};
        const regType = {
            '\\w': '字符',
            '\\d': '数字',
            'a-z': '小写字母',
            'A-Z': '大写字母',
            '0-9': '数字',
        };
        this.getMatchData(this.regExp, regType, res);
        const keys = Object.keys(res);
        return text1 + `"${keys.join('或')}"`;
    }

    public getMatchData(text: string, regType: {[key: string]: string}, result: any, dep: number = 0): boolean {
        if (!text) {
            return;
        }
        const reg = /(\[([\s\S]+?)\])|\(([\s\S]+?)\)|(\\[dw])|((-| |\w|[\u4e00-\u9fea\uff00-\uffef\u3000-\u303f])+)/g;
        let match;
        let key: string;
        let bChanged: boolean = false;
        // tslint:disable-next-line: no-conditional-assignment
        while (match = reg.exec(text)) {
            key = match[5] || match[4] || match[3] || match[2];
            let myChanged: boolean = false;
            key = key.replace(/(\\[dw])|(a-z|A-Z|0-9)/g, (all, a, b) => {
                const c = a || b;
                myChanged = true;
                if (regType[c]) {
                    result[regType[c]] = true;
                }
                return '';
            });
            key = key.replace(/((-| |\w|[\u4e00-\u9fea\uff00-\uffef\u3000-\u303f])+)/g, (all, a, b, c) => {
                result[a] = true;
                myChanged = true;
                return '';
            });
            if (myChanged === false) {
                result[key] = true;
            }
            bChanged = true;
        }

        if (dep === 0 && !bChanged) {
            result[text] = true;
        }

        return bChanged;
    }

    public setErrorTextBgColor(type: number = 1): void {
        this.setTextErrorBgColor(type);
    }

    public setMaxLength( maxLength: number ): number {
        if ( (maxLength == null ) // && maxLength === this.maxLength)
            || maxLength === this.maxLength ) {
            return ResultType.UnEdited;
        }
        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlContentMaxLength(this, this.maxLength, maxLength));
        }
        this.setDirty();
        this.maxLength = maxLength;
        return ResultType.Success;
    }

    public getFixedLength(): number {
        return this.fixedLength;
    }

    public setFixedLength( fixedLength: number ): number {
        if ( (fixedLength == null) // && fixedLength === this.fixedLength)
            || fixedLength === this.fixedLength ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlContentFixedLength(this, this.fixedLength, fixedLength));
        }
        this.setDirty();
        this.fixedLength = fixedLength;
        return ResultType.Success;
    }

    public getHideHasTitle(): boolean {
        return this.hideHasTitle;
    }

    public setHideHasTitle(flag: boolean): number {
        if (flag == null || flag === this.hideHasTitle) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlContentHideHasTitle(this, this.hideHasTitle, flag));
        }
        this.setDirty();
        this.hideHasTitle = flag;
        return ResultType.Success;
    }

    public setTextBorder(flag: boolean): number {
        if (flag == null || flag === this.bTextBorder) {
            return ResultType.UnEdited;
        }

        this.bTextBorder = flag;

        return ResultType.Success;
    }

    public isTextBorder(): boolean {
        return this.bTextBorder;
    }

    public isMoreThanMaxLength1(): boolean {
        const length = this.getNewControlTextLength();

        if ( null != this.maxLength && 0 !== this.maxLength && length > this.maxLength ) {
            return true;
        }

        return false;
    }

    public isMoreThanMaxLength(): boolean {
        const length = this.getNewControlTextLength();

        if ( null != this.maxLength && 0 !== this.maxLength && length >= this.maxLength ) {
            return true;
        }

        return false;
    }

    public getNewControlTextLength(): number {
        let len = super.getNewControlTextLength(true);
        const title = this.getTitle();
        if (title) {
            len -= title.length;
        }
        return len;
    }

    /**
     * 设置newcontrol属性
     * @param property
     */
    public setProperty(property: INewControlProperty): number {
        let res = super.setProperty(property);
        res = this.setMaxLength(property.newControlMaxLength) && res;
        res = this.setFixedLength(property.newControlFixedLength) && res;
        res = this.setTextBorder(property.bTextBorder) && res;
        res = this.setHideHasTitle(property.hideHasTitle) && res;
        res = this.setEvent(property.eventInfo) && res;
        res = this.setAlignments(property.alignments) && res;
        res = this.setRegExp(property.regExp) && res;
        res = this.setPrintSelected(property.printSelected) && res;
        // this.setTitle(property.newControlTitle);
        return res;
    }

    public getProperty(): INewControlProperty {
        const newControlProperty = super.getProperty();
        newControlProperty.newControlMaxLength = this.getMaxLength();
        newControlProperty.bTextBorder = this.isTextBorder();
        newControlProperty.alignments = this.alignments;
        newControlProperty.eventInfo = this.getEvent();
        newControlProperty.regExp = this.getRegExp();
        newControlProperty.printSelected = this.bPrintSelected;
        return newControlProperty;
    }

    public isPrintSelected(): boolean {
        return this.bPrintSelected;
    }

    public getPrintSelected(): boolean {
        return this.bPrintSelected;
    }

    public setPrintSelected(bPrintSelected: boolean): number {
        if (this.bPrintSelected === bPrintSelected || bPrintSelected == null) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlPrintChecked(this, this.bPrintSelected, bPrintSelected));
        }

        this.bPrintSelected = bPrintSelected;
        this.setDirty();
        return ResultType.Success;
    }

    public toPrint(): void {
        super.toPrint();
        if (this.isHidden()) {
            return;
        }

        if (this.bPrintSelected) {
            this.setHidden(true, false);
        }
    }

    // public getTitle(): string {
    //     return this.title;
    // }

    // public setTitle(title: string): void {
    //     if ( title === this.title ) {
    //         return ;
    //     }

    //     const history = this.getDocumentParent()
    //                     .getHistory();
    //     history.addChange(new ChangeNewControlTitle(this, this.title, title));
    //     this.title = title;

    //     if ( this.title ) {
    //         const startPortion = this.getStartBorderPortion();
    //         startPortion.removeFromContent(1, startPortion.content.length - 1, true);
    //         startPortion.addText(this.title);

    //         this.recalculate();
    //     }
    // }
}

/**
 * 结构化元素: 节
 */
export class NewControlSection extends NewControl {
    // private title: string;
    private regExp: string;
    private bMoreThenMaxLength: boolean;
    private bPrintSelected: boolean;

    constructor(parent: DocumentContentBase, name: string, property: INewControlProperty, sText?: string ) {
        super(parent, name, property, sText);
        this.regExp = property.regExp;
        // this.title = property.newControlTitle;
        this.bPrintSelected = property.printSelected === undefined ? false : property.printSelected;
    }

    public setProperty(property: INewControlProperty): number {
        let res = super.setProperty(property);
        res = this.setRegExp(property.regExp) && res;
        res = this.setPrintSelected(property.printSelected) && res;

        return res;
    }

    public setPrintSelected(bPrintSelected: boolean): number {
        if (this.bPrintSelected === bPrintSelected || bPrintSelected == null) {
            return ResultType.UnEdited;
        }
    
        const history = this.getHistory();
        if (history) {
            history.addChange(new ChangeNewControlPrintChecked(this, this.bPrintSelected, bPrintSelected));
        }
    
        this.bPrintSelected = bPrintSelected;
        
        this.setDirty(); 
    
        return ResultType.Success;
    }

    public toPrint(): void {
        super.toPrint();
        if (this.isHidden()) {
            return;
        }
    
        if (this.bPrintSelected) {
            this.setHidden(true, false);
        } else {
        }
    }

    /**
     * 设置校验表达式
     * @param regExp 正则表达式
     * @returns 
     */
    public setRegExp(regExp: string): number {
        if (regExp == null || this.regExp === regExp) {
            return ResultType.UnEdited;
        }
        try {
            new RegExp(regExp)
        } catch (error) {
            console.warn(error);
            return ResultType.ParamError;
        }
        this.regExp = regExp;
        return ResultType.Success;
    }

    // 获取正则表达式
    public getRegExp(): string {
        return this.regExp;
    }

    public matchRegExp(): number {
        if (!this.regExp) {
            return ResultType.Success;
        }
        const startPortion = this.getStartBorderPortion();
        const endPortion = this.getEndBorderPortion();
        const para = startPortion.paragraph;
        const contents = para.content;
        const startIndex = contents.findIndex((item) => item === startPortion);
        if (startIndex < 0) {
            return ResultType.UnEdited;
        }
        let text = '';
        for (let index = startIndex + 1, len = contents.length; index < len; index++) {
            const item = contents[index];
            if (item === endPortion) {
                break;
            }
            const tx = item.getSelectText(true);
            if (tx) {
                text += tx;
            }
        }
        if (!text) {
            return ResultType.Success;
        }
        return new RegExp(this.regExp).test(text) === false ? ResultType.Failure : ResultType.Success;
    }

    public matchRegExp2(): string {
        if (!this.regExp) {
            return;
        }
        const startPortion = this.getStartBorderPortion();
        const endPortion = this.getEndBorderPortion();
        const para = startPortion.paragraph;
        const contents = para.content;
        const startIndex = contents.findIndex((item) => item === startPortion);
        if (startIndex < 0) {
            return;
        }
        let text = '';
        for (let index = startIndex + 1, len = contents.length; index < len; index++) {
            const item = contents[index];
            if (item === endPortion) {
                break;
            }
            const tx = item.getSelectText(true);
            if (tx) {
                text += tx;
            }
        }
        if (!text || new RegExp(this.regExp).test(text)) {
            return;
        }
        let match = /\(\?\!([\s\S]+?)\)/.exec(this.regExp);
        let text1;
        if (match) {
            match = new RegExp(`(${match[1]})`).exec(text);
            if (!match) {
                return;
            }
            text = match[1];
            text1 = '不可输入限制字:';
            // return `"${match[1]}"`;
        } else {
            text = this.regExp;
            text1 = '必须包含:';
        }

        const res = {};
        const regType = {
            '\\w': '字符',
            '\\d': '数字',
            'a-z': '小写字母',
            'A-Z': '大写字母',
            '0-9': '数字',
        };
        this.getMatchData(this.regExp, regType, res);
        const keys = Object.keys(res);
        return text1 + `"${keys.join('或')}"`;
    }

    public getMatchData(text: string, regType: {[key: string]: string}, result: any, dep: number = 0): boolean {
        if (!text) {
            return;
        }
        const reg = /(\[([\s\S]+?)\])|\(([\s\S]+?)\)|(\\[dw])|((-| |\w|[\u4e00-\u9fea\uff00-\uffef\u3000-\u303f])+)/g;
        let match;
        let key: string;
        let bChanged: boolean = false;
        // tslint:disable-next-line: no-conditional-assignment
        while (match = reg.exec(text)) {
            key = match[5] || match[4] || match[3] || match[2];
            let myChanged: boolean = false;
            key = key.replace(/(\\[dw])|(a-z|A-Z|0-9)/g, (all, a, b) => {
                const c = a || b;
                myChanged = true;
                if (regType[c]) {
                    result[regType[c]] = true;
                }
                return '';
            });
            key = key.replace(/((-| |\w|[\u4e00-\u9fea\uff00-\uffef\u3000-\u303f])+)/g, (all, a, b, c) => {
                result[a] = true;
                myChanged = true;
                return '';
            });
            if (myChanged === false) {
                result[key] = true;
            }
            bChanged = true;
        }

        if (dep === 0 && !bChanged) {
            result[text] = true;
        }

        return bChanged;
    }

    public setErrorTextBgColor(type: number = 1): void {
        this.setTextErrorBgColor(type);
    }

    /**
     * 超过最大长度，设置错误提示信息
     * @param type 1: 失去颜色，2：设置颜色
     * @return false: 不需要刷新，true: 需要刷新
     */
    public setTextErrorBgColor(type: number): boolean {
        if (type === 1) {
            if (this.bMoreThenMaxLength) {
                this.setMaxLengthBgColor();
                return true;
            }
            return false;
        }
        const bMore = this.matchRegExp() === ResultType.Failure;
        if (bMore) {
            this.setMaxLengthBgColor(ErrorTextColor.Default);
            return true;
        } else if (this.bMoreThenMaxLength) {
            this.setMaxLengthBgColor();
            return true;
        }
        return false;
    }

    public setMaxLengthBgColor(color?: string): boolean {
        const startPortion = this.getStartBorderPortion();
        const endPortion = this.getEndBorderPortion();
        const contents = startPortion.paragraph.content;
        let bStart: boolean = false;
        for (let index = 0, length = contents.length; index < length; index++) {
            const portion = contents[index];
            if (portion === startPortion) {
                bStart = true;
                continue;
            }
            if (portion === endPortion) {
                break;
            }
            if (bStart) {
                portion.textProperty.setMaxLengthBackgroundColor(color);
            }
        }
        this.bMoreThenMaxLength = color !== undefined;
        return true;
    }


    /**
     * 设置newcontrol属性
     * @param property
     */
    // public setProperty(property: INewControlProperty): void {
    //     super.setProperty(property);
    //     this.setTitle(property.newControlTitle);
    // }

    // public getTitle(): string {
    //     return this.title;
    // }

    // public setTitle(title: string): void {
    //     if ( title === this.title ) {
    //         return ;
    //     }

    //     const history = this.getDocumentParent()
    //                     .getHistory();
    //     history.addChange(new ChangeNewControlTitle(this, this.title, title));
    //     this.title = title;

    //     if ( this.title ) {
    //         const startPortion = this.getStartBorderPortion();
    //         startPortion.removeFromContent(1, startPortion.content.length - 1, true);
    //         startPortion.addText(this.title);

    //         this.recalculate();
    //     }
    // }
}
