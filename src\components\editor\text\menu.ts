import { MenuItemIndex } from "@/common/commonDefines";

export let menuList =  [
    {name: '剪切', hide: false, disabled: false,  className: 'cut', index: MenuItemIndex.Cut},
    {name: '复制', hide: false, disabled: false,  className: 'copy', index: MenuItemIndex.Copy},
    {name: '粘贴', hide: false, disabled: false,  className: 'format', index: MenuItemIndex.Format},
    {name: '删除', hide: false, disabled: false,  className: 'delete', index: MenuItemIndex.Delete},
    {name: '字体', hide: false, disabled: false,  className: 'font', index: MenuItemIndex.Font},
    {name: '段落', hide: false, disabled: false,  className: 'paragraph', index: MenuItemIndex.Paragraph},
    {name: '元素属性', hide: true, disabled: false,  className: 'struct', index: MenuItemIndex.Struct},
    {name: 'AI检查', hide: false, disabled: false,  className: 'struct', index: MenuItemIndex.AICheck},
    {name: '图片属性', hide: true, disabled: false,  className: 'image', index: MenuItemIndex.Image},
    {name: '编辑公式', hide: true, disabled: false,  className: 'formula', index: MenuItemIndex.Formula},
    {name: '合并单元格', hide: true, disabled: false,  className: 'mergeCell', index: MenuItemIndex.MergeCell},
    {name: '拆分单元格', hide: true, disabled: false,  className: 'splitCell', index: MenuItemIndex.SplitCell},
    {name: '插入', hide: true, disabled: false,  className: 'insert', index: MenuItemIndex.Insert,
        childs: [
            {name: '在上方插入行', disabled: true, index: MenuItemIndex.InsertTopRow},
            {name: '在下方插入行', disabled: true, index: MenuItemIndex.InsertBottomRow},
            {name: '在左侧插入列', disabled: true, index: MenuItemIndex.InsertLeftCol},
            {name: '在右侧插入列', disabled: true, index: MenuItemIndex.InsertRightCol},
        ],
    },
    {name: '删除行', hide: true, disabled: false,  className: 'deleteRow', index: MenuItemIndex.DeleteRow},
    {name: '删除列', hide: true, disabled: false,  className: 'deleteCol', index: MenuItemIndex.DeleteCol},
    {name: '表格属性', hide: true, disabled: false,  className: 'table', index: MenuItemIndex.Table},
    {name: '单元格属性', hide: true, disabled: false,  className: 'col', index: MenuItemIndex.CellProperty,
        childs: [
            {name: '保护', hide: false, disabled: true, index: MenuItemIndex.Protected},
            {name: '取消保护', hide: true, disabled: false, index: MenuItemIndex.UnProtected},
        ],
    },
    {name: '刷新', hide: false, disabled: true,  className: 'refresh', index: MenuItemIndex.Refresh},
];
