
import { IParagraphOptions, IRunOptions } from 'docx';
import { ISerialBase, PageNumType, SerialObjType } from './ISerialBase';

export interface ISerialParaObj extends ISerialBase, Omit<IParagraphOptions, 'children'> {
    type: SerialObjType.Paragraph;
    wordWrap: boolean;
    overflowPunct: boolean;
}

export interface ISerialPortionObj {
    type: SerialObjType.Portion;
    children: (ISerialTextObj | ISerialImageObj | ISerialPageNumObj)[];
}

export interface ISerialImageObj {
    type: SerialObjType.Image;
    data: string;
    transformation: {
        width: number;
        height: number;
    };
}

export interface ISerialPageNumObj {
    type: SerialObjType.PageNum;
    pageNumType: PageNumType;
    pageNumString: string;
}

export interface ISerialTextObj extends IRunOptions {
    type: SerialObjType.Text;
}
