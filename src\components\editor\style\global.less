@color: #171819;; // 标准颜色，表单、表格内颜色
@color2: #54627b; // 次级颜色，少部分使用，表单前字体部分颜色
@color3: #3d3d3f; // 小标题颜色
@color4: #acb4c1; // 占位符颜色
@color5: #ff3143; // 报错状态
@currenBgColor: #212938; // 黑色背景颜色
@borderColor: #d5d4dc;
@activeColor: #0040ff; // was: #009fff
@activeBgColor: #e6f7ff;
@disabledColor: #bbb;
@addonColor: #fafafa;
@readonlyColor: #ddd;
@closeBtnW: 15px;
@fontSize: 14px;
@fontFamily: 'PingFang SC', 'Microsoft YaHei', 'Microsoft Jhenghei', sans-serif;

// 使用命名空间定义一些可用的变量
#less() {
    .title {
        font-size: @fontSize;
        color: @color3;
        font-weight: 600;
    }

    .text {
        font-family: @fontFamily;
        font-size: @fontSize;
        color: @color;
    }

    .line {
        width: 100%;
        height: 28px;
        margin-bottom: 8px;
        line-height: 28px;
        vertical-align: middle;
    }

    .inline-block {
        display: inline-block;
    }

    .w-5 {
        width: 5%;
        line-height: 1;
    }

    .w-10 {
        width: 10%;
        line-height: 1;
    }

    .w-020 {
        width: 20px;
        line-height: 1;
    }

    .w-20 {
        width: 20%;
        line-height: 1;
    }

    .w-40 {
        width: 40%;
        line-height: 1;
    }

    .w-050 {
        width: 50%;
        line-height: 1;
    }

    .w-70 {
        width: 70px;
        line-height: 1;
    }

    .w-90 {
        width: 90px;
        line-height: 1;
    }

    .right-auto(@width) {
        width: calc(100% - @width);
        margin-left: 2px;
        text-align: left;
        line-height: 1;
    }

    .vertical(@position) {
        vertical-align: @position;
    }

    .overflow {
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap;
        word-break: break-word;
    }

}

