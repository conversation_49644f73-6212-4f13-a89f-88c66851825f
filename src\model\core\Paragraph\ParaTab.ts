import {ParaElementType} from './ParagraphContent';
import TextProperty from '../TextProperty';
import {measure} from '../util';
import {ParaElementBase, RepalceText} from './ParaElementBase';

export default class ParaTab extends ParaElementBase {

  constructor() {
    super();
    this.content = RepalceText.Space; // String.fromCharCode(0x0022);
    this.type = ParaElementType.ParaTab;
  }

  /**
   * 测量字符
   * @param textPr
   */
  public measure( textPr: TextProperty ): number {
    const m = measure(this.content, textPr)[0];

    this.width = m.width;
    this.width = this.width << 2;

    this.widthVisible = this.width;
    return m.height;
  }

  /**
   * 拷贝
   * @param bForUI true: UI显示时所需宽度等信息
   */
  public copy(bForUI: boolean = false): ParaTab {
    const newTab = new ParaTab();

    newTab.positionX = this.positionX;
    newTab.positionY = this.positionY;
    newTab.bViewSecret = this.bViewSecret;

    if ( true === bForUI) {
      newTab.type = this.type;
      newTab.width = this.width;
      newTab.widthVisible = this.width;
      newTab.content = RepalceText.SpaceForUI;
    }

    return newTab;
  }

  public isSpace(): boolean {
    return false;
  }

  public isParaTab(): boolean {
    return true;
  }
}
