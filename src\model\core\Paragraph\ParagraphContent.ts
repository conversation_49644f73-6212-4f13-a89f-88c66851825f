
enum BreakType {
    BreakLine = 1,
    BreakPage,
}

/**
 * 段落元素的类型
 */
export enum ParaElementType {
    Unkown = -1,
    ParaText = 1,
    ParaTextPr,
    ParaSpace,
    ParaEnd,
    ParaNewLine,
    ParaSym,
    ParaTab,
    // Para_NewLineRendered,
    ParaPortion,
    ParaPageNum,
    ParaDrawing,
    ParaComment,  // 批注
    ParaMedEquation,  // 医学公式
    ParaNewControlBorder,
    ParaFixedHeight, // 打印留白高度
    ParaSvgDrawing,
    ParaVideoDrawing,
    ParaAudioDrawing,
    ParaBarcode,     // 条形码
    ParaQRCode,     // 二维码
    ParaButton,
}

export enum ParaTextName {
    ParaCheckbox = 1,
    Region = 2,
    PortionStyle,
}

/**
 * 当前光标所在位置
 */
export interface IParaPos {
    range?: number;
    line: number;  // 行号
    page: number;  // ParaPage页面
    pos: number;  //  portionContent
}

/**
 * 段落中ParaPortion的位置和深度（嵌套关系）
 * 通常：depth = 0，data[0]记录的是portionPos，即portion的索引
 *       depth = 1，data[1]记录的是portionContentPos，即portion内容的索引
 */
export class ParagraphContentPos {
    public data: number[] = [0, 0, 0];
    public depth: number = 0;
    public bPlaceholder: boolean = false; // 占位符
    public bStart: boolean;

    public add(pos: number): void {
        this.data[this.depth] = pos;
        this.depth++;
    }
    public unShift(pos: number): void {
        this.data.unshift(pos);
        this.depth++;
    }

    public clear(): void {
        this.data = [];
        this.depth = 0;
    }

    public splice(start: number, count: number, item?: number[]): number[] {
        if (start < 0) {
            return [];
        }
        let actCount = count;
        if (start + count > this.depth) {
            actCount = this.depth - start;
        }

        if (item === undefined) {
            item = [];
        }
        const res = this.data.splice(start, actCount, ...item);
        this.depth = this.data.length;
        return res;
    }

    public set(otherPos: ParagraphContentPos): void {
        const len = otherPos.depth;

        for (let pos = 0; pos < len; pos++) {
            this.data[pos] = otherPos.data[pos];
        }

        this.depth = otherPos.depth;

        if (this.data.length > this.depth) {
            this.data.length = this.depth;
        }
    }

    public update(pos: number, depth: number): void {
        this.data[depth] = pos;
        this.depth = depth + 1;
    }

    public update2(pos: number, depth: number): void {
        this.data[depth] = pos;
    }

    public get(depth: number = 0): number {
        return this.data[depth];
    }

    public getDepth(): number {
        //增加点防御 by tinyzhi
        if (typeof this.depth !== 'number') {
            return 0; 
        }
        return this.depth - 1;
    }

    /**
     * 比较两个位置记录点的先后顺序：必须是同一个段落
     * 1: 当前位置（this）在后；  0：都在相同位置；    -1： 其他位置otherPos在后
     * @param otherPos
     */
    public compare( otherPos: ParagraphContentPos ): number {
        let depth = 0;
        const len1 = this.data.length;
        const len2 = otherPos.data.length;
        const lenMin = Math.min(len1, len2);

        while ( depth < lenMin ) {
            if ( this.get(depth) > otherPos.get(depth) ) {
                return 1;
            } else if ( this.get(depth) === otherPos.get(depth) ) {
                depth++;
                continue;
            } else {
                return -1;
            }
        }

        if ( len1 !== len2 ) {
            return -1;
        }

        return 0;
    }

    public copy(): ParagraphContentPos {
        const contentPos = new ParagraphContentPos();
        contentPos.depth = this.depth;
        contentPos.data = this.data.slice();

        return contentPos;
    }

    public shift(): number {
        if ( 1 <= this.data.length ) {
            this.depth--;
            return this.data.shift();
        }

        return 0;
    }


    public pop(): number {
        if ( 1 <= this.data.length ) {
            this.depth--;
            return this.data.pop();
        }

        return -1;
    }

    public reset(): void {
        this.clear();
        this.data = [0, 0, 0];
    }
}
