import { CodeValueItem } from '../../../common/commonDefines';
import { ChangeBaseBoolProperty, ChangeBaseObjectProperty, ChangeBaseStringProperty,
    ChangeBaseLongProperty, ChangeBase, ChangeBaseContent } from '../HistoryChange';
import { changeObjectFactory, HistroyItemType } from '../HistoryDescription';
import ParaPortion from '../Paragraph/ParaPortion';
import { NewControl, NewControlContent } from './NewControl';

export class ChangeNewControlAddItem extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, pos: number, parent: NewControl, bAdd?: boolean ) {
        super(changeClass, pos, parent, (false === bAdd ? false : true));
        this.type = HistroyItemType.NewControlAddItem;
    }

    public undo(): void {
        const newControl = this.changeClass as NewControl;
        const newControlManager = newControl.getNewControlManager();
        if ( null == this.new ) {
            const newControls = newControlManager.getAllNewControls();
            newControls.splice(this.old, 1);
        } else {
            // const parent = newControl.getParent();
            const leafList = this.new.getLeafList();
            leafList.splice(this.old, 1);
        }
        if ( this.color ) {
            newControlManager.removeNewControlInMapOnly(newControl);
        }
    }

    public redo(): void {
        const newControl = this.changeClass as NewControl;
        const newControlManager = newControl.getNewControlManager();

        if ( null == this.new ) {
            const newControls = newControlManager.getAllNewControls();
            newControls.splice(this.old, 0, newControl);
        } else {
            // const parent = newControl.getParent();
            const leafList = this.new.getLeafList();
            if ( leafList ) {
                leafList.splice(this.old, 0, newControl);
            }

            const leafList2 = newControl.getLeafList();
            if ( leafList2 ) {
                leafList2.forEach((element) => {
                    newControlManager.addNewControlInMapOnly(element);
                });
            }
        }
        if ( this.color ) {
            newControlManager.addNewControlInMapOnly(newControl);
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlRemoveItem extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, pos: number, parent: NewControl, bAdd?: boolean ) {
        super(changeClass, pos, parent, (false === bAdd ? false : true));
        this.type = HistroyItemType.NewControlRemoveItem;
    }

    public undo(): void {
        const newControl = this.changeClass as NewControl;
        const newControlManager = newControl.getNewControlManager();

        if ( null == this.new ) {
            const newControls = newControlManager.getAllNewControls();
            newControls.splice(this.old, 0, newControl);
        } else {
            // const parent = newControl.getParent();
            const leafList = this.new.getLeafList();
            if ( leafList ) {
                leafList.splice(this.old, 0, newControl);
            }
        }
        // const leafList2 = newControl.getLeafList();
        // if ( leafList2 ) {
        //     leafList2.forEach((element) => {
        //         newControlManager.addNewControlInMapOnly(element);
        //         this.addLeafNewControl(element.getLeafList(), newControlManager);
        //     });
        // }
        this.addLeafNewControl(newControl.getLeafList(), newControlManager);
        if ( this.color ) {
            newControlManager.addNewControlInMapOnly(newControl);
        }
    }

    public redo(): void {
        const newControl = this.changeClass as NewControl;
        const newControlManager = newControl.getNewControlManager();
        if ( null == this.new ) {
            const newControls = newControlManager.getAllNewControls();
            newControls.splice(this.old, 1);
        } else {
            const leafList = this.new.getLeafList();
            leafList.splice(this.old, 1);
        }
        if ( this.color ) {
            newControlManager.removeNewControlInMapOnly(newControl);
        }
    }

    private addLeafNewControl(leafList: NewControl[], newControlManager: any): void {
        if (leafList && leafList.length) {
            leafList.forEach((element) => {
                newControlManager.addNewControlInMapOnly(element);
                this.addLeafNewControl(element.getLeafList(), newControlManager);
            });
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlRemoveLeafItems extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: NewControl[], news: NewControl[], bAdd?: boolean ) {
        super(changeClass, old, news, false);
        this.type = HistroyItemType.NewControlRemoveLeafItems;
    }

    public undo(): void {
        this.changeClass.leafList = this.old;
    }

    public redo(): void {
        this.changeClass.leafList = this.new;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlRemoveLeafItems2 extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl[], pos: number, items: NewControl[], bAdd?: boolean ) {
        super(changeClass, pos, items, false);
        this.type = HistroyItemType.NewControlRemoveLeafItems2;
    }

    public undo(): void {
        if ( this.changeClass && this.new ) {
            this.new.forEach((element, index) => {
                this.changeClass.splice(this.old + index, 0, element);
            });
        }
    }

    public redo(): void {
        if ( this.changeClass && this.new ) {
            this.changeClass.splice(this.old, this.new.length);
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangNewControlBorder extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: ParaPortion, news: ParaPortion, bStart: boolean) {
        super(changeClass, old, news, bStart);
        this.type = HistroyItemType.NewControlBorder;
    }

    public setValue(value: ParaPortion): void {
        const newControl = this.getClass();
        if ( true === this.color ) {
            newControl.content.startBorderPortion = value;
        } else {
            newControl.content.endBorderPortion = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlPlaceHolderPortion extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControlContent, old: ParaPortion, news: ParaPortion, bStart?: boolean) {
        super(changeClass, old, news, bStart);
        this.type = HistroyItemType.NewControlPlaceHolderPortion;
    }

    public setValue(value: ParaPortion): void {
        const newControlContent = this.getClass();
        if ( newControlContent ) {
            newControlContent.placeHolder = value;
        }
    }

    public isDirty(): boolean {
        return (this.changeClass && this.changeClass.getParent() && this.changeClass.getParent()
                    .isTableCellContent());
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlPlaceHolder extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaPortion, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlPlaceHolder;
    }

    public setValue( value: boolean ): void {
        const portion = this.getClass();
        if ( portion ) {
            portion.bPlaceHolder = value;
        }
    }

    public write(): any {
        return super.write(this.type);
    }

    public isDirty(): boolean {
        return (this.changeClass && this.changeClass.isTableCellContent());
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlName extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlName;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            const newControlManager = newControl.getNewControlManager();
            const newControlNames = newControlManager.getNameMap();
            newControlNames.delete(newControl.name);

            newControl.name = value;
            newControl.content.setNewControlName(value);
            newControlNames.set(value, newControl);
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlPlaceHolderContent extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlPlaceHolderContent;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.content.placeHolderContent = value;
        }
    }

    public isDirty(): boolean {
        return (this.changeClass && this.changeClass.getDocumentParent() && this.changeClass.getDocumentParent()
                    .isTableCellContent());
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlMustInput extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlMustInput;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bMustInput = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlCanntEdit extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlCanntEdit;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bCanntEdit = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlForceValidate extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlForceValidate;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.forceValidate = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlTips extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlTips;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.tipsContent = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlCanntDelete extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlCanntDelete;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bCanntDelete = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlCanntCopy extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlCanntCopy;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bCanntCopy = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlHiddenBackground extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlHiddenBackground;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bHiddenBackground = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlReverseEdit extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlReverseEdit;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bReverseEdit = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlSerialNumber extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlSerialNumber;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.serialNumber = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlStartDate extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlStartDate;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.startDate = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlEndDate extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlEndDate;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.endDate = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlHidden extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlHidden;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bHidden = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlShowBorder extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlShowBorder;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bShowBorder = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlViewSecretType extends ChangeBaseLongProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlViewSecretType;
    }

    public setValue(value: number): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.viewSecretType = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlContentMaxLength extends ChangeBaseLongProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlContentMaxLength;
    }

    public setValue(value: number): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.maxLength = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlContentFixedLength extends ChangeBaseLongProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlContentFixedLength;
    }

    public setValue(value: number): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.fixedLength = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlContentHideHasTitle extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlContentHideHasTitle;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.hideHasTitle = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlDateBoxFormat extends ChangeBaseLongProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlDateBoxFormat;
    }

    public setValue(value: number): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.dateBoxFormat = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlDateBoxTime extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlDateBoxTime;
    }

    public setValue(value: number): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.dateTime = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlDateBoxText extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlDateBoxText;
    }

    public setValue(value: number): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.text = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlNumMaxValue extends ChangeBaseLongProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlNumMaxValue;
    }

    public setValue(value: number): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.maxValue = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlNumMinValue extends ChangeBaseLongProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlNumMinValue;
    }

    public setValue(value: number): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.minValue = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlNumPrecision extends ChangeBaseLongProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlNumPrecision;
    }

    public setValue(value: number): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.precision = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlNumUnit extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlNumUnit;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.unit = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlTitle extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlTitle;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.content.title = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlMapAddItem extends ChangeBase {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl ) {
        super(changeClass);
        this.type = HistroyItemType.NewControlMapAddItem;
    }

    public undo(): void {
        const newControl = this.changeClass as NewControl;
        const newControlManager = newControl.getNewControlManager();
        newControlManager.removeNewControlInMapOnly(newControl);
    }

    public redo(): void {
        const newControl = this.changeClass as NewControl;
        const newControlManager = newControl.getNewControlManager();
        newControlManager.addNewControlInMapOnly(newControl);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlPrefix extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlPrefix;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            if ( true === this.color ) {
                newControl.selectPrefixContent = value;
            } else {
                newControl.prefixContent = value;
            }
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlSeparator extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlSeparator;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.separator = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlRetrieve extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlRetrieve;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bRetrieve = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlItemSelect extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: CodeValueItem, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlItemSelect;
    }

    public setValue(value: boolean): void {
        const item = this.getClass();
        if ( item ) {
            item.bSelect = value;
        }
    }

    // public isDirty(): boolean {
    //     return this.color;
    // }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlItemCode extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: CodeValueItem, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlItemCode;
    }

    public setValue(value: string): void {
        const item = this.getClass();
        if ( item ) {
            item.code = value;
        }
    }

    // public isDirty(): boolean {
    //     return this.color;
    // }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlItemValue extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: CodeValueItem, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlItemValue;
    }

    public setValue(value: string): void {
        const item = this.getClass();
        if ( item ) {
            item.value = value;
        }
    }

    // public isDirty(): boolean {
    //     return true;
    // }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangNewControlParent extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControlContent, old: any, news: any, bStart?: boolean) {
        super(changeClass, old, news, bStart);
        this.type = HistroyItemType.NewControlParent;
    }

    public setValue(value: any): void {
        const newControlContent = this.getClass();
        if ( newControlContent ) {
            newControlContent.parent = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlShowValue extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlShowValue;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bShowValue = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangNewControlParentControl extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: NewControl, news: NewControl, bStart?: boolean) {
        super(changeClass, old, news, bStart);
        this.type = HistroyItemType.NewControlParentControl;
    }

    public setValue(value: any): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.parentControl = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlShowRight extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlShowRight;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bShowRight = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlRadioShowType extends ChangeBaseLongProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlRadioShowType;
    }

    public setValue(value: number): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.showType = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlRadioChecked extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlRadioChecked;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.checked = value;
            newControl.resetItemTextColor?.();
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlRadioPortions extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, pos: ParaPortion[], items: ParaPortion[], bAdd?: boolean ) {
        super(changeClass, pos, items, true);
        this.type = HistroyItemType.NewControlRadioPortions;
    }

    public undo(): void {
        if ( this.changeClass && this.old ) {
            this.changeClass.radioPortions = this.old;
        }
    }

    public redo(): void {
        if ( this.changeClass && this.new ) {
            this.changeClass.radioPortions = this.new;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlRadioPortions2 extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, pos: any, items: any, bAdd?: boolean ) {
        super(changeClass, pos, items, true);
        this.type = HistroyItemType.NewControlRadioPortions2;
    }

    public undo(): void {
        if ( this.changeClass && this.old ) {
            this.changeClass.portions = this.old;
        }
    }

    public redo(): void {
        if ( this.changeClass && this.new ) {
            this.changeClass.portions = this.new;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlPrintChecked extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlPrintChecked;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bPrintSelected = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlRadioItemSelected extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: any, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlRadioItemSelected;
    }

    public setValue(value: boolean): void {
        const item = this.getClass();
        if ( item ) {
            item.bSelect = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlLabelCode extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlLabelCode;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.labelCode = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlAddrSelection extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlShowRight;
    }

    public setValue(value: any): void {
        const newControl = this.getClass();
        if ( newControl ) {
            const {province, city, county, hierarchy} = value;
            newControl.province = province;
            newControl.city = city;
            newControl.county = county;
            newControl.hierarchy = hierarchy;
        }
    }

}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlAddItems extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl[], pos: number, items: NewControl[], bAdd?: boolean ) {
        super(changeClass, pos, items, true);
        this.type = HistroyItemType.NewControlAddItems;
    }

    public undo(): void {
        if ( this.changeClass && this.new ) {
            this.changeClass.splice(this.old, this.new.length);
        }
    }

    public redo(): void {
        if ( this.changeClass && this.new ) {
            this.new.forEach((element, index) => {
                this.changeClass.splice(this.old + index, 0, element);
            });
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlMapAddItems extends ChangeBaseContent {

    constructor( changeClass: any, pos: number, items: any, color?: boolean ) {
        super(changeClass, pos, items, true);
        this.type = HistroyItemType.NewControlMapAddItems;
    }

    public undo(): void {
        const newControlManager = this.changeClass;
        if ( newControlManager && this.items ) {
            const names = newControlManager.getNameMap();
            this.items.forEach((element) => {
                names.delete(element.getNewControlName());
            });
        }
    }

    public redo(): void {
        const newControlManager = this.changeClass;
        if ( newControlManager && this.items ) {
            const names = newControlManager.getNameMap();
            this.items.forEach((element) => {
                names.set(element.getNewControlName(), element);
            });
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlCheckBoxChecked extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlCheckBoxChecked;
    }

    public setValue(value: boolean): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.bSelected = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNewControlGroup extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: NewControl, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlGroup;
    }

    public setValue(value: string): void {
        const newControl = this.getClass();
        if ( newControl ) {
            newControl.group = value;
        }
    }
}

changeObjectFactory[HistroyItemType.NewControlPlaceHolder] = ChangeNewControlPlaceHolder;
