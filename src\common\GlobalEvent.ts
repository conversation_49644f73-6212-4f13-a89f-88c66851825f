class HandlerEvent {
    private _callbacks: object;
    private _activeId: number;

    constructor() {
        this._callbacks = {};
    }

    public isExistEvent(id: number, name: string, callback: (...arrs: any[]) => any ): boolean {
      const callbacks = this._callbacks;
      const arrs = callbacks[name + id];
      if (arrs === undefined) {
        return false;
      }

      for (let index = 0, length = arrs.length; index < length; index++) {
        if (callbacks[index] && callback && callbacks[index].toString() === callback.toString()) {
          return true;
        }
      }

      return false;
    }

    public setEvent(id: number, name: string, ...arrs: any[]): boolean {
      this._activeId = id;
      const sName = name + id;
      const callbacks = this._callbacks;
      if (callbacks.hasOwnProperty(sName)) {
          // for (let i = 0; i < callbacks[name].length; ++i) {
          const datas = callbacks[sName];
          // if (name === GlobalEventName.Mousedown) {
          //   // console.log(sName)
          //   // console.log(datas)
          // }

          for (let i = datas.length - 1; i >= 0; i--) {
            const callback = datas[i];
            if (typeof callback === 'function') {
              callback.apply(window, arrs);
            }
          }

          return true;
      }
      return false;
    }

    /**
     * 新增回调方法
     * @param id docid，全局唯一，使用这个键的原因：当一个页面有多个编辑器时，那么必定造成触发错误问题
     * @param name 名称
     * @param callback 回调函数
     */
    public addEvent(id: number, name: string, callback: (...arrs: any[]) => any): void {
      const sName = name + id;
      if (!this._callbacks.hasOwnProperty(sName)) {
        this._callbacks[sName] = [];
      }
      this._callbacks[sName].push(callback);

      if (name === GlobalEventName.Mousedown) {
        // console.log(sName)
        // console.log(this._callbacks[sName])
      }

    }

    public getActiveDocId(): number {
      return this._activeId;
    }

    public deleteEvent(id: number, name: string, callback: (...arrs: any[]) => any): void {
      const sName = name + id;
      const callbacks = this._callbacks;
      const arrs = callbacks[sName];
      if (arrs !== undefined) {
        for (let i = arrs.length - 1; i > -1; --i) {
          if (arrs[i] === callback) {
            callbacks[sName].splice(i, 1);
            if (i === 0 && !callbacks[sName].length) {
              delete callbacks[sName];
            }
          }
        }
      }
    }

    public deleteAllEventByName(id: number, name: string): void {
      const sName = name + id;
      const callbacks = this._callbacks;
      delete callbacks[sName];
    }

    public getAllEvents(): any {
      return this._callbacks;
    }
}

// tslint:disable-next-line: sy-global-const-name
export const GlobalEvent = new HandlerEvent();


export enum GlobalEventName {
    ContentChange = 'editorContentChange',
    UnMounted = 'editorUnMounted',
    Mousedown = 'mousedown',
    Mousemove = 'mousemove',
    Mouseup = 'mouseup',
    TableMouseup = 'tableMouseup',
    TableShowtips = 'tableShowtips',
    TableMousemove = 'tableMousemove',
    TableMousedown = 'tableMousedown',
    TableMessageTip = 'tableMessageTip',
    SelectImage = 'selectImage',
    Dblclick = 'dblclick',
    DialogEvent = 'dialogEvent',
    Selection = 'selection',
    MoveCursor = 'moveCursor',
    CursorVisible = 'cursorVisible',
    NewControlToShow = 'newControlToShow',
    NewControlTips = 'newControlTips',
    ViewScale = 'viewScale',
    Click = 'click',
    Blur = 'blur',
    Resize = 'resize',
    UpdateCursorType = 'updateCursorType',
    VisibilityChange = 'visibilityChange',
    Readonly = 'readonly',
    RegionActive = 'regionActive',
    RightMenuVisible = 'rightMenuVisible',
    MainMenuVisible = 'mainMenuVisible',
    ToolBarItemVisible = 'toolBarItemVisible',
    ToolBarCustomChange = 'toolBarCustomChange',
    CheckedChange = 'checkedChange',
    VisibleChange = 'visibleChange',
    WindowClick = 'windowClick',
    NewControlChange = 'newControlChange',
    RegionChange = 'regionChange',
    CommentChange = 'commentChange', // 批注变更
    RevisionChange = 'revisionChange', // 修订刷新
    AutoSaveInc = 'autoSaveInc',
    MessageEvent = 'messageEvent',
    RefreshDocUI = 'RefreshDocUI',
    TableEvent = 'tableEvent',
    NISTableEvent = 'NISTableEvent',
    TableCellActive = 'tableCellActive',
    ViewMode = 'viewMode',
    NewNISCellToShow = 'newNISCellToShow',
    OpenCompleted = 'openCompleted',
    ViewPropChange = 'viewPropChange',
    TipDbClick = 'TipDbClick',
    KeyDown = 'KeyDown',
    Search = 'search',
    OutFocus = 'outFocus',
    SendMonitorData = 'sendMonitorData',
}
