import { XmlComponent } from '../../xml-components';

import { TableCell } from '../table-cell';
import { TableRowProperties } from './table-row-properties';
import { TableRowHeight } from '../../../../../model/core/Table/TableRowProperty';

export class TableRow extends XmlComponent {
    private properties: TableRowProperties;

    constructor(private readonly cells?: TableCell[]) {
        super('w:tr');

        this.initializeProperties();

        if (cells != null) {
            cells.forEach((c) => this.root.push(c));
        }

    }

    // custom method, may be useful
    public setCell(cols: number): TableRow {
        const cells: TableCell[] = [];
        for (let j = 0; j < cols; j++) {
            cells.push(new TableCell());
        }
        if (cells != null) {
            cells.forEach((c) => this.root.push(c));
        }
        return this;
    }

    public getCell(ix: number): TableCell {
        const cell = this.cells[ix];

        if (!cell) {
            throw Error('Index out of bounds when trying to get cell on row');
        }

        return cell;
    }

    // public addGridSpan(index: number, cellSpan: number): TableCell {
    //     const remainCell = this.cells[index];
    //     remainCell.CellProperties.addGridSpan(cellSpan);
    //     this.cells.splice(index + 1, cellSpan - 1);
    //     this.root.splice(index + 2, cellSpan - 1);

    //     return remainCell;
    // }

    // public mergeCells(startIndex: number, endIndex: number): TableCell {
    //     const cellSpan = endIndex - startIndex + 1;

    //     return this.addGridSpan(startIndex, cellSpan);
    // }

    public addRowHeight(tableRowHeight: TableRowHeight): TableRow {
        this.properties.addRowHeight(tableRowHeight);

        return this;
    }

    public addIsTableHeader(tblHeader: string): TableRow {
        this.properties.addIsTableHeader(tblHeader);
        return this;
    }

    /**
     * initialize properties if not
     */
    private initializeProperties(): void {
        if (!this.properties) {
            this.properties = new TableRowProperties();

            // should be the first child element
            // this.root.push(this.properties);
            this.root.unshift(this.properties);
        }
    }
}
