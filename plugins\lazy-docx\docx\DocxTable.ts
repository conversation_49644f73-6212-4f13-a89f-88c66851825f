import { ISerialTableObj, ISerialTableRowObj, SerialObjType } from '../../serialize/serialInterface';
import { Table, TableRow } from 'docx';
import DocxTableRow from './DocxTableRow';
import { IAbstractDocx, IDocx } from './IDocx';

export default class DocxTable extends IAbstractDocx implements IDocx {
    constructor(private readonly tableObj: ISerialTableObj) { super(); }

    public buildTo(collector: Table[]): Table[] {
        if (this.tableObj.type !== SerialObjType.Table) {
            return collector;
        }
        const option: any = Object.assign({}, this.tableObj);
        this.deleteUnUsefulProp(option, 'type', 'children');
        const children: TableRow[] = [];
        for (const content of this.tableObj.children) {
            if (content.type === SerialObjType.TableRow) {
                new DocxTableRow(content as ISerialTableRowObj).buildTo(children);
            }
        }
        option.rows = children;
        const table = new Table(option);
        collector.push(table);
        return collector;
    }
}
