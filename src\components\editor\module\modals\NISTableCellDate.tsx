import * as React from 'react';
import Dialog from '../../ui/Dialog';
import But<PERSON> from '../../ui/Button';
import Checkbox from '../../ui/CheckboxItem';
// tslint:disable-next-line: max-line-length
import { CustomPropertyElementType, DateBoxFormat, DAYS_DROPDOWN, INISProperty, MONTHS_DROPDOWN, NISDateBoxFormat, NIS_DATE_FORMAT_STRING, YEARS_DROPDOWN } from '../../../../common/commonDefines';
import CustomPropertyBtn from './CustomProperty';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import Select from '../../ui/select/Select';
import Input from '../../ui/Input';
import { TableCell } from '../../../../model/core/Table/TableCell';
import { DocumentCore } from '../../../../model/DocumentCore';
import '../../style/NewNISDateBox.less';
import { NISTableCellCommon } from './NISTableCellCommonProps';

interface IDialogProps {
    documentCore: DocumentCore;
    visible: boolean;
    id?: string;
    close: (name: string | number, bRefresh?: boolean) => void;
    refresh: (bRefresh: boolean, timeout?: number) => void;
}

interface IState {
    bRefresh: boolean;
}

interface IType {
    key: string;
    value: NISDateBoxFormat;
}

export default class NISTableCellDate extends React.Component<IDialogProps, IState> {
    private cellProps: any; // revised cell props
    // private tableProps: any;
    private visible: any;
    private bCustomProperty: boolean;
    private types: IType[];
    private customChecked: boolean;
    private timeout: any;
    private nisProps: any;

    constructor(props: any) {
        super(props);
        this.cellProps = {
            bProtected: false,
            // bCanFormulaCacl: false,
            // formularType: false,
            customProperty: undefined,
            serialNumber: undefined,
            dateBoxFormat: NISDateBoxFormat.DateSlash,
            customFormat: {},
            hideDateText: false,
            visible: undefined
        };

        this.types = [
            {key: NIS_DATE_FORMAT_STRING[NISDateBoxFormat.FullDateSlash], value: NISDateBoxFormat.FullDateSlash},
            {key: NIS_DATE_FORMAT_STRING[NISDateBoxFormat.DateSlash], value: NISDateBoxFormat.DateSlash},
            {key: NIS_DATE_FORMAT_STRING[NISDateBoxFormat.FullDateHyphen], value: NISDateBoxFormat.FullDateHyphen},
            {key: NIS_DATE_FORMAT_STRING[NISDateBoxFormat.DateHyphen], value: NISDateBoxFormat.DateHyphen},
        ];

        this.state = {
            bRefresh: false,
        };
        this.nisProps = {};

        this.visible = this.props.visible;

        // this.init();
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public render(): any {

        return (
            <Dialog
                id={this.props.id}
                visible={this.visible}
                width={380}
                // height={350}
                open={this.open}
                preventDefault={false}
                title='日期单元格'
                confirm={this.confirm}
                footer={this.renderFooter()}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='serialNumber'
                                value={this.cellProps.serialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='tile'>属性：</span>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='bProtected'
                            value={this.cellProps.bProtected}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            禁止编辑
                        </Checkbox>
                        <Checkbox
                            name='hideDateText'
                            value={this.cellProps.hideDateText}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            隐藏日期显示
                        </Checkbox>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='visible'
                            value={this.cellProps.visible}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            网格线
                        </Checkbox>
                    </div>
                    <div className='editor-line'>
                        <CustomPropertyBtn
                            name='customProperty'
                            properties={this.cellProps.customProperty}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            id='bCustomProperty'
                            visible={this.bCustomProperty}
                            type={CustomPropertyElementType.TableCell}
                        />
                    </div>
                    <NISTableCellCommon prop={this.nisProps}/>
                    <div className='editor-line editor-multi-line'>
                        <label className='table-label title w-70'>日期格式</label>
                        <div className='right-auto'>
                            {this.renderFormatList()}
                        </div>
                    </div>
                    <div className='editor-line editor-multi-line'>
                        <div className='editor-line custom-date'>
                            <Checkbox
                                name='customFormat' // name not in use
                                value={this.customChecked}
                                disabled={false}
                                onChange={this.openCustomChange}
                            >
                                自定义日期格式
                            </Checkbox>
                        </div>
                        {this.renderCustomFormat()}
                    </div>
                </div>
            </Dialog>
        );
    }

    private renderCustomFormat(): any {
        let className = 'custom-format';
        const disabled = this.cellProps.dateBoxFormat !== NISDateBoxFormat.AutoCustom;
        if (disabled) {
            className += ' un-active';
        }

        const customFormat = this.cellProps.customFormat;
        return (
            <div className={className}>
                <div className='editor-line custom-table'>
                    <div className='w-20 nis-custom-right'>
                        <Select
                            data={YEARS_DROPDOWN}
                            value={customFormat.year}
                            name='year'
                            disabled={disabled}
                            onChange={this.customChange}
                        />
                    </div>
                    <div className='w-10 nis-custom-right'>
                        <Input
                            value={customFormat.yearAndMonth}
                            disabled={disabled}
                            name='yearAndMonth'
                            unDeleted={true}
                            onChange={this.customChange}
                        />
                    </div>
                    <div className='w-20 nis-custom-right'>
                        <Select
                            data={MONTHS_DROPDOWN}
                            disabled={true}
                            value={customFormat.month}
                            name='month'
                            onChange={this.customChange}
                        />
                    </div>
                    <div className='w-10 nis-custom-right'>
                        <Input
                            value={customFormat.monthAnyDay}
                            disabled={disabled}
                            name='monthAnyDay'
                            unDeleted={true}
                            onChange={this.customChange}
                        />
                    </div>
                    <div className='w-20'>
                        <Select
                            data={DAYS_DROPDOWN}
                            disabled={disabled}
                            value={customFormat.day}
                            name='day'
                            onChange={this.customChange}
                        />
                    </div>
                </div>
            </div>
        );
    }

    private renderFormatList(): any {
        const activeValue = this.cellProps.dateBoxFormat;
        const list = this.types.map((item) => {
            let className = null;
            if (item.value === activeValue) {
                className = 'active';
            }
            return (
                <li className={className} key={item.key} onClick={this.onClick.bind(this, item)}>{item.key}</li>
            );
        });
        return (
            <ul className='format-list'>
                {list}
            </ul>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private init(): void {
        const curCellProps = this.props.documentCore.getTableCellProps();
        // console.log(curCellProps)
        if (curCellProps != null) {
            // copy object
            // deep copy, but lose TS type; function is said cannot be copied
            // const curCellPropsCopy = JSON.parse(JSON.stringify(curCellProps));

            // TODO: normal props
            this.cellProps.bProtected = curCellProps.bProtected;
            const nisProps = this.nisProps = curCellProps.nisProperty;
            if (nisProps != null) {
                const {dateBoxFormat, customFormat, hideDateText, customProperty, serialNumber} = nisProps;
                this.cellProps.customProperty = customProperty;
                this.cellProps.dateBoxFormat = dateBoxFormat;
                this.cellProps.customFormat = customFormat;
                this.cellProps.hideDateText = hideDateText;
                this.cellProps.serialNumber = serialNumber;
                this.cellProps.visible = nisProps.gridLine?.visible;
                if (customFormat == null || (customFormat != null && Object.keys(customFormat).length === 0)) {
                    this.initCustomProps();
                } else {
                    this.cellProps.customFormat = {...customFormat};
                }

                if (dateBoxFormat === NISDateBoxFormat.AutoCustom) {
                    this.customChecked = true;
                } else {
                    this.customChecked = false;
                }
            }
        }
        // console.log(this.cellProps)

    }

    private checkChange(name: string, subname: string, e: any): void {
        this[name][subname] = !this[name][subname];

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private openCustomChange = (value: any, name: string): void => {
        // TODO? existing weird logic
        this.customChecked = value;
        this.cellProps.dateBoxFormat = NISDateBoxFormat.AutoCustom;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private customChange = (value: any, name: string): void => {
        const customFormat = this.cellProps.customFormat;
        customFormat[name] = value;
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
            // this.setShowTime();
            this.setState({bRefresh: !this.state.bRefresh});
        }, 300);
    }

    private open = (): void => {
        const { documentCore } = this.props;
        const curNisCell = true; // documentCore.getCurNISCell();
        // console.log(curNisCell)
        if (curNisCell != null) {
            // init props here first
            this.init();
        }
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClick = (item: IType): void => {
        this.cellProps.dateBoxFormat = item.value;
        this.customChecked = false;
        // this.setShowTime();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private initCustomProps(): void {
        this.cellProps.customFormat = {
            year: YEARS_DROPDOWN[0].value,
            month: MONTHS_DROPDOWN[0].value,
            day: DAYS_DROPDOWN[0].value,
            // hour: hours[0].value,
            // minute: minutes[0].value,
            // second: seconds[0].value,
            // millisecond: '',
            yearAndMonth: '-',
            monthAnyDay: '-',
            // dayAfter: ' ',
            // hourAndMinute: ':',
            // minuteAndSecond: ':',
            // secondAfter: '',
        };
    }

    private onChange = (value: any, name: string): void => {
        this.cellProps[name] = value;
    }

    private confirm = (id?: any): void => {
        const { documentCore } = this.props;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        const nisProperty: INISProperty = {
            dateBoxFormat: this.cellProps.dateBoxFormat,
            serialNumber: this.cellProps.serialNumber,
            customFormat: this.cellProps.customFormat,
            customProperty: this.cellProps.customProperty,
            hideDateText: this.cellProps.hideDateText,
            gridLine: {visible: this.cellProps.visible}
        };

        if (NISDateBoxFormat.AutoCustom !== this.cellProps.dateBoxFormat) {
            nisProperty.customFormat = null;
        }
        // console.log(nisProperty)
        documentCore.setTableCellProps({bProtected: this.cellProps.bProtected,
            nisProperty});

        this.close(true);
    }

}
