import React from 'react';
import '../../style/select.less';

interface IData {
    value: any;
    key: string;
    bDefault?: boolean;
}

interface IProps {
    datas: IData[];
    value: any;
    name: string;
    label?: string;
    width?: number | string;
    onChange: (value: any, name: string) => void;
}

interface IState {
    value: string;
    bDown: boolean;
}
/**
 * 暂不存储选择状态
 */
export class CommentSelect extends React.Component<IProps, IState> {
    private ref: React.RefObject<HTMLDivElement>;
    constructor(props: IProps) {
        super(props);
        this.state = {
            value: props.value,
            bDown: true,
        };
        this.ref = React.createRef<HTMLDivElement>();
    }

    public render(): React.ReactNode {
        const {width} = this.props;
        const style: any = {};
        if (width) {
            style.width = typeof width === 'string' ? width : width + 'px';
        }

        return (
            <div className="c-select-container" style={style}>
                <div className='c-label'>{this.props.label}</div>
                <div className='c-selector' tabIndex={-1} ref={this.ref}>
                    {this.renderList()}
                </div>
            </div>
        );
    }

    public componentDidMount(): void {
        const dom = this.ref.current;
        if (dom) {
            dom.addEventListener('click', this.handleClick);
            dom.addEventListener('blur', this.handleBlur);
        }
    }

    public componentWillUnmount(): void {
        const dom = this.ref.current;
        if (dom) {
            dom.removeEventListener('click', this.handleClick);
            dom.removeEventListener('blur', this.handleBlur);
        }
    }

    private handleClick = (event: MouseEvent): void => {
        event.stopPropagation();
        const dom = event.target as HTMLDivElement;
        const classList = dom.classList;
        if (classList.contains('item')) {
            this.props.onChange(dom.dataset['index'], this.props.name);
        }
        this.setState({bDown: !this.state.bDown});
    }

    private handleBlur = (event) => {
        event.stopPropagation();
        if (!this.state.bDown) {
            this.setState({bDown: true})
        }
    }

    private renderList(): React.ReactNode {
        const {datas, value} = this.props;
        if (!datas.length) {
            return null;
        }
        const children: any[] = [];
        let target = datas[0].key;
        for (const data of datas) {
            if (value === data.value) {
                target = data.key;
            }
            children.push(
                <div className='item' data-index={data.value} key={data.value}>
                    {data.key}
                </div>
            );
        }
        const {bDown} = this.state;
        const radioClass = `radio${bDown ? ' radio-down' : ' radio-up'}`;
        const itemsStyle: any = {};
        if (bDown) {
            itemsStyle.display = 'none';
        }
        return (
            <React.Fragment>
                <div className='select'>
                    <div className='content'>{target}</div>
                    <div className={radioClass}/>
                </div>
                <div className='items' style={itemsStyle}>
                    {children}
                </div>
            </React.Fragment>
        );
    }
}
