/*

The MIT License (MIT)

Copyright (c) 2015 Thomas <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

*/

import { Blob } from './Blob';
import { Obj } from './Primitives';

export class ColorRef {
    public r: number;
    public g: number;
    public b: number;

    constructor(reader: Blob, r?: number, g?: number, b?: number) {
        if (reader != null) {
            this.r = reader.readUint8();
            this.g = reader.readUint8();
            this.b = reader.readUint8();
            reader.skip(1);
        } else {
            this.r = r;
            this.g = g;
            this.b = b;
        }
    }

    public clone(): ColorRef {
        return new ColorRef(null, this.r, this.g, this.b);
    }

    public toHex(): string {
        // tslint:disable-next-line: no-bitwise
        const rgb = (this.r << 16) | (this.g << 8) | this.b;
        return (0x1000000 + rgb).toString(16)
            .slice(1);
    }

    public toString(): string {
        return '{r: ' + this.r + ', g: ' + this.g + ', b: ' + this.b + '}';
    }
}

export class Font extends Obj {
    public height: number;
    public width: number;
    public escapement: number;
    public orientation: number;
    public weight: number;
    public italic: number;
    public underline: number;
    public strikeout: number;
    public charset: number;
    public outprecision: number;
    public clipprecision: number;
    public quality: number;
    public pitch: number;
    public family: number;
    public facename: string;

    constructor(reader: Blob, copy: Font | number) {
        super('font');
        if (reader != null) {
            this.height = reader.readInt16();
            this.width = reader.readInt16();
            this.escapement = reader.readInt16();
            this.orientation = reader.readInt16();
            this.weight = reader.readInt16();
            this.italic = reader.readUint8();
            this.underline = reader.readUint8();
            this.strikeout = reader.readUint8();
            this.charset = reader.readUint8();
            this.outprecision = reader.readUint8();
            this.clipprecision = reader.readUint8();
            this.quality = reader.readUint8();
            const pitchAndFamily = reader.readUint8();
            // tslint:disable-next-line: no-bitwise
            this.pitch = pitchAndFamily & 0xf; // TODO: double check
            // tslint:disable-next-line: no-bitwise
            this.family = (pitchAndFamily >> 6) & 0x3; // TODO: double check

            const dataLength = copy as number;
            const start = reader.pos;
            this.facename = reader.readNullTermString(Math.min(dataLength - (reader.pos - start), 32));
        } else if (copy != null) {
            copy = copy as Font;
            this.height = copy.height;
            this.width = copy.width;
            this.escapement = copy.escapement;
            this.orientation = copy.orientation;
            this.weight = copy.weight;
            this.italic = copy.italic;
            this.underline = copy.underline;
            this.strikeout = copy.strikeout;
            this.charset = copy.charset;
            this.outprecision = copy.outprecision;
            this.clipprecision = copy.clipprecision;
            this.quality = copy.quality;
            this.pitch = copy.pitch;
            this.family = copy.family;
            this.facename = copy.facename;
        } else {
            // TODO: Values for a default font?
            this.height = -80;
            this.width = 0;
            this.escapement = 0;
            this.orientation = 0;
            this.weight = 400;
            this.italic = 0;
            this.underline = 0;
            this.strikeout = 0;
            this.charset = 0;
            this.outprecision = 0;
            this.clipprecision = 0;
            this.quality = 0;
            this.pitch = 0;
            this.family = 0;
            this.facename = 'Helvetica';
        }
    }

    public clone(): Font {
        return new Font(null, this);
    }

    public toString(): string {
        return JSON.stringify(this);
    }
}
