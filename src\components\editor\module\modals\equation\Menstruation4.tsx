import * as React from 'react';
import Dialog from '../../../ui/Dialog';
import Input from '../../../ui/Input';
import Button from '../../../ui/Button';
// import { message } from '../../../../../common/Message';
import { ParaEquation } from '../../../../../model/core/Paragraph/ParaDrawing';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    equation: ParaEquation;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface ITestContent {
    value1?: string;
    value2?: string;
    value3?: string;
}

interface IState {
    bRefresh: boolean;
}

export default class Menstruation3 extends React.Component<IDialogProps, {}> {
    private visible: boolean;
    private data: ITestContent;
    private oldData: ITestContent;
    private equationElemDom: HTMLElement;

    constructor(props: any) {
        super(props);
        this.visible = this.props.visible;
        this.data = {};
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                open={this.open}
                footer={this.renderFooter()}
                width={400}
                title={'月经史公式4'}
            >
                <div className='menstruation4'>
                    <div  className='w-050'>
                        <div className='editor-line'>
                            初潮年龄
                        </div>
                        <div className='editor-line'>
                            <div className='w-050'>
                                <Input
                                    name='value1'
                                    onChange={this.onChange}
                                    value={this.data.value1}
                                />
                            </div>
                        </div>
                        <div className='line'></div>
                    </div>
                    <div className='w-050'>
                        <div className='editor-line'>
                            经期(天)
                        </div>
                        <div className='editor-line'>
                            <div className='w-050'>
                                <Input
                                    name='value2'
                                    onChange={this.onChange}
                                    value={this.data.value2}
                                />
                            </div>
                        </div>
                        <div className='line'></div>
                        <div className='editor-line'>
                            周期(天)
                        </div>
                        <div className='editor-line'>
                            <div className='w-050'>
                                <Input
                                    name='value3'
                                    onChange={this.onChange}
                                    value={this.data.value3}
                                />
                            </div>
                        </div>
                    </div>
                </div>

            </Dialog>
        );
    }

    public componentDidMount(): void {
        //
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        const equation = this.props.equation;
        // let equationElemDom: HTMLElement;
        const data = this.data = {};
        const oldData = this.oldData = {};
        const svg = equation.equationElem;
        if (svg) {
            const equationElemDom = this.equationElemDom = new DOMParser().parseFromString(
                svg,
                'text/xml',
            ).documentElement;
            const texts = equationElemDom.querySelectorAll('text');
            if (texts.length > 0) {
                texts.forEach((text, index) => {
                    const curText = text.innerHTML.trim();
                    data['value' + ++index] = curText;
                    oldData['value' + index] = curText;
                });
            }
        }

        this.setState({});
    }

    private onChange = (value: any, name: string): void => {
        this.data[name] = value;
        this.setState({});
    }

    private close = (bRefresh: boolean = false): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({});
    }

    private isSameData(): boolean {
        const datas = this.data;
        const oldDatas = this.oldData;

        const keys = Object.keys(datas);
        for (let index = 0, length = keys.length; index < length; index++) {
            const key = keys[index];
            if (datas[key] !== oldDatas[key]) {
                return false;
            }
        }

        return true;
    }

    private confirm = (): void => {
        if (this.isSameData()) {
            this.close();
            return;
        }
        const documentCore = this.props.documentCore;
        const dom = this.equationElemDom;
        const texts = dom.querySelectorAll('text');
        const datas = this.data;
        texts[0].innerHTML = datas.value1 || '';
        texts[1].innerHTML = datas.value2 || '';
        texts[2].innerHTML = datas.value3 || '';

        // 修改属性
        const svgStr = dom.outerHTML;
        const drawObj = documentCore.getDrawingObjects();
        const svgConvertedURI = drawObj.convertSVGToImageString(svgStr);
        // drawObj.setDrawingProp(this.props.equation.name, {
        documentCore.setDrawingProp(this.props.equation.name, {
            width: this.props.equation.width,
            src: svgConvertedURI,
            equationElem: svgStr,
        });
        this.close(true);
    }

}
