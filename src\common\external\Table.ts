import { DocumentCore } from '../../model/DocumentCore';
import { EmrEditor } from '../../components/editor/Main';
import { ResultType, isValidName, EXTERNAL_STRUCT_TYPE, DataType, NISRowType,
    BloodPressureFormat,
    isStrictDateFormat} from '../commonDefines';
import { ExternalAction } from './ExternalAction';
import { jsql } from './SQLExec';
export default class Table extends ExternalAction {
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    constructor(host: EmrEditor) {
        super(host.state.documentCore);
        this._host = host;
        this._documentCore = host.state.documentCore;
    }

    public insertTable(name: string, col: number , row: number, hasTitlerow: boolean, nNumbers: number ): number {
        if (typeof name !== 'string' || !name || !isValidName(name) || !(col > 0) || !(row > 0)) {
            return ResultType.ParamError;
        }

        if (hasTitlerow === true && (row <= nNumbers || !(nNumbers > -1)) || hasTitlerow !== undefined &&
        (typeof hasTitlerow !== 'boolean' || typeof nNumbers !== 'number')) {
            return ResultType.ParamError;
        }

        if (hasTitlerow !== true) {
            nNumbers = undefined;
        }
        
        const res = this._documentCore.insertNewTable(col, row, nNumbers, name);
        
        if (res) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public insertTableWithParament(name: string, col: number, row: number, sJson: string): number {
        if (typeof name !== 'string' || !name || !isValidName(name) || !(col > 0) || !(row > 0)) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        let headerNum = 0;
        if (row <= obj.headerNum || !(obj.headerNum > -1) || typeof obj.headerNum !== 'number') {
            return ResultType.ParamError;
        } else {
            headerNum = obj.headerNum;
        }

        let bRepeatHeader = true;
        if (null != obj.bRepeatHeader && typeof obj.bRepeatHeader !== 'boolean') {
            return ResultType.ParamError;
        } else {
            bRepeatHeader = obj.bRepeatHeader;
        }
        
        const res = this._documentCore.insertNewTable(col, row, headerNum, name, bRepeatHeader);
        
        if (res) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public putCellContentByArray(sName: string, sJsonContent: string): number {
        if (typeof sName !== 'string' || typeof sJsonContent !== 'string'
            || !sName || !sJsonContent) {
            return ResultType.ParamError;
        }
        let obj: any;
        try {
            obj = JSON.parse(sJsonContent);
        } catch (error) {
            return ResultType.ParamError;
        }

        
        const res = this._documentCore.putCellContentByArray(sName, obj);
        
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public getAllTableNamesByCurrentDoc(): string {
        const tableNames = this._documentCore.getAllTableNames();
        return tableNames.join(',');
    }

    public protectTable(sTable: string , bEditorProtect: boolean): number {
        if (typeof sTable !== 'string' || !sTable || typeof bEditorProtect !== 'boolean') {
            return ResultType.ParamError;
        }

        // if (bDeleteProtect) {
        //     bCanDeleteRow = false;
        // }

        const res = this._documentCore.setTableProperty1(sTable, {
            bReadOnly: bEditorProtect,
        });

        // if (res === ResultType.Success) {
        //     this._host.handleRefresh();
        // }

        return res;
    }

    public protectTableCell(sTable: string, sCellName: string, bEditorProtect: boolean): number {
        if (typeof sTable !== 'string' || !sTable || typeof sCellName !== 'string' || !sCellName || typeof bEditorProtect !== 'boolean') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setTableCellProtect(sTable, sCellName, bEditorProtect);
        return res;
    }

    public getTableNameByCurrentCursor(): string {
        return this._documentCore.getCurrentTableName();
    }

    /** 将光标移动至当前表格所选行的最后一个单元格 */
    public moveCursorToTableRowEnd(): boolean {
        const table = this._documentCore.getCurrentTable();
        if (table != null && table.isTable()) {
            table.moveCursorToSelectionRowEnd();
            this._documentCore.clearSelection();
            this._documentCore.updateCursorXY();
            // this._host.handleRefresh();
            this._host.updateCursor();
            return true;
        }
        return false;
    }

    /**
     * 返回单元格内结构化元素名称集合
     * @param sTalbeName 表格名称
     * @param sCellName 单元格名称
     * @return 结构化元素name，以英文逗号隔开
     */
    public getStructsNameByCell(sTalbeName: string, sCellName: string): string {
        if (!sTalbeName || !sCellName) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getStructsNameByCell(sTalbeName, sCellName);
    }

    public getTableCellNameByCurrentCursor(): string {
        return this._documentCore.getCurrentCellName();
    }

    /**
     * 合并目标表格的指定单元格
     * @param sTableName 表格名称 
     * @param sCellNames 单元格名称（多个单元格以分号隔开）
     */
    public mergeTableCell(sTableName: string, sCellNames: string): boolean {
        if (!sTableName || typeof sTableName !== 'string' ||
            !sCellNames || typeof sCellNames !== 'string') {
            return false;
        }
        const table = this._documentCore.getTableByName(sTableName);
        const cellNames = sCellNames.split(',');
        if (table && cellNames.length) {
            const res = table.mergeTableCellByCellNames(cellNames);
            res && this._host.handleRefresh();
            return res;
        }
        return false;
    }

    /**
     * 获取指定表格指定单元格的文本内容
     * @param sTableName 表格名称
     * @param sCellName 单元格名称
     */
     public getCellContent(sTableName: string, sCellName: string): string {
        if (!sTableName || typeof sTableName !== 'string' ||
            !sCellName || typeof sCellName !== 'string') {
            return ResultType.StringEmpty;
        }
        const table = this._documentCore.getTableByName(sTableName);
        if (table) {
            const cell = table.getTableCellByName(sCellName);
            if (cell) {
                return cell.getCellTextWithAttr();
            }
        }
        return ResultType.StringEmpty;
    }

    /**
     * 获取指定表格指定单元格所在列的文本内容
     * @param sTableName 表格名称
     * @param sCol 指定列
     */
     public getColContent(sTableName: string, sCol: string): string {
        const colIndex = + sCol;
        if (!sTableName || typeof sTableName !== 'string' ||
            !sCol || isNaN(+sCol) || colIndex < 1) {
            return ResultType.StringEmpty;
        }
        const table = this._documentCore.getTableByName(sTableName);
        if (table) {
            const cells = table.getCellsByColIndex(colIndex - 1);
            const resObj = {
                name: sTableName,
                col: colIndex,
                content: []
            };
            if (cells && cells.length) {
                // tslint:disable-next-line: prefer-for-of
                for (let idx = 0; idx < cells.length; idx++) {
                    const cell = cells[idx];
                    if (cell) {
                        resObj.content.push({
                            cellName: table.getTableCellName(idx, colIndex - 1),
                            value: cell.getCellTextWithAttr()
                        });
                    }
                }
                if (resObj.content.length > 0) {
                    return JSON.stringify(resObj);
                }
            }
        }
        return ResultType.StringEmpty;
    }

    public incMultiRows(name: string, nIndex: number , nCount: number, nDirection: number): number {
        if (typeof name !== 'string' || typeof nIndex !== 'number' ||
            typeof nCount !== 'number' || typeof nDirection !== 'number' ||
            !name || nIndex < 1 || !(nCount > 0) || (nDirection !== 0 && nDirection !== 1)) {
            return ResultType.ParamError;
        }

        if (nIndex !== (nIndex | 0) || nCount !== (nCount | 0)) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.insertTableRows(name, nIndex - 1, nCount, nDirection);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public delMultiRows(name: string, index: number, nCount: number, nDirection: number): number {
        if (typeof name !== 'string' || typeof index !== 'number' ||
            typeof nCount !== 'number' || typeof nDirection !== 'number' ||
            !name || index < 0 || !(nCount > 0) ||
            (nDirection !== 0 && nDirection !== 1)) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.deleteTableRows(name, index, nCount, nDirection);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public setTableCellFormula(sName:string, sCell:string,sFormula:string){
        if (typeof sName !== 'string' || typeof sCell !== 'string' || typeof sFormula !== 'string' || !sName || !sCell  || !sFormula) {
            return ResultType.ParamError;
        }
        const table = this._documentCore.getTableByName(sName);
        if (table) {
            const cell = table.getTableCellByName(sCell);
            if (cell) {
                const rowIndex = table.getRowIndexByName(sCell);
                const colIndex = table.getColIndexByName(sCell);
                cell.setCellFormula(sFormula,rowIndex,colIndex);
            }
        }
    }

    public setTableProp(sName: string, sProp: string): number {
        if (typeof sName !== 'string' || typeof sProp !== 'string' || !sName || !sProp) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sProp);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setTableProperty2(sName, obj);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return res;
    }

    public getTableProp(sName: string, sProp: string): string {
        if (typeof sName !== 'string' || typeof sProp !== 'string' || !sName || !sProp) {
            return ResultType.StringEmpty;
        }

        let obj: any;
        try {
            obj = JSON.parse(sProp);
        } catch (error) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getTableProperty2(sName, obj);
    }

    public getTableXmlInfoByParament(sName: string, sRev: string): string {
        if (typeof sName !== 'string' || !sName || (sRev && typeof sRev !== 'string')) {
            return ResultType.StringEmpty;
        }

        const document = this._documentCore.getDocument();
        if ( document ) {
            const table = document.getTableByName(sName);

            if (table) {
                table.setApplyToAll(true);
                const startPos = table.getCurContentPosInDoc(true, true);
                const endPos = table.getCurContentPosInDoc(true, false);
                const newControlManager = document.getNewControlManager();
                const newControls = newControlManager.getNewControls(startPos, endPos);
                table.setApplyToAll(false);

                if ( newControls && newControls.length ) {
                    const result: any = document.getNewControlsPropToCustom(newControls);
                    const keys = Object.keys(result);

                    keys.forEach((key) => {
                        result[key].type = EXTERNAL_STRUCT_TYPE[result[key].type];
                    });
                    return JSON.stringify(result);
                }
            }
        }
        
        return ResultType.StringEmpty;
    }

    /**
     * 获取当前光标所在表格行的索引
     * @returns 当前光标所在表格行的索引，从1开始；如果光标不在表格内，则返回-1
     */
    public getCurrentTableRowIndex(): number {
        // 获取当前光标所在的单元格名称
        const cellName = this._documentCore.getCurrentCellName();
        if (!cellName) {
            return -1; // 光标不在表格内
        }
        
        // 获取当前表格名称
        const tableName = this._documentCore.getCurrentTableName();
        if (!tableName) {
            return -1; // 无法获取表格名称
        }
        
        // 获取表格对象
        const document = this._documentCore.getDocument();
        if (!document) {
            return -1;
        }
        
        const table = document.getTableByName(tableName);
        if (!table) {
            return -1;
        }
        
        // 从单元格名称中提取行号
        // 尝试直接使用表格的方法获取当前单元格的行索引
        try {
            // 尝试获取当前单元格的行索引
            const cell = table.getCellByName(cellName);
            if (cell && cell.row) {
                // 行索引是从0开始的，需要+1使其从1开始
                return cell.row.index + 1;
            }
        } catch (e) {
            // 如果方法不存在或出错，则继续尝试其他方法
        }
        
        // 如果上述方法失败，尝试从单元格名称中提取行号
        // 单元格名称可能的格式有多种，尝试匹配数字部分
        const rowMatch = cellName.match(/[^0-9]+([0-9]+)/);
        if (rowMatch && rowMatch[1]) {
            // 提取行号并转换为数字
            const rowNumber = parseInt(rowMatch[1], 10);
            return rowNumber > 0 ? rowNumber : -1;
        }
        
        return -1; // 无法获取行索引
    }
    
    /**
     * 获取指定表格指定行的结构化元素信息
     * @param sTableName 表格名称
     * @param nRowIndex 行索引，从1开始
     * @returns 该行中的结构化元素信息，格式与 getTableXmlInfoByParament 相同
     */
    public getTableRowStructInfo(sTableName: string, nRowIndex: number): string {
        if (typeof sTableName !== 'string' || !sTableName || 
            typeof nRowIndex !== 'number' || nRowIndex < 1) {
            return ResultType.StringEmpty;
        }
        
        const document = this._documentCore.getDocument();
        if (!document) {
            return ResultType.StringEmpty;
        }
        
        const table = document.getTableByName(sTableName);
        if (!table) {
            return ResultType.StringEmpty;
        }
        
        // 表格行索引是从0开始，而用户传入的行索引是从1开始
        const rowIndex = nRowIndex - 1;
        
        // 检查行索引是否有效
        if (rowIndex < 0 || rowIndex >= table.content.length) {
            return ResultType.StringEmpty;
        }
        
        // 获取指定行
        const row = table.content[rowIndex];
        if (!row) {
            return ResultType.StringEmpty;
        }
        
        // 获取该行的单元格范围
        const cellsCount = row.getCellsCount();
        if (cellsCount <= 0) {
            return ResultType.StringEmpty;
        }
        
        // 获取该行的所有单元格中的结构化元素
        const newControlManager = document.getNewControlManager();
        const newControls = [];
        
        for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
            const cell = row.getCell(cellIndex);
            if (cell) {
                // 获取单元格的内容范围
                const cellContent = cell.content;
                if (cellContent) {
                    // 设置单元格内容为选中状态
                    cellContent.setApplyToAll(true);
                    const cellStartPos = cellContent.getCurContentPosInDoc(true, true);
                    const cellEndPos = cellContent.getCurContentPosInDoc(true, false);
                    
                    // 获取单元格内的结构化元素
                    const cellNewControls = newControlManager.getNewControls(cellStartPos, cellEndPos);
                    if (cellNewControls && cellNewControls.length > 0) {
                        newControls.push(...cellNewControls);
                    }
                    
                    // 恢复单元格内容的选中状态
                    cellContent.setApplyToAll(false);
                }
            }
        }
        
        // 如果有结构化元素，则返回其信息
        if (newControls && newControls.length > 0) {
            const result: any = document.getNewControlsPropToCustom(newControls);
            const keys = Object.keys(result);
            
            keys.forEach((key) => {
                result[key].type = EXTERNAL_STRUCT_TYPE[result[key].type];
            });
            
            return JSON.stringify(result);
        }
        
        return ResultType.StringEmpty;
    }

    public getStructsXmlInfoByParament2(sJson: string): any {
        if (typeof sJson !== 'string' || !sJson) {
            return {};
        }

        let sJsonObj = null;
        try {
            if (sJson != null) {
                sJsonObj = JSON.parse(sJson);
            }
        } catch (error) {
            // tslint:disable-next-line: no-console
            console.log('sJson format is incorrect');
            return {};
        }

        if (sJsonObj == null || sJsonObj.needTable !== 1) {
            return {};
        }

        const result: any = {table: []};
        const logicDocument = this._documentCore.getDocument();
        const tableManager = logicDocument != null ? logicDocument.tableManager : null;
        if (tableManager != null) {
            const tableMap = tableManager.getTableMap();
            for (const table of tableMap.values()) {
                const tableInfo = {name: undefined, CustomProperty: undefined};
                // console.log(table)
                tableInfo.name = table.getTableName();
                const customProps = table.getCustomProps();
                const revisedProps = {};
                for (const customProp of customProps) {
                    if (customProp.type === DataType.String) {
                        revisedProps[customProp.name] = customProp.value;
                    } else if (customProp.type === DataType.Number) {
                        revisedProps[customProp.name] = +customProp.value;
                    } else if (customProp.type === DataType.Boolean) {
                        revisedProps[customProp.name] = customProp.value === 'true' ? true : false;
                    }
                }
                // console.log(customProps);
                tableInfo.CustomProperty = revisedProps;

                result.table.push(tableInfo);
            }
        }

        return result;
    }

    /**
     * 获取表格内单元格的内容
     * @param sName 表格名称
     * @param sJson 条件json
     */
     public getTableContentByParament(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || typeof sJson !== 'string' || !sName) {
            return ResultType.StringEmpty;
        }

        const document = this._documentCore.getDocument();
        if ( document ) {
            const table = document.getTableByName(sName);
            if (table) {
                try {
                    const obj = JSON.parse(sJson);
                    const rows = table.content;
                    let startRow = 0;
                    const needTitle = Number.parseInt(obj.needTitle);
                    const rangeType = Number.parseInt(obj.rangeType);
                    const result = [];

                    // 表头过滤
                    for (let i = 0; i < rows.length; i++) {
                        const curRow = rows[i];
                        if (curRow.property.bTableHeader) {
                            startRow = i + 1;
                            if (!Number.isNaN(needTitle) && needTitle === 1) { // 为1时表示需要表头,为0时表示不需要
                                const rowResult = [];
                                for (const cell of curRow.content) {
                                    // ? 单元格返回内容
                                    rowResult.push(cell.getCellTextWithAttr(obj.customPropNeed));
                                }
                                result.push(rowResult);
                            }
                        } else {
                            break;
                        }
                    }
                    // rangeType为0时返回所有行列内容
                    if (Number.isNaN(rangeType) || rangeType !== 1) {
                        for (let i = startRow; i < rows.length; i++) {
                            const curRow = rows[i];
                            const rowResult = [];
                            for (const cell of curRow.content) {
                                // ? 单元格返回内容
                                rowResult.push(cell.getCellTextWithAttr(obj.customPropNeed));
                            }
                            result.push(rowResult);
                        }
                    } else { // 为1时返回begin到end的数据
                        const beginRow = Number.parseInt(obj.range?.beginRow);
                        const endRow = Number.parseInt(obj.range?.endRow);
                        const beginCol = Number.parseInt(obj.range?.beginCol);
                        const endCol = Number.parseInt(obj.range?.endCol);

                        const bc = (Number.isNaN(beginCol) || beginCol < 1) ? 1 : beginCol;

                        for (let i = startRow + ((Number.isNaN(beginRow) || beginRow < 1) ? 1 : beginRow) - 1;
                            i < ((Number.isNaN(endRow) || startRow + endRow >= rows.length) ? rows.length : startRow + endRow);
                            i++) {
                                const curRow = rows[i];
                                const rowResult = [];

                                const ec = (Number.isNaN(endCol) || endCol >= curRow.content.length) ? curRow.content.length : endCol;
                                for (let j = bc - 1; j < ec; j++) {
                                    // ? 单元格返回内容
                                    rowResult.push(curRow.content[j].getCellTextWithAttr(obj.customPropNeed));
                                }
                                result.push(rowResult);
                        }
                    }
                    return JSON.stringify(result);
                } catch (error) {
                    return ResultType.StringEmpty;
                }
            }
        }
        return ResultType.StringEmpty;
    }

    public insertNISTable(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' || !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        // const colsInfo: string[] = [];
        try {
            obj = JSON.parse(sJson);

            if ( typeof obj.col !== 'number' || obj.col < 0
                || typeof obj.row !== 'number' || obj.row < 0
                || typeof obj.headerNumber !== 'number' || obj.headerNumber < 0 ) {
                return ResultType.ParamError;
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        
        const res = this._documentCore.insertNISTable(obj.col, obj.row, obj.headerNumber, sName);
        

        if (res) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public deleteNISTable(sName: string): number {
        if (typeof sName !== 'string' || !sName || !isValidName(sName)) {
            return ResultType.ParamError;
        }

        
        const res = this._documentCore.deleteNISTable(sName);
        

        if (ResultType.Success === res) {
            this._host.handleRefresh();
        }

        return res;
    }

    public getNISTableNames(): string {
        const names = this._documentCore.getNISTableNames();
        if ( names && names.length ) {
            return names.toString();
        }

        return ResultType.StringEmpty;
    }

    public setNISTableColID(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        const colsID: string[] = [];
        const table = this._documentCore.getCurrentTable(sName);
        try {
            obj = JSON.parse(sJson);

            if ( !obj || !Array.isArray(obj) ) {
                return ResultType.ParamError;
            }

            const tableColsID = table.getNISTableColIDs();
            // tslint:disable-next-line: prefer-for-of
            for (let index = 0; index < obj.length; index++) {
                const id = obj[index];
                if (typeof id === 'string') {
                    if (id) {
                        colsID.push(id);
                    } else if (id === ResultType.StringEmpty && index < tableColsID.length) {
                        colsID.push(tableColsID[index]);
                    }
                } else {
                    return ResultType.ParamError;
                }
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        if ( !table || (colsID.length !== table.getNISTableColNumber()) ) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISTableColID(table, colsID);
        if (res) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public getNISTableColID(sName: string): string {
        if (typeof sName !== 'string' || !sName || !isValidName(sName)) {
            return ResultType.StringEmpty;
        }

        const table = this._documentCore.getCurrentTable(sName);
        if ( !table ) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getNISTableColID(table);
    }

    public filterColID(sName: string, serialNumberJson: string): string {
        if (typeof sName !== 'string' || typeof serialNumberJson !== 'string' ||
            !sName || !isValidName(sName) || !serialNumberJson) {
            return ResultType.StringEmpty;
        }

        let obj: any;
        try {
            obj = JSON.parse(serialNumberJson);

            if ( !obj || !Array.isArray(obj) ) {
                return ResultType.StringEmpty;
            }

            if ([...new Set(obj)].length !== obj.length) {
                return ResultType.StringEmpty;
            }
        } catch (error) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.filterColID(sName, obj);
    }

    public setNISTableMode(nMode: number): number {
        if (typeof nMode !== 'number') {
            return ResultType.ParamError;
        }
        return this._documentCore.setNISTableMode(nMode);
    }

    public setTableHeaderReadOnly(sName: string, nMode: boolean): number {
        if (typeof sName !== 'string' || !sName || !isValidName(sName) || typeof nMode !== 'boolean') {
            return ResultType.ParamError;
        }

        return this._documentCore.setTableHeaderReadOnly(sName, nMode);
    }

    public isNewNISTable(sName: string): number {
        if (typeof sName !== 'string' || !sName || !isValidName(sName)) {
            return ResultType.ParamError;
        }

        return this._documentCore.isNewNISTable(sName);
    }

    public hideNISFirstRow(sName: string, bHide: boolean): number {
        if (typeof sName !== 'string' || !sName || !isValidName(sName) || typeof bHide !== 'boolean') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.hideNISFirstRow(sName, bHide);
        if ( ResultType.Success === res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
        }

        return res;
    }

    public getNISCurrentRowID(): string {
        return this._documentCore.getNISCurrentRowID();
    }

    public getNISTableInfo(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || !isValidName(sName) ) {
            return ResultType.StringEmpty;
        }

        // let obj: INISTableInfoData;
        // try {
        //     obj = JSON.parse(sJson);
        //     if (![0, 1].includes(obj.onlyNeedNoSum)) {
        //         return ResultType.StringEmpty;
        //     }
        //     if (obj.timePropName && typeof obj.timePropName !== 'string') {
        //         return ResultType.StringEmpty;
        //     }
        //     if (obj.datePropName && typeof obj.datePropName !== 'string') {
        //         return ResultType.StringEmpty;
        //     }
        // } catch (error) {
        //     return ResultType.StringEmpty;
        // }

        return this._documentCore.getTableInfo(sName, sJson);
    }


    /**
     * 获取指定表格的行数
     * @param sTableName 表格名称
     * @returns 表格的行数，如果表格不存在则返回0
     */
    public getTableRowCount(sTableName: string): number {
        if (typeof sTableName !== 'string' || !sTableName || !isValidName(sTableName)) {
            return 0;
        }
        
        return this._documentCore.getTableRowCount(sTableName);
    }
    
    /**
     * 获取指定表格的列数
     * @param sTableName 表格名称
     * @returns 表格的列数，如果表格不存在则返回0
     */
    public getTableColumnCount(sTableName: string): number {
        if (typeof sTableName !== 'string' || !sTableName || !isValidName(sTableName)) {
            return 0;
        }
        
        return this._documentCore.getTableColumnCount(sTableName);
    }

    public getNISTableProp(sName: string): string {
        if (typeof sName !== 'string' || !sName || !isValidName(sName)) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getTableProperty2(sName, undefined);
    }

    public setRowInfo(sTableName: string, sInfo: string): number {
        if (typeof sTableName !== 'string' || !isValidName(sTableName) || typeof sInfo !== 'string') {
            return ResultType.ParamError;
        }
        let obj: any;
        try {
            obj = JSON.parse(sInfo);
            if (Array.isArray(obj) || typeof obj !== 'object') {
                return ResultType.ParamError;
            }
            if (obj.rowID && typeof obj.rowID !== 'string') {
                return ResultType.ParamError;
            }
            if (obj.cellInfo && !Array.isArray(obj.cellInfo)) {
                return ResultType.ParamError;
            }
        } catch (error) {
            return ResultType.ParamError;
        }
        const res = this._documentCore.setRowInfo(sTableName, obj);
        if (res === ResultType.Success) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
        }
        return res;
    }

    public getRowInfo(sTableName: string, rowID: string, sParam: string): string {
        if (typeof sTableName !== 'string' || !isValidName(sTableName) ||
            typeof rowID !== 'string' || typeof sParam !== 'string') {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getRowInfo(sTableName, rowID, sParam);
    }

    public getNISAllRowsID(sName: string): string {
        if (typeof sName !== 'string' || !sName || !isValidName(sName)) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getNISAllRowsID(sName);
    }

    public getNISLastRowID(sName: string): string {
        if (typeof sName !== 'string' || !sName || !isValidName(sName)) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getNISLastRowID(sName);
    }

    public getNISFirstRowID(sName: string): string {
        if (typeof sName !== 'string' || !sName || !isValidName(sName)) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getNISFirstRowID(sName);
    }

    public getHeaderRowText(sName: string): string {
        if (typeof sName !== 'string' || !sName || !isValidName(sName)) {
            return ResultType.StringEmpty;
        }

        const document = this._documentCore.getDocument();
        if ( document ) {
            const table = document.getTableByName(sName);
            if (table != null) {
                const content = table.content;
                const result = [];
                for (const row of content) {
                    if (row.isTableHeader() === true) {
                        const cells = row.content;
                        for (const cell of cells) {
                            result.push(cell.getCellContentText());
                        }
                    }
                }

                return JSON.stringify(result);
            }
        }

        return '';
    }

    public moveRowToPosition(sName: string, movedRow: string, PositionRow: string, direct: number): number {
        if (typeof sName !== 'string' || typeof movedRow !== 'string' || typeof PositionRow !== 'string' ||
            !sName || !isValidName(sName) || !movedRow || !PositionRow
            || typeof direct !== 'number' || ![1, 2].includes(direct)) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(movedRow);

            if ( !obj || typeof obj.rowID !== 'string' || !obj.rowID ||
                ((typeof obj.number !== 'number' || 0 > obj.number) && null != obj.number) ) {
                return ResultType.ParamError;
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        const result = this._documentCore.moveRowToPosition(sName, obj, PositionRow, direct);
        if (ResultType.Success === result) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
        }

        return result;
    }

    public sortRow(sName: string, rowJson: string): number {
        if (typeof sName !== 'string' || typeof rowJson !== 'string' ||
            !sName || !isValidName(sName) || !rowJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(rowJson);

            // if ( !obj || !Array.isArray(obj) ) {
            //     return ResultType.ParamError;
            // }
        } catch (error) {
            return ResultType.ParamError;
        }

        const result = this._documentCore.sortRow(sName, obj);
        if (ResultType.Success === result) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
        }

        return result;
    }

    public sortRowInRange(sName: string, beginRow: string, rowJson: string): number {
        if (typeof sName !== 'string' || typeof rowJson !== 'string' || typeof rowJson !== 'string' ||
            !sName || !isValidName(sName) || !rowJson || !beginRow) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(rowJson);

            if ( !obj || !Array.isArray(obj) ) {
                return ResultType.ParamError;
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        const result = this._documentCore.sortRowInRange(sName, beginRow, obj);
        if (ResultType.Success === result) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
        }

        return result;
    }

    public setNISRowProp(sName: string, rowID: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !rowID || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        let prop;
        try {
            obj = JSON.parse(sJson);

            prop = {
                type: obj.rowType,
                signStatus: obj.signStatus,
                bReadOnly: obj.editProtect,
                bDeleteProtect: obj.deleteProtect,
            };
        } catch (error) {
            return ResultType.ParamError;
        }

        return this._documentCore.setNISRowProp(sName, rowID, prop);
    }

    public setNISRowText(sName: string, rowID: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson || !rowID) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);

            if ( !obj || !Array.isArray(obj) ) {
                return ResultType.ParamError;
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISRowText(sName, rowID, obj);
        if ( ResultType.Success === res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
        }

        return res;
    }

    public getNISRowProp(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.StringEmpty;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getNISRowProp(sName, obj);
    }

    public getHeaderRowTextByColID(sTable: string, colIDs: string): string {
        if (!sTable || !colIDs) {
            return ResultType.StringEmpty;
        }

        let cols: string[];
        try {
            cols = JSON.parse(colIDs);
        } catch (error) {
            return ResultType.StringEmpty;
        }

        if (!cols || !Array.isArray(cols) || cols.length === 0) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getHeaderRowTextByColID(sTable, cols);
    }

    public getNISRowCreator(sName: string, rowID: string): string {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            !sName || !isValidName(sName) || !rowID) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getNISRowCreator(sName, rowID);
    }

    public setNISRowCreator(sName: string, rowID: string, creator: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' || typeof creator !== 'string' ||
            !sName || !isValidName(sName) || !creator || !rowID) {
            return ResultType.ParamError;
        }

        return this._documentCore.setNISRowCreator(sName, rowID, creator);
    }

    public getNISRowText(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.StringEmpty;
        }
        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getNISRowText(sName, obj);
    }

    public protectNISRows(sName: string, sJson: string, bFlag: boolean): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson || 'boolean' !== typeof bFlag) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);

            if ( !obj || !Array.isArray(obj) ) {
                return ResultType.ParamError;
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.protectNISRows(sName, obj, bFlag);
        if (res) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public protectNISCurrentRow(bFlag: boolean): number {
        if ('boolean' !== typeof bFlag) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.protectNISCurrentRow(bFlag);
        if (res) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public filterNISRowID(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.StringEmpty;
        }

        let obj: any;
        const sqlCmd = new Map();
        try {
            obj = JSON.parse(sJson);
            for (const operate in obj) {
                if (operate) {
                    sqlCmd.set(operate, obj[operate]);
                }
            }
        } catch (error) {
            return ResultType.StringEmpty;
        }

        // const sList = this._documentCore.getNISTableList(sName);
        const table = this._documentCore.getCurrentTable(sName);
        const sList = [];
        const colIDs = table.getNISTableColIDs();
        for (let rowIndex = 0; rowIndex < table.content.length; rowIndex++) {
            const row = table.content[rowIndex];
            if ( row && !row.isTableHeader() ) {

                const columnIDs = Object.create(null);
                for (let colIndex = 0; colIndex < row.content.length; colIndex++) {
                    const cell = row.getCell(colIndex);
                    const key = colIDs[colIndex];
                    if ( key ) {
                        const content = cell.getCellContentText();
                        columnIDs[key] = content;
                    }
                }

                if ( columnIDs ) {
                    sList.push({rowID: rowIndex, cols: columnIDs});
                }
            }
        }
        // console.log(sList)
        const result = jsql(sList, sqlCmd);

        if ( result && result.length ) {
            return result.toString();
        }

        return ResultType.StringEmpty;
    }

    // 选中某一行
    public selectNISRow(sName: string, rowID: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            !sName || !isValidName(sName) || !rowID) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.selectNISRow(sName, rowID);
        if (res) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public getSelectedNISRowNumber(): number {
        return this._documentCore.getSelectedNISRowNumber();
    }

    public cancelSelecteNISRow(): number {
        const res = this._documentCore.cancelSelecteNISRow();
        if (res) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public isCurrentNISRowCanDelete(): boolean {
        return this._documentCore.isCurrentNISRowCanDelete();
    }

    // 判断当前光标选中的列是否已经签名，如果选中多列，则返回结果为与关系
    public isCurrentNISRowSigned(): number {
        return this._documentCore.isCurrentNISRowSigned();
    }

    // 删除光标所在的当前行
    public deleteNISCurrentRow(): number {
        const curRowId = this._documentCore.getNISCurrentRowID();
        if (curRowId !== ResultType.StringEmpty && this._documentCore.isHiddenFirstNISRow(curRowId)) {
            return ResultType.Failure;
        }
        const res = this._documentCore.deleteNISCurrentRow();

        if (res) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    // 单行或者多行 {row1,row2,row3}
    public deleteNISRows(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);

            if ( !obj || !Array.isArray(obj) ) {
                return ResultType.ParamError;
            }
        } catch (error) {
            return ResultType.ParamError;
        }
        const firstRowID = this._documentCore.getNISFirstRowID(sName);
        if (firstRowID !== ResultType.StringEmpty && this._documentCore.isHiddenFirstNISRow(firstRowID)) {
            obj = obj.filter((rowid: string) => rowid !== firstRowID);
        }
        const res = this._documentCore.deleteNISRows(sName, obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public setNISSignCellText(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISSignCellText(sName, obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public deleteNISSignCellsText(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.deleteNISSignCellsText(sName, obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public async signCurrentNISRow(sJson: string): Promise<number> {
        if (typeof sJson !== 'string' || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = await this._documentCore.signCurrentNISRow(obj);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public async signNISRows(sName: string, sJson: string): Promise<number> {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = await this._documentCore.signNISRows(sName, obj);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public deleteCurrentNISRowSign(sJson: string): number {
        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.deleteCurrentNISRowSign(obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public deleteNISRowsSign(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.deleteNISRowsSign(sName, obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public getNISSignNumber(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.Failure2;
        }

        return this._documentCore.getNISSignNumber(sName, obj);
    }

    public getNISSignStatus(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        return this._documentCore.getNISSignStatus(sName, obj);
    }

    public setNISSignStatus(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        return this._documentCore.setNISSignStatus(sName, obj);
    }

    public filterNISSignRow(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.StringEmpty;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.StringEmpty;
        }

        const res = this._documentCore.filterNISSignRow(sName, obj);

        return res.toString();
    }

    public getSignNameByRow(sName: string, rowID: string): string {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            !sName || !isValidName(sName) || !rowID) {
            return ResultType.StringEmpty;
        }

        const res = this._documentCore.getSignNameByRow(sName, rowID);

        return res.toString();
    }

    public insertSumRowAfterCurrentRow(sJson: string): number {
        if (typeof sJson !== 'string' || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
            obj.before = false;
            obj.type = NISRowType.Sum;
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.insertSumRowAtCurrentRow(obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public insertSumRowBeforeCurrentRow(sJson: string): number {
        if (typeof sJson !== 'string' || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
            obj.before = true;
            obj.type = NISRowType.Sum;
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.insertSumRowAtCurrentRow(obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public insertSumRows(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.StringEmpty;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
            obj.type = NISRowType.Sum;
        } catch (error) {
            return ResultType.StringEmpty;
        }

        const res = this._documentCore.insertSumRows(sName, obj);
        if ( ResultType.StringEmpty !== res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
        }

        return res;
    }

    public deleteSumRows(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.deleteSumRows(sName, obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public setRowSumStatus(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);

            if ( !obj.rowID || !Array.isArray(obj.rowID) ) {
                return ResultType.ParamError;
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setRowSumStatus(sName, obj);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public getRowSumStatus(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.StringEmpty;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getRowSumStatus(sName, obj);
    }

    public getCurrentRowSumStatus(): number {
        return this._documentCore.getCurrentRowSumStatus();
    }

    public cleanRowSumStatus(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.cleanRowSumStatus(sName, obj);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public getLastSumRow(sName: string): string {
        if (typeof sName !== 'string' || !sName || !isValidName(sName)) {
            return ResultType.StringEmpty;
        }

        const res = this._documentCore.getLastSumRow(sName);
        if ( ResultType.StringEmpty !== res ) {
            this._host.handleRefresh();
        }

        return res;
    }

    public filterSumRow(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.StringEmpty;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.StringEmpty;
        }

        const res = this._documentCore.filterSumRow(sName, obj);
        if ( ResultType.StringEmpty !== res ) {
            this._host.handleRefresh();
        }

        return res;
    }

    public getNISRowsTextByJson(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.StringEmpty;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);

            if ( !obj.rowID || !Array.isArray(obj.rowID)
                || !obj.colID || !Array.isArray(obj.colID) ) {
                return ResultType.StringEmpty;
            }
        } catch (error) {
            return ResultType.StringEmpty;
        }

        const res = this._documentCore.getNISRowsTextByJson(sName, obj);
        if ( ResultType.StringEmpty !== res ) {
            this._host.handleRefresh();
        }

        return res;
    }

    public insertSumDetailRowAfterCurrentRow(sJson: string): number {
        if (typeof sJson !== 'string' || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
            obj.before = false;
            obj.type = NISRowType.SumDetail;
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.insertSumRowAtCurrentRow(obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public insertSumRowDetailBeforeCurrentRow(sJson: string): number {
        if (typeof sJson !== 'string' || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
            obj.before = true;
            obj.type = NISRowType.SumDetail;
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.insertSumRowAtCurrentRow(obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public insertSumDetailRows(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.StringEmpty;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
            obj.type = NISRowType.SumDetail;
        } catch (error) {
            return ResultType.StringEmpty;
        }

        const res = this._documentCore.insertSumRows(sName, obj);
        if ( ResultType.StringEmpty !== res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
        }

        return res;
    }

    public insertNISRowAfterCurrentRow(sJson: string): number {
        let obj: any;
        try {
            const contents = JSON.parse(sJson);
            const before = false;
            obj = {
                contents,
                before,
            };

            if ( !contents || !Array.isArray(contents) ) {
                return ResultType.ParamError;
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.insertNISRowAtCurrentRow(obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public insertNISRowBeforeCurrentRow(sJson: string): number {
        let obj: any;
        try {
            const contents = JSON.parse(sJson);
            const before = true;
            obj = {
                contents,
                before,
            };

            if ( !contents || !Array.isArray(contents) ) {
                return ResultType.ParamError;
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.insertNISRowAtCurrentRow(obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public insertNISRows(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.StringEmpty;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.StringEmpty;
        }

        const res = this._documentCore.insertNISRows(sName, obj);
        if ( ResultType.StringEmpty !== res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
        }

        return res;
    }

    /**
     * 对指定行的血压单元格设置血压格式
     * @param sName 表格名称
     * @param rowID 行id
     * @param colID 列id
     * @param format 1 – AAA:BBB 2 – AAA/BBB 3 – AAA BBB
     */
    public setBPCellFormat(sName: string, rowID: string, colID: string, type: number, format?: string): number {
        if (!sName || !rowID || !colID ) {
            return ResultType.ParamError;
        }

        switch ( type ) {
            case 1: {
                format = BloodPressureFormat.AColonB;
                break;
            }
            case 2: {
                format = BloodPressureFormat.ASlashB;
                break;
            }
            case 3: {
                format = BloodPressureFormat.ASpaceB;
                break;
            }
            default: {
                return ResultType.ParamError;
            }
        }

        const res = this._documentCore.setBPCellFormat(sName, rowID, colID, format);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    /**
     * 对指定行的血压单元格设置血压格式
     * @param sName 表格名称
     * @param rowID 行id
     * @param colID 列id
     * @param value “AAA=xxx,BBB=xxx” 严格按照此格式传入，否则会失败
     */
    public setBPCellValue(sName: string, rowID: string, colID: string, value: string): number {
        if (!sName || !rowID || !colID || !value || typeof value !== 'string') {
            return ResultType.ParamError;
        }
        const matchs = /^AAA=(\d{1,3}),BBB=(\d{1,3})$/g.exec(value);
        if (!matchs) {
            return ResultType.ParamError;
        }
        const res = this._documentCore.setBPCellValue(sName, rowID, colID, `${matchs[1]}:${matchs[2]}`);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public judgeTimeAndBpCell(sTable: string, sRowID: string): string {
        if (!sTable || !sRowID) {
            return ResultType.ParamError + '';
        }

        return this._documentCore.judgeTimeAndBpCell(sTable, sRowID);
    }

    public setNISCellType(sName: string, rowID: string, colID: string, nType: number): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' || typeof colID !== 'string' ||
            !sName || !isValidName(sName) || (!rowID && !colID) || typeof nType !== 'number'
            || ![0, 1, 2, 3, 4, 5, 6, 7].includes(nType)) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISCellType(sName, rowID, colID, nType);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public getNISCellType(sName: string, rowID: string, colID: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' || typeof colID !== 'string' ||
            !sName || !isValidName(sName) || !rowID || !colID) {
            return ResultType.Failure2;
        }

        return this._documentCore.getNISCellType(sName, rowID, colID);
    }

    public setNISCellLayout(sName: string, rowID: string, colID: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !rowID || !colID || !sJson) {
            return ResultType.Failure;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISCellLayout(sName, rowID, colID, sJson);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public setNISCellText(sName: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISCellText(sName, obj);
        if ( res ) {
            this._host.hideNewControlLayer();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public getNISCellText(sName: string, sJson: string): string {
        if (typeof sName !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson) {
            return ResultType.StringEmpty;
        }
        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getNISCellText(sName, obj);
    }

    public setNISCellProp(sName: string, rowID: string, colID: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !sJson || !rowID) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISCellProp(sName, rowID, colID, obj);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public getNISCellProp(sName: string, rowID: string, colID: string, sPropName: string): string {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || typeof sPropName !== 'string' ||
            !sName || !isValidName(sName) || !rowID || !colID || !sPropName) {
            return ResultType.StringEmpty;
        }
        return this._documentCore.getNISCellProp(sName, rowID, colID, sPropName);
    }

    public getNISCellsPropOfFirstRow(sName: string, sPropName: string): string {
        if (typeof sName !== 'string' || typeof sPropName !== 'string' ||
            !sName || !isValidName(sName) || !sPropName) {
            return ResultType.StringEmpty;
        }

        const res = this._documentCore.getNISCellsPropOfFirstRow(sName, sPropName);
        return JSON.stringify(res);
    }

    public setNISDateCellFormat(
        sName: string, rowID: string, colID: string, nType: number, sFormat: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || typeof nType !== 'number' || (0 === nType && typeof sFormat !== 'string') ||
            !sName || !isValidName(sName) || !rowID) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISDateCellFormat(sName, rowID, colID, nType, sFormat);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public setNISDateCellValue(sName: string, rowID: string, colID: string, sValue: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || typeof sValue !== 'string' ||
            !sName || !isValidName(sName) || !rowID) {
            return ResultType.ParamError;
        }

        // sValue should be strict 'xxxx-xx-xx'
        if (isStrictDateFormat(sValue) === false) {
            return ResultType.ParamError;
        }
        const res = this._documentCore.setNISDateCellValue(sName, rowID, colID, sValue);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public setNISDateCellProp(sName: string, rowID: string, colID: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !rowID) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISDateCellProp(sName, rowID, colID, sJson);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public setNISTimeCellFormat(
        sName: string, rowID: string, colID: string, nType: number, sFormat: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' || typeof colID !== 'string' ||
            typeof nType !== 'number' || typeof sFormat !== 'string' ||
            !sName || !isValidName(sName) || !rowID) {
            return ResultType.ParamError;
        }
        const reg = /HH([\s\S]+)MM/g;
        const match = reg.exec(sFormat);
        if (!match) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISTimeCellFormat(sName, rowID, colID, nType, sFormat);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public setNISTimeCellValue(sName: string, rowID: string, colID: string, sValue: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || typeof sValue !== 'string' ||
            !sName || !isValidName(sName) || !rowID || !sValue || 5 < sValue.length) {
            return ResultType.ParamError;
        }

        const time = sValue.split(':');
        if ( !time || 2 !== time.length
            || 0 > Number.parseInt(time[0], 0) || 24 <= Number.parseInt(time[0], 0)
            || 0 > Number.parseInt(time[1], 0) || 60 <= Number.parseInt(time[1], 0)) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISTimeCellValue(sName, rowID, colID, sValue);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public setNISCompoundCellCodeAndValueByArray(
        sName: string, rowID: string, colID: string, sJson: string, nType?: number): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !colID || !sJson) {
            return ResultType.ParamError;
        }

        if ( null != nType && (typeof nType !== 'number' || (0 !== nType % 1) || nType < 1 || 4 < nType)) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISCompoundCellCodeAndValueByArray(sName, rowID, colID, obj, nType);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public setNISCompoundCellSeparator(sName: string, rowID: string, colID: string, sSeparator: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || typeof sSeparator !== 'string' ||
            !sName || !isValidName(sName) || !colID || !sSeparator) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISCompoundCellSeparator(sName, rowID, colID, sSeparator);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public getNISCompoundCelllCurrentValue(sName: string, rowID: string, colID: string, nType?: number): string {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' ||
            !sName || !isValidName(sName) || !rowID || !colID ||
            (null !== nType && typeof nType !== 'number')) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getNISCompoundCelllCurrentValue(sName, rowID, colID, nType);
    }

    public getNISCompoundCelllCurrentCode(sName: string, rowID: string, colID: string, nType?: number): string {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || !sName || !isValidName(sName) || !rowID || !colID ||
            (null !== nType && typeof nType !== 'number')) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getNISCompoundCelllCurrentCode(sName, rowID, colID, nType);
    }

    public setNISNumCellMaxValue(sName: string, rowID: string, colID: string, maxValue: number): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' ||
            !sName || !isValidName(sName) || !rowID || !colID || typeof maxValue !== 'number' || 65535 < maxValue) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISNumCellMaxValue(sName, rowID, colID, maxValue);
        if ( ResultType.Success === res ) {
            this._host.handleRefresh();
        }

        return res;
    }

    public getNISNumCellMaxValue(sName: string, rowID: string, colID: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || !sName || !isValidName(sName) || !rowID || !colID ) {
            return ResultType.NumberNaN;
        }

        return this._documentCore.getNISNumCellMaxValue(sName, rowID, colID);
    }

    public setNISNumCellMinValue(sName: string, rowID: string, colID: string, minValue: number): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' ||
            !sName || !isValidName(sName) || !rowID || !colID || typeof minValue !== 'number' || -65535 > minValue) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISNumCellMinValue(sName, rowID, colID, minValue);
        if ( ResultType.Success === res ) {
            this._host.handleRefresh();
        }

        return res;
    }

    public getNISNumCellMinValue(sName: string, rowID: string, colID: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || !sName || !isValidName(sName) || !rowID || !colID ) {
            return ResultType.NumberNaN;
        }
        return this._documentCore.getNISNumCellMinValue(sName, rowID, colID);
    }

    public setNISNumCellPrecision(sName: string, rowID: string, colID: string, precision: number): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' ||
            !sName || !isValidName(sName) || !rowID || !colID || typeof precision !== 'number'
            || (0 !== precision % 1) || precision < 0 || 8 < precision) {
            return ResultType.NumberNaN;
        }

        const res = this._documentCore.setNISNumCellPrecision(sName, rowID, colID, precision);
        if ( ResultType.Success === res ) {
            this._host.handleRefresh();
        }

        return res;
    }

    public getNISNumCellPrecision(sName: string, rowID: string, colID: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' ||
            !sName || !isValidName(sName) || !rowID || !colID) {
            return ResultType.NumberNaN;
        }

        return this._documentCore.getNISNumCellPrecision(sName, rowID, colID);
    }

    public setNISQuickCellValueByArray(sName: string, rowID: string, colID: string, sJson: string): number {
        if (typeof sName !== 'string' || typeof rowID !== 'string' ||
            typeof colID !== 'string' || typeof sJson !== 'string' ||
            !sName || !isValidName(sName) || !rowID || !colID || !sJson) {
            return ResultType.ParamError;
        }

        let obj: any;
        try {
            obj = JSON.parse(sJson);

            if ( !obj || !Array.isArray(obj) ) {
                return ResultType.ParamError;
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setNISQuickCellValueByArray(sName, rowID, colID, obj);
        if ( res ) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public moveCursorToTableCell(sTable:string, sCellName:string): number {
        if (!sTable || !sCellName) {
            return ResultType.ParamError;
        }
        
        const document = this._documentCore.getDocument();
        if ( document ) {
            const table = document.getTableByName(sTable);

            if (table) {
                const cell = table.getTableCellByName(sCellName);

                if (cell) {
                    document.removeSelection();

                    cell.content.setContentCurPos();
                    cell.moveCursorToEndPos();
                    table.curCell = cell;

                    document.setControlActiveByType(table.getDocumentSectionType(), false);
                    document.updateCursorXY();

                    this._host.handleRefresh();
                    return ResultType.Success;
                }
            }
        }

        return ResultType.Failure;
    }


    public updateNISTableByJson(sJson: string): number {
        let res = ResultType.ParamError;
        if (!sJson || typeof sJson !== 'string') {
            return res;
        }

        const datas = JSON.parse(sJson);
        if (!datas || typeof datas !== 'object' || !(datas instanceof Array)) {
            return res;
        }

        const doc = this._documentCore.getDocument();
        for (let index = 0, len = datas.length; index < len; index++) {
            const data = datas[index];
            const table = doc.getTableByName(data.tableID);
            if (table?.isNISTable()) {
                const rowsInfo = data.rows;
                if (typeof rowsInfo !== 'object' || !(rowsInfo instanceof Array)) {
                    return res;
                } else {
                    const rows = [];
                    for (let index2 = 0, len2 = rowsInfo.length; index2 < len2; index2++) {
                        const rowInfo = rowsInfo[index2];
                        const {type, direction, rowID, indexRowID, ...colIDs} = rowInfo;

                        if (1 === type) {
                            if (typeof direction !== 'number' || ![1,2].includes(direction)) {
                                return res;
                            }

                            let rowID = indexRowID;
                            if (!rowID) {
                                rowID = (table as any).getNISLastRowID();
                                rowInfo.indexRowID = rowID;
                            }

                            const row = (table as any).getRowByRowID(rowID);
                            if (!row) {
                                return res;
                            }

                            for (const colID in colIDs) {
                                if (!row.getCellByColID(colID)) {
                                    return res;
                                }
                            }

                            rows.push({
                                type, indexRowID: rowInfo.indexRowID, direction, colIDs
                            });
                        } else if (2 === type) {
                            const row = (table as any).getRowByRowID(rowID);
                            if (!row) {
                                return res;
                            }

                            for (const colID in colIDs) {
                                if (!row.getCellByColID(colID)) {
                                    return res;
                                }
                            }

                            rows.push({type, rowID, colIDs});
                        } else {
                            return ResultType.ParamError;
                        }
                    }

                    let bChange = false;
                    res = ResultType.UnEdited;

                    rows.forEach((rowInfo) => {
                        const {type, indexRowID, direction, rowID, colIDs} = rowInfo;

                        if (1 === type) {
                            bChange = true;
                            (table as any).insertNISRowByColID(indexRowID, direction, colIDs);
                        } else if (2 === type) {
                            bChange = true;
                            (table as any).setNISRowTextByColID(rowID, colIDs);
                        }
                    });

                    if (bChange) {
                        res = ResultType.Success;
                        doc.recalculate();
                        doc.updateCursorXY();
                        this._host.handleRefresh();
                    }
                }
            } else {
                return ResultType.ParamError;
            }
        }

        return res;
    }

    public getTableColInfo(sTable: string, colID: string): string {
        if (typeof sTable !== 'string' || typeof colID !== 'string' || !sTable || !colID) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getTableColInfo(sTable, colID);
    }
}
