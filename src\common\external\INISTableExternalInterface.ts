export default interface INISTableExternalInterface {
    // ---------------------------------------------------- 表操作 ------------------------------------------------

    /**
     * 在当前光标位置插入护理表格
     * @param sName 表格名称
     * @param sJson: {“row”:行数, ”col”: 列数 ,”heaerNumber”: 表头数 }
     */
    insertTable(sName: string, sJson: string): number;

    /**
     * 删除护理表格
     * @param sName 表格名称
     */
    deleteTable(sName: string): number;

    // 返回当前文档的护理表格name，如果存在多个表格，则返回多个，以,隔开
    getTableName(): string;

    // 设置表格的列信息，传递进来的值即为列id，列id初始为Col1，Col2, Col3 列id只针对除表头外的正文行
    // 列id不允许为空，不允许为null
    setTableColID(sTableName: string, sJson: string): number;

    /**
     * 返回当前文档的护理表格的列ID
     * @param sTableName 表格名称
     */
    getTableColID(sTableName: string): string;

    /**
     * 获取护理表格的指定serialNumber的列ID
     * @param sTable
     * @param serialNumberJson
     */
    filterColID(sTable: string, serialNumberJson: string): string;

    /**
     * 设置护理单的模式
     * @param nMode 模式; 1 – 模板制作 2 – 模板书写
     */
    setNisMode(nMode: number): number;

    setTableHeaderReadOnly(sName: string, nMode: boolean): number;

    /**
     * 判断指定的表格是否是新建的表格
     * @param sTableName
     */
    isNew(sTableName: string): number;

    // ---------------------------------------------------- 行ID获取 ------------------------------------------------
    hideFirstRow(sTableName: string, bHide: boolean): number;

    // 获取光标当前位置的行id，如果选中多行，则返回多个，以,隔开
    getCurrentRowID(): string;

    getTableInfo(sName: string, sJson: string): string;

    /**

     * 获取护理表格属性（目前只支持自定义属性）
     * @param sName 表格名称
     */
    getTableProp(sName: string): string;

    /**
     * 填充护理表格行信息
     * @param sTableName 护理表格名称
     * @param sInfo 填充信息
     */
    setRowInfo(sTableName: string, sInfo: string): number;

    /**
     * 获取护理表格行信息
     * @param sTableName 护理表格名称
     * @param rowID 行id
     * @param sParam 参数
     */
    getRowInfo(sTableName: string, rowID: string, sParam: string): string;

    // 返回指定表格的所有行id，不包括表头
    getAllRowsID(sTableName: string): string;

    // 返回在指定表格里，符合条件的行id，以,隔开。
    // 参数为json: 支持where命令，select命令，type命令, col: id
    // where:$.colID1==6.24 && $.colID1<12.00
    // type:1 // 1 普通行 2 汇总行 3 汇总明细行 4 所有行
    filterRowID(sTableName: string, sJon: string): string;

    // 返回最后一行的行id
    getLastRowID(sTableName: string): string;

    // 返回第一行的行id
    getFirstRowID(sTableName: string): string;

    // 获取护理表格的表头内容
    getHeaderRowText(sTableName: string): string;

    // 将指定行移动到对应位置行的之前或者之后
    // moveRowToPosition(sTable: string, movedRow: string, PositionRow: string, direct: number): number;

    // 按照传递进来的行id顺序，重排护理表格

    sortRow(sTable: string, rowJson: string): number;

    sortRowInRange(sTable: string, beginRow: string, rowJson: string): number;

    // --------------------------------------------------- 行属性，文本 抽取跟设置 -----------------------------------

    // 设置行属性，只针对非表头，包括 signStatus(true,false)，editProtect(true,false)，deleteProtect(true,false)，
    // rowType(1 – normal 2—normal&&had sum 3 – sum 4 -- sumDetail)
    setRowProp(sTableName: string, rowID: string, sJon: string): number;

    /**
     * 设置指定行id的行文本,
     * 行赋值 如果不想对某个单元格赋值，请传递null
     * {“col1”:”text1”, “col2”:”text”, “col3”:null}
     */
    // [ “rowID”:[“sInbfo1”,” sInbfo1”,null],“rowID”:[“sInbfo1”,” sInbfo1”,” sInbfo1”]]
    setRowText(sTableName: string, rowID: string, sJon: string): number;

    // 获取指定行的某个行属性，包括signStatus,editorProtect,deleteProtect,rowType
    getRowProp(sTableName: string, sJon: string): string;

    // getRowCreator(sName: string, rowID: string): string;

    // setRowCreator(sName: string, rowID: string, creator: string): number;

    // 获取一行或者多行数据 入参json为：{“row1”,”row2”};
    // 返回[ “rowID”:[“sInbfo1”,” sInbfo1”,null],“rowID”:[“sInbfo1”,” sInbfo1”,” sInbfo1”]]
    getRowText(sTableName: string, sJon: string): string;

    // -------------------------------------------------------- 行只读设置 ------------------------------------------

    // 单行或者多行 {row1,row2,row3}
    protectRows(sTableName: string, sJon: string, bFlag: boolean): number;

    // 光标当前行
    protectCurrentRow(bFlag: boolean): number;

    // ----------------------------------------------------------- 行选中 ------------------------------------------

    /**
     * 选中指定rowid的行
     * @param sTableName
     * @param rowID
     */
    selectRow(sTableName: string, rowID: string): number;

    // 当前选中的行数（光标在单元格内算选中一行）
    // getSelectedRowNumber(): number;

    // 取消光标的选中行
    cancelSelecteRow(sTableName: string): number;

    // ----------------------------------------------------------- 行状态 ------------------------------------------

    // 判断当前光标选中的行是否可以删除
    // isCurrentRowCanDelete(): boolean;

    /**
     * 判断当前光标选中的列是否已经签名，如果选中多列，则返回结果为与关系
     * 返回值：-1 – 失败  1 – 已经签名  0 – 未签名
     */

    isCurrentRowSigned(): number;


    // ----------------------------------------------------------- 行删除 ------------------------------------------

    // 删除光标所在的当前行
    // 该接口无视 行的“删除保护”，能够直接删除，如果需要控制删除，需要结合接口isCurrentRowCanDelete 使用
    deleteCurrentRow(): number;

    // 单行或者多行 {row1,row2,row3}
    deleteRows(sTableName: string, sjon: string): number;

    // --------------------------------------------------------- 行签名、解签 ------------------------------------------

    /**
     * 设置指定rowID的签名cell的文本
     * @param sName
     * @param sJson {“row1”,”col1”}或者{“row1”}
     */

    setSignCellText(sName: string, sJson: string): number;

    deleteSignCellsText(sName: string, sJson: string): number;


    /**
     * 签名光标所在行
     * @param sJson signType: -- 1文字签名, 2 图片签名 signContent – 签名信息，
     *              根据type可以是文本或者base64值 signpost (插入位置，暂不支持)
     */

    signCurrentRow(sJson: string): Promise<number>;

    signRows(sName: string, sJson: string): Promise<number>;

    deleteCurrentRowSign(sJson: string): number;

    deleteRowsSign(sName: string, sJson: string): number;

    getSignNumber(sName: string, sJson: string): number;

    getSignStatus(sName: string, sJson: string): number;

    setSignStatus(sName: string, sJson: string): number;

    filterSignRow(sName: string, sJson: string): string;

    getSignNameByRow(sName: string, rowID: string): string;

    // --------------------------------------------------------- 汇总 ------------------------------------------

    insertSumRowAfterCurrentRow(sJson: string): number;

    insertSumRowBeforeCurrentRow(sJson: string): number;

    insertSumRows(sName: string, sJson: string): string;

    deleteSumRows(sName: string, sJson: string): number;

    setRowSumStatus(sName: string, sJson: string): number;

    getRowSumStatus(sTable: string, sJson: string): string;

    getCurrentRowSumStatus(): number;

    cleanRowSumStatus(sName: string, sJson: string): number;

    getLastSumRow(sTable: string): string;

    filterSumRow(sName: string, sJson: string): string;

    getRowsTextByJson(sName: string, sJson: string): string;

    insertSumDetailRowAfterCurrentRow(sJson: string): number;

    insertSumRowDetailBeforeCurrentRow(sJson: string): number;

    insertSumDetailRows(sTable: string, sJson: string): string;

    // ----------------------------------------------------------- 行插入 ------------------------------------------

    /**
     * 在当前光标行下方插入一行
     * @param sJson 插入行的初始文本, {“col1”:”text1”,”col2”:”text3”}
     */
    insertRowAfterCurrentRow(sJson: string): number;

    /**
     * 在当前光标行下方插入一行
     * @param sJson 插入行的初始文本, {“col1”:”text1”,”col2”:”text3”}
     */
    insertRowBeforeCurrentRow(sJson: string): number;

    // 在指定行后方插入多行
    // 在指定行向下插入多行，单元格继承上一行的类型属性，新插入的行的初始文本跟随设置。未赋值的单元格值为空值。
    insertRows(sName: string, sJson: string): string;

    /**
     * 对指定行的血压单元格设置血压格式
     * @param sName 表格名称
     * @param rowID 行id
     * @param colID 列id
     * @param type 1 – AAA:BBB 2 – AAA/BBB 3 – AAA BBB
     * @param format 可选，以后自定义属性使用
     */
    // setBPCellFormat(sName: string, rowID: string, colID: string, type: number, format?: string): number;

    /**
     * 对指定行的血压单元格设置血压格式
     * @param sName 表格名称
     * @param rowID 行id
     * @param colID 列id
     * @param value “AAA=xxx,BBB=xxx” 严格按照此格式传入，否则会失败
     */
    // setBPCellValue(sName: string, rowID: string, colID: string, value: string): number;

    /**
     * 校验指定护理表格指定行里面的时间跟血压单元格的值
     * @param sTable 表格名称
     * @param sRowID 行id
     */
    // judgeTimeAndBpCell(sTable: string, sRowID: string): string;

    /**
     * 获取护理表格的表头信息。
     * @param sTable 护理表格名
     * @param colIDs colId集合 ['a1', 'a2' , 'a3', 'a4']
     */
    getHeaderRowTextByColID(sTable: string, colIDs: string): string;

    // ------------------------------------------------- 单元格操作: 文本设置获取 ----------------------------------------

    setCellType(sName: string, rowID: string, colID: string, nType: number): number;

    getCellType(sName: string, rowID: string, colID: string): number;

    setCellLayout(sTableName: string, rowID: string, colID: string, sJson: string): number;

    setCellText(sName: string, sJson: string): number;

    getCellText(sName: string, sJson: string): string;

    // ------------------------------------------------- 单元格属性: 文本设置获取 ----------------------------------------

    // ----------通用-----------

    setCellProp(sTableName: string, rowID: string, colID: string, sJson: string): number;

    getCellProp(sTableName: string, rowID: string, colID: string, sPropName: string): string;

    getCellsPropOfFirstRow(sTableName: string, sPropName: string): string;

    // ----------日期-----------

    setDateCellFormat(sTableName: string, rowID: string, colID: string, nType: number, sFormat: string): number;

    setDateCellValue(sTableName: string, rowID: string, colID: string, sValue: string): number;

    setDateCellProp(sTableName: string, rowID: string, colID: string, sJson: string): number;

    // ----------时间-----------

    setTimeCellFormat(sTableName: string, rowID: string, colID: string, nType: number, sFormat: string): number;

    setTimeCellValue(sTableName: string, rowID: string, colID: string, sValue: string): number;

    // ----------下拉框-----------

    setCompoundCellCodeAndValueByArray(
        sTableName: string, rowID: string, colID: string, sJson: string, nType?: number): number;

    setCompoundCellSeparator(sTableName: string, rowID: string, colID: string, sSeparator: string): number;

    getCompoundCellCurrentValue(sTableName: string, rowID: string, colID: string, nType?: number): string;

    getCompoundCellCurrentCode(sTableName: string, rowID: string, colID: string, nType?: number): string;

    // -------------number-------------

    setNumCellMaxValue(sTableName: string, rowID: string, colID: string, maxValue: number): number;

    getNumCellMaxValue(sTableName: string, rowID: string, colID: string): number;

    setNumCellMinValue(sTableName: string, rowID: string, colID: string, minValue: number): number;

    getNumCellMinValue(sTableName: string, rowID: string, colID: string): number;

    setNumCellPrecision(sTableName: string, rowID: string, colID: string, precision: number): number;

    getNumCellPrecision(sTableName: string, rowID: string, colID: string): number;

    // --------------quick---------------

    setQuickCellValueByArray(sName: string, rowID: string, colID: string, sJson: string): number;
}
