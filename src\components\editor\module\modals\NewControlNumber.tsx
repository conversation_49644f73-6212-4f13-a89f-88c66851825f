import * as React from 'react';
import Dialog from '../../ui/Dialog';
import { INewControlProperty, NewControlType, NewControlContentSecretType,
    NewControlDefaultSetting, isValidName, isValidUnit, AlignType, EventType } from '../../../../common/commonDefines';
import {layer, message} from '../../../../common/Message';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import Button from '../../ui/Button';
import CustomPropertyBtn from './CustomProperty';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import CascadeBtn from './NewCascade';
import SelectUI from '../../ui/select/Select';
import EventBtn from './NewCascadeEvent';
import { ExternalDataBind } from './ExternalDataBind';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    property?: INewControlProperty;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NewControlNum extends React.Component<IDialogProps, IState> {
    private newControl: INewControlProperty;
    private displayType: boolean;
    private _currentName: string;
    private refContainer: any;
    private visible: boolean;
    private bCascade: boolean;
    private alignments: any[];
    private bCascadeEvent: boolean;
    private resetSourceBind: boolean;
    private dataBind: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };

        this.newControl = {newControlName: undefined};
        this.refContainer = {
            minValue: React.createRef(),
            maxValue: React.createRef(),
            precision: React.createRef(),
            unit: React.createRef(),
        };
        this.visible = this.props.visible;
        this.alignments = [
            {
                key: '默认',
                value: AlignType.Left,
            },
            // {
            //     key: '右对齐',
            //     value: AlignType.Right,
            // },
            {
                key: '居中对齐',
                value: AlignType.Center,
            },
        ];
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={350}
                open={this.open}
                title='数值框'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <span className='title'>常规</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlName'
                                value={this.newControl.newControlName}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlSerialNumber'
                                value={this.newControl.newControlSerialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>标识符</div>
                        <div className='right-auto'>
                            <Input
                                name='identifier'
                                value={this.newControl.identifier}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>提示符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlInfo'
                                value={this.newControl.newControlInfo}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>占位符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlPlaceHolder'
                                value={this.newControl.newControlPlaceHolder}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line editor-multi-line'>
                        <span className='title w-70'>属性</span>
                        <div className='right-auto newcontrol-prop'>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHidden'
                                        value={this.newControl.isNewControlHidden}
                                        onChange={this.onChange}
                                    >
                                        隐藏
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntDelete'
                                        value={this.newControl.isNewControlCanntDelete}
                                        onChange={this.onChange}
                                    >
                                        禁止删除
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntEdit'
                                        value={this.newControl.isNewControlCanntEdit}
                                        onChange={this.onChange}
                                    >
                                        禁止编辑
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlMustInput'
                                        value={this.newControl.isNewControlMustInput}
                                        onChange={this.onChange}
                                    >
                                        必填项
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlShowBorder'
                                        value={this.newControl.isNewControlShowBorder}
                                        onChange={this.onChange}
                                    >
                                        显示边框
                                    </Checkbox>
                                </div>

                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlReverseEdit'
                                        value={this.newControl.isNewControlReverseEdit}
                                        onChange={this.onChange}
                                    >
                                        反向编辑
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHiddenBackground'
                                        value={this.newControl.isNewControlHiddenBackground}
                                        onChange={this.onChange}
                                    >
                                        隐藏背景色
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='tabJump'
                                        value={this.newControl.tabJump}
                                        onChange={this.onChange}
                                    >
                                        键盘跳转
                                    </Checkbox>
                                </div>
                            </div>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='bTextBorder'
                                        value={this.newControl.bTextBorder}
                                        onChange={this.onChange}
                                    >
                                        带框字符
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='forceValidate'
                                        value={this.newControl.forceValidate}
                                        onChange={this.onChange}
                                    >
                                        强制校验
                                    </Checkbox>
                                </div>
                            </div>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='printSelected'
                                        value={this.newControl.printSelected}
                                        onChange={this.onChange}
                                    >
                                        打印时隐藏
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <CustomPropertyBtn
                                    name='customProperty'
                                    properties={this.newControl.customProperty}
                                    documentCore={this.props.documentCore}
                                    onChange={this.onChange}
                                    close={this.onClose}
                                    id='bCustomProperty'
                                    visible={true}
                                />
                            </div>
                        </div>
                    </div>

                    <div className='editor-line'>
                        <span className='title'>固有属性：</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-050'>
                            <Checkbox
                                name='displayType'
                                value={this.displayType}
                                onChange={this.displayTypeChange}
                            >
                                隐私显示
                            </Checkbox>
                        </div>
                    </div>

                    <div className='editor-line'>
                        <div className='w-70'>最小值</div>
                        <div className='right-auto'>
                            <Input
                                ref={this.refContainer.minValue}
                                type='number'
                                name='minValue'
                                value={this.newControl.minValue}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>

                    <div className='editor-line'>
                        <div className='w-70'>最大值</div>
                        <div className='right-auto'>
                            <Input
                                type='number'
                                ref={this.refContainer.maxValue}
                                name='maxValue'
                                value={this.newControl.maxValue}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>

                    <div className='editor-line'>
                        <div className='w-70'>精度</div>
                        <div className='right-auto'>
                            <Input
                                ref={this.refContainer.precision}
                                type='number'
                                name='precision'
                                value={this.newControl.precision}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>

                    <div className='editor-line'>
                        <div className='w-70'>单位</div>
                        <div className='right-auto'>
                            <Input
                                disabled={this.newControl.forceValidate}
                                name='unit'
                                // onBlur={this.unitBlur}
                                value={this.newControl.unit}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>
                            级联
                        </div>
                        <div className='right-auto'>
                            <CascadeBtn
                                visible={this.bCascade}
                                id='bCascade'
                                controlName={this.newControl.newControlName}
                                documentCore={this.props.documentCore}
                                properties={this.newControl.cascade}
                                name='cascade'
                                close={this.onClose}
                                onChange={this.onChange}
                                type={this.newControl.newControlType}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>
                            计算级联
                        </div>
                        <div className='right-auto'>
                            <EventBtn
                                visible={this.bCascadeEvent}
                                id='bCascadeEvent'
                                controlName={this.newControl.newControlName}
                                documentCore={this.props.documentCore}
                                properties={this.newControl.eventInfo}
                                name='eventInfo'
                                close={this.onClose}
                                onChange={this.onChange}
                                type={EventType.Sum}
                                hasMix={true}
                            />
                        </div>
                    </div>

                    <div className='editor-line'>
                        <div className='w-70'>
                            固定长度
                        </div>
                        <div className='right-auto'>
                            <Input
                                type={'number'}
                                name='newControlFixedLength'
                                value={this.newControl.newControlFixedLength}
                                onChange={this.onChange}
                                renderAppend={this.renderCell}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>
                            内部对齐
                        </div>
                        <div className='right-auto'>
                            <SelectUI
                                name='alignments'
                                onChange={this.onChange}
                                data={this.alignments}
                                value={this.newControl.alignments}
                            />
                        </div>
                    </div>
                    <ExternalDataBind
                            name={this.newControl.newControlName}
                            id='externalDataBind'
                            visible={this.visible}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            properties={this.dataBind}
                            resetId={'resetSourceBind'}
                    />
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        if ((id === 'bCustomProperty' || id === 'bCascade' || id === 'bCascadeEvent') && bRefresh) {
            this.setState({bRefresh: !this.state.bRefresh});
        } else if ('externalDataBind' === id && bRefresh) {
            this.resetSourceBind = false;
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private renderCell(): any {
        return '厘米';
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        const props = this.props.property;
        const newControl: INewControlProperty = this.newControl;
        this.displayType = false;
        if (props === undefined) {
            this.init();
            newControl.newControlName = this.props.documentCore.makeUniqueNewControlName(NewControlType.NumberBox);
        } else {
            const keys = Object.keys(props);
            keys.push('cascade');
            keys.push('eventInfo');
            keys.forEach((key) => {
                const val = props[key];
                newControl[key] = val;
            });
            if (this.newControl.newControlDisplayType === NewControlContentSecretType.AllSecret) {
                this.displayType = true;
            }
            if (newControl.alignments === undefined) {
                newControl.alignments = AlignType.Left;
            }
        }
        this.dataBind = newControl.externalDataBind;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.props.close(this.props.id, bRefresh);
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        let flag = this.unitBlur();
        if (flag === false) {
            return;
        }
        flag = this.blurValidNum();
        if (flag === false) {
            return;
        }

        this._currentName = null;
        const props = this.props.property;
        const newControl = this.newControl;
        if (!isValidName(newControl.newControlName)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        if (newControl.identifier && !isValidName(newControl.identifier)) {
            message.error('标识符不符合规范，请重新输入');
            return;
        }
        if ((!props || props.newControlName !== newControl.newControlName)
            && !documentCore.checkNewControlName(newControl.newControlName)) {
            message.error('已存在该名字，请重新命名');
            return;
        }

        if (this.resetSourceBind) {
            this.newControl.externalDataBind = {
                sourceObj: '',
                sourceKey: '',
                bReadOnly: false,
                commandUpdate: 1,
            }

            this.resetSourceBind = false;
        } else if (this.dataBind) {
            this.newControl.externalDataBind = this.dataBind;
        }

        if (props === undefined) {
            documentCore.addNewControl(this.newControl);
        } else {
            documentCore.setNewControlProperty(this.newControl, props.newControlName);
        }

        this.close(true);
    }

    private init(): void {
        const newControl = this.newControl;
        newControl.newControlName = undefined;
        newControl.newControlInfo = undefined;
        newControl.newControlPlaceHolder = NewControlDefaultSetting.DefaultPlaceHolderContent;
        newControl.newControlType = NewControlType.NumberBox;
        newControl.isNewControlHidden = false;
        newControl.isNewControlCanntDelete = false;
        newControl.isNewControlCanntEdit = false;
        newControl.isNewControlMustInput = false;
        newControl.isNewControlShowBorder = true;
        newControl.isNewControlReverseEdit = false;
        newControl.isNewControlHiddenBackground = true;
        newControl.newControlDisplayType = NewControlContentSecretType.DontSecret;
        newControl.minValue = undefined;
        newControl.maxValue = undefined;
        newControl.precision = undefined;
        newControl.unit = undefined;
        newControl.forceValidate = false;
        newControl.customProperty = undefined;
        newControl.tabJump = true;
        newControl.cascade = undefined;
        newControl.identifier = undefined;
        newControl.newControlFixedLength = undefined;
        newControl.bTextBorder = undefined;
        newControl.alignments = AlignType.Left;
        newControl.eventInfo = undefined;
        newControl.externalDataBind = undefined;
        newControl.printSelected = false;
        this.resetSourceBind = false;
    }

    private unitBlur = (): boolean => {
        const value = this.newControl.unit;
        if (value && !isValidUnit(value)) {
            message.error('单位设置错误');
            return false;
        }

        return true;
    }

    private blurValidNum = (key: string = 'minValue'): boolean => {
        // const key = this._currentName;
        // if (key === 'unit') {
        //     return;
        // }
        // const oldFlag = this.isValidData;
        // this.isValidData = false;
        const newControl = this.newControl;
        const val = newControl[key];
        if (val == null || val === '') {
            return true;
        }

        // const numStr = value + '';
        // if (isNaN(val)) {
        //     message.error('当前输入框中存在非法字符!')
        //     .then(() => {
        //         const onFocus = this.refContainer[key].current.onFocus;
        //         if (typeof onFocus === 'function') {
        //             onFocus();
        //         }
        //     });

        //     return false;
        // }

        let value: any = Number.parseFloat(val);
        if (key === 'minValue') {
            if (typeof newControl.maxValue === 'number') {
                if (newControl.maxValue < value) {
                    message.error('最小值不能大于最大值!')
                    .then(() => {
                        const onFocus = this.refContainer[key].current.onFocus;
                        if (typeof onFocus === 'function') {
                            onFocus();
                        }
                    });
                    return false;
                }
            }
        } else if (typeof newControl.minValue === 'number') {
            if (newControl.minValue > value) {
                message.error('最大值不能小于最小值!')
                .then(() => {
                    const onFocus = this.refContainer[key].current.onFocus;
                    if (typeof onFocus === 'function') {
                        onFocus();
                    }
                });
                return false;
            }
        }
        // this.isValidData = oldFlag && true;

        // if (val == null) {
        //     value = '';
        // }
        newControl[key] = value;
        return true;
    }

    private onChange = (value: any, name: string): void => {
        if (name === 'forceValidate') {
            if (value === true) {
                const messageOptions = {cancelDefault: true};
                layer.confirm('设置强制校验后，单位属性将失效，是否确认设值？', messageOptions).then((resolve) => {
                    this.newControl[name] = value;
                    this.setState({});
                }, (reject) => {
                    // refresh to remove tick
                    this.setState({});
                });
            } else {
                if (value === false && this.newControl[name] === true) {
                    // only when T -> F
                    this.newControl[name] = value;
                }

                this.setState({});
            }

        } else {
            if ('resetSourceBind' !== name) {
                if ('externalDataBind' !== name) {
                    this.newControl[name] = value;
                } else {
                    this.dataBind = value;
                }
            } else {
                this.resetSourceBind = true;
            }
        }
    }

    private displayTypeChange = (): void => {
        this.displayType = !this.displayType;
        if (this.displayType) {
            this.newControl.newControlDisplayType = NewControlContentSecretType.AllSecret;
        } else {
            this.newControl.newControlDisplayType = NewControlContentSecretType.DontSecret;
        }
    }
}
