import { ISerialTableObj } from '../serialize/serialInterface';
import { ICellSpanInfo, IExcel } from './IExcel';
import ExcelJS from 'exceljs';
import ExcelTableRow from './ExcelTableRow';
import { transDocxLengthToMM } from '../serialize/serializer';

export default class ExcelTable implements IExcel {
    private spanInfos: ICellSpanInfo[];
    constructor(private table: ISerialTableObj) {
        this.spanInfos = [];
    }

    public buildTo(sheet: ExcelJS.Worksheet): ExcelTable {
        // 需要记录上一行的单元格合并信息，过滤纵向合并
        const prevRowSpans: ICellSpanInfo[] = [];
        let rowIndex = 1;
        for (const tableRow of this.table.children) {
            const row = sheet.getRow(rowIndex);
            // 过滤已经结束的纵向合并信息
            for (let i = prevRowSpans.length - 1; i >= 0; i--) {
                const curPreSpan = prevRowSpans[i];
                if (curPreSpan.startRow + curPreSpan.rowSpan - 1 < rowIndex) {
                    prevRowSpans.splice(i, 1);
                }
            }

            const rowBuilder = new ExcelTableRow(tableRow, prevRowSpans).buildTo(row);

            // store row spanInfo
            for (const span of rowBuilder.CellSpanInfo) {
                const newSpan = {
                    ...span,
                    startRow: rowIndex,
                };
                this.spanInfos.push(newSpan);
                prevRowSpans.push(newSpan);
            }
            rowIndex++;
        }

        for (const span of this.spanInfos) {
            const { colSpan, rowSpan, startCol, startRow } = span;
            sheet.mergeCells(startRow, startCol, startRow + rowSpan - 1, startCol + colSpan - 1);
        }
        // 添加表格列宽, 列宽单位为1/10英寸(1单位为2.54mm)
        if (this.table.columnWidths) {
            sheet.columns = [...this.table.columnWidths.map((width) => {
                return {
                    width: transDocxLengthToMM(width) / 2.54 * 1.3, // 1.3为抵消列宽折扣
                };
            })];
        }
        return this;
    }

}
