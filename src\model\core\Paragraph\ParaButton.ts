import { DEFAULT_BUTTON_PROPS, IParaButtonProp, NewControlType, ResultType } from "@/common/commonDefines";
import TextProperty from "../TextProperty";
import { ParaElementBase } from "./ParaElementBase";
import { ParaElementType } from "./ParagraphContent";
import { measure } from "../util";

export default class ParaButton extends ParaElementBase {
    public portion: any;
    public name: string;
    public height: number;
    private color: string;
    private bPrint: boolean;
    
    constructor(portion: any, props?: IParaButtonProp) {
        super();
        this.type = ParaElementType.ParaButton;
        this.portion = portion;
        if (props) {
            this.content = props.content;
            this.color = props.color;
            this.bPrint = props.bPrint;
            this.name = props.name;
            this.measureContent();
        }
    }

    public copy(): ParaButton {
        const button = new ParaButton(this.portion);
        button.bPrint = this.bPrint;
        button.color = this.color;
        button.content = this.content;
        button.width = button.widthVisible = this.width;
        button.height = this.height;
        button.name = this.name;
        button.bPrint = this.bPrint;
        button.bVisible = this.bVisible;
        button.positionX = this.positionX;
        button.positionY = this.positionY;
        return button;
    }

    public getButtonProps(): IParaButtonProp {
        return {
            color: this.color,
            name: this.name,
            bPrint: this.bPrint,
            content: this.content
        }
    }

    public getStructType(): number {
        return NewControlType.Button;
    }

    public setButtonProps(props: IParaButtonProp): number {
        let result = ResultType.UnEdited;
        // result = this.setName(props.name) && result;
        result = this.setPrint(props.bPrint) && result;
        result = this.setColor(props.color) && result;
        const res = this.setText(props.content);
        if (res === ResultType.Success) {
            result = ResultType.Refresh;
        }

        return result;
    }

    public setName(name: string): number {
        if (name == null || name === this.name) {
            return ResultType.UnEdited;
        }
        this.name = name;
        return ResultType.Success;
    }

    public setPrint(bPrint: boolean): number {
        if (bPrint == null || bPrint === this.bPrint) {
            return ResultType.UnEdited;
        }
        this.bPrint = bPrint;
        return ResultType.Success;
    }

    public getPrintSelected(): boolean {
        return this.bPrint === true;
    }

    public getDocumentSectionType(): number {
        return this.portion.paragraph.getParent().getDocumentSectionType();
    }

    public setColor(color: string): number {
        if (color == null || color === this.color) {
            return ResultType.UnEdited;
        }
        this.color = color;
        return ResultType.Success;
    }

    public setText(content: string): number {
        if (content == null || content === this.content) {
            return ResultType.UnEdited;
        }
        this.content = content;
        this.measureContent();
        return ResultType.Success;
    }

    public getColor(): string {
        return this.color;
    }

    public measure(textPr: TextProperty, text?: string): number {
        return this.height;
    }

    public isButton(): boolean {
        return true;
    }

    public delete(): boolean {
        const index = this.portion.content.findIndex((item) => item === this);
        if (index === -1) {
            return false;
        }
        this.portion.selectTopPos();

        this.portion.portionContentPos = index + 1;
    }

    private measureContent(): boolean {
        const content = this.content;
        if (!content) {
            return false;
        }
        const rects = measure(content, {fontFamily: DEFAULT_BUTTON_PROPS.fontFamily, fontSize: DEFAULT_BUTTON_PROPS.fontSize});
        let maxWidth: number = 0;
        let maxHeight: number = 0;
        rects.forEach((rect) => {
            const {width, height} = rect;
            maxWidth += width;
            if (height > maxHeight) {
                maxHeight = height;
            }
        });
        this.width = this.widthVisible = maxWidth + DEFAULT_BUTTON_PROPS.paddingLeft + DEFAULT_BUTTON_PROPS.paddingRight;
        this.height = maxHeight + DEFAULT_BUTTON_PROPS.paddingBottom + DEFAULT_BUTTON_PROPS.paddingTop;
        return true;
    }
}
