import * as React from 'react';
import { DocumentCore, IDrawSelectionsCell } from '../../../../model/DocumentCore';
import { CodeValueItem, ViewModeType, INISProperty } from '../../../../common/commonDefines';
import retrieve from '../../../../common/Retrieve';
import Checkbox from '../../ui/CheckboxItem';
import Input from '../../ui/Input';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../../../common/GlobalEvent';
import Radiobox from '../../ui/RadioboxItem';
import { TableCell } from '../../../../model/core/Table/TableCell';
import Paragraph from '../../../../model/core/Paragraph';
import ParaPortion from '../../../../model/core/Paragraph/ParaPortion';

interface INISTableCellListBoxProps {
    documentCore: DocumentCore;
    nisProperty: INISProperty;
    scale?: number;
    closeNewComboxList?: () => void;
    refresh?: (bClose?: boolean) => void;
    position?: {left: number, top: number, width: number};
    bFromEnter?: boolean;
    // lineHeight?: number;
    bound?: IDrawSelectionsCell;
}

interface INISTableCellListBoxState {
    curIndex?: number;      // 当前选中item索引
    bRefresh: boolean;
    // curRadioIndex?: number; // which radio is selected
}

export class NISTableCellListBox extends React.Component<INISTableCellListBoxProps, INISTableCellListBoxState> {
    // private bSelect: boolean;
    private selectItemsPos: Set<string>; // the items that are of checked value(may not show)
    private bMulti: boolean;
    private newControlItems: CodeValueItem[];
    private nisItems: CodeValueItem[];
    private searchItems: CodeValueItem[];
    // private sortListItems: CodeValueItem[];
    private keyword: string;
    private unSearchKeyword: string;
    private searchTime: any;
    private inputRef: any;
    private bSearch: boolean;
    private documentCore: DocumentCore;
    private nisProperty: INISProperty;
    private containerRef: any;
    private _tabIndex: number;
    private top: number;
    // private radioSort: boolean; // whether to sort radio list
    private curNISCell: TableCell;
    private _dblTimer: any; // 用于双击控制的标记
    private colWidth: number; // 多列时每列的宽度

    constructor(props: any) {
        super(props);
        this.documentCore = props.documentCore;
        const nisProperty = this.nisProperty = (props.nisProperty as INISProperty);
        this.state = {
            curIndex: -1,
            bRefresh: false,
            // curRadioIndex: -1,
        };
        try {
            const curNISCell = this.curNISCell = this.documentCore.getCurNISCell();
            // console.log(curNISCell)
            let bChanged: boolean;
            if (curNISCell && typeof curNISCell.updateSelectItemByText === 'function') {
                try {
                    bChanged = curNISCell.updateSelectItemByText();
                } catch (error) {
                    console.log(error);
                }
            }
            // this.bSelect = false;
            this.selectItemsPos = new Set();
            let list;
            if (bChanged) {
                list = curNISCell.getItemList()
                .map((item) => {
                    return {...item};
                });
            } else {
                list = nisProperty.items;
            }
            // this.bMulti = false;
            // this.searchItems = this.newControlItems = list;
            this.searchItems = this.nisItems = list;
            this.inputRef = React.createRef();
            this.containerRef = React.createRef();
            this.bSearch = this.nisProperty.bRetrieve;
            this._tabIndex = 1;
            // TODO: for now
            this.bMulti = nisProperty.bCheckMultiple; // !curNISCell.isListCell();
            // this.radioSort = true;
            this.init();
        } catch (error) {
            console.log(error);
        }

    }

    public render(): any {
        const position = this.props.position;
        let left: number;
        let top: number;
        let width: number;
        if (position) {
            left = position.left;
            top = position.top;
            width = position.width;
        } else {
            try {
                const scale = this.props.scale;
                const documentCore = this.documentCore;
                // const bounds = documentCore.getNewControlsFocusBounds();
                // const bounds = documentCore.getNISCellBounds();
                // const lastLine = bounds.bounds[bounds.bounds.length - 1];
                const lastLine = this.props.bound; // bounds[0];
                const viewMode = documentCore.getViewMode();
                let subHeight = 0;
                if (viewMode === ViewModeType.WebView) {
                    const page = documentCore.getPagePositionInfo(0);
                    subHeight -= page.y;
                }

                left = lastLine.x * scale;
                top = (lastLine.y + lastLine.height + subHeight) * scale; // + 3;
                width = 150 > lastLine.width ? 150 : lastLine.width;
            } catch (error) {
                console.log(error);
            }
        }
        this.top = top;
        this.colWidth = (width - 70);
        return (
            <div className='new-cell-list' tabIndex={-1} ref={this.containerRef} style={{top, left, width}}>
                {this.renderSearchContent()}
                <div className='combox-item-panel'>
                    {this.renderListHeader()}
                    {this.renderTable()}
                </div>
                {this.renderBtns()}
            </div>
        );
    }

    public componentDidMount(): void {
        // const position = this.props.position;
        // if (position) {
        //     const dom = this.containerRef.current;
        //     let top = position.top;
        //     const maxHeight = document.firstElementChild.clientHeight - top;
        //     const currentHeight = dom.clientHeight;
        //     const subHeight = currentHeight - maxHeight;
        //     if (subHeight > 1) {
        //         top -= subHeight + 50;
        //         dom.style.top = top + 'px';
        //     }
        // }

        const dom = this.containerRef.current;
        if (!dom) {
            return;
        }

        try {
            let top = dom.getBoundingClientRect().top;
            const maxHeight = document.firstElementChild.clientHeight - top;
            const currentHeight = dom.clientHeight;
            const subHeight = currentHeight - maxHeight;
            if (subHeight > 1) {
                const bound = this.props.bound;
                const height = (bound && bound.height == null ? 0 : bound.height);
                // menu up
                top = this.top - currentHeight - height - 5;
                dom.style.top = top + 'px';
            }
            const lis = dom.querySelectorAll('.combox-item-list li[data-code]');
            if (lis && this.selectItemsPos.size > 0) {
                for (const li of lis) {
                    if (this.selectItemsPos.has(li.dataset.code)) {
                        const panel = li.parentElement.parentElement;
                        panel.scrollTop = li.offsetTop - panel.offsetTop;
                        break;
                    }
                }
            }
        } catch (error) {
            console.log(error);
        }
        dom.addEventListener('click', this.containerClick);
        // dom.addEventListener('keydown', this.keydown);

        // dom.addEventListener('focus', this.focus);
        if (this.bSearch) {
            setTimeout(() => {
                if ( this.inputRef.current ) {
                    this.inputRef.current.onFocus();
                }
            }, 300);
        } else if (this.props.bFromEnter) {
            this.mousedownEvent(null);
            // setTimeout(() => {
            //     dom.querySelector('li').focus();
            // }, 150);
        }
        // if (this.bMulti === false) {
        //     // radio list scroll to view
        //     setTimeout(() => {
        //         const radioBoxChecked = this.containerRef.current.querySelector('.radiobox-icon-checked');
        //         if (radioBoxChecked) {
        //             // outer scroll bar shouldnt move
        //             radioBoxChecked.scrollIntoView({behavior: 'smooth', block: 'end'});
        //             // radioBoxChecked.scrollTo(0, 0)
        //         }
        //     }, 110);
        // }
    }

    public componentWillUnmount(): void {
        const dom = this.containerRef.current;
        dom.removeEventListener('click', this.containerClick);
        // dom.removeEventListener('keydown', this.keydown);
        gEvent.deleteEvent(this.documentCore.getCurrentId(), gEventName.Mouseup, this.mousedownEvent);
        // dom.removeEventListener('focus', this.focus);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: INISTableCellListBoxProps): void {
        const nextPropsNisProperty = nextProps.nisProperty ;
        this.searchItems = this.nisItems = nextPropsNisProperty.items;
        this.bSearch = nextPropsNisProperty.bRetrieve;
        this.bMulti = nextPropsNisProperty.bCheckMultiple; // !this.curNISCell.isListCell();
        // if (this.bMulti === false) {
        //     this.radioSort = true;
        // }
        this.reset();
    }

    // private keydown = (e: any): void => {
    //     const code = e.keyCode;
    //     let index: number;
    //     switch (code) {
    //         case 13: {
    //             const searchItems = this.sortListItems;
    //             if (this.bMulti) {
    //                 if (this.selectItemsPos.size === 0) {
    //                     this.selectItemsPos.add(searchItems[0].code);
    //                 }
    //                 this.confirm();
    //                 // if (this.selectItemsPos.has(code)) {
    //                 //     this.selectItemsPos.delete(code);
    //                 // } else {
    //                 //     this.selectItemsPos.add(code);
    //                 // }
    //             } else {
    //                 this.onClick(searchItems[this._tabIndex - 1].code);
    //             }
    //             break;
    //         }
    //         case 9: {
    //             this.close();
    //             const props = this.props;
    //             const res = props.documentCore.jumpToNextNewControl(props.newControlPropety.newControlName);
    //             if (res === true) {
    //                 props.refresh();
    //             }
    //             break;
    //         }
    //         case 38: {
    //             index = this._tabIndex;
    //             if (index > 1) {
    //                 this._tabIndex = --index;
    //             }

    //             break;
    //         }
    //         case 40: {
    //             index = this._tabIndex;
    //             const length = this.newControlItems.length;
    //             if (index < length ) {
    //                 this._tabIndex = ++index;
    //             }
    //             break;
    //         }
    //     }
    //     // if (code === 13) {
    //     //     bStop = true;
    //     // }
    //     // else if (code === 9) {
    //     //     const li = this.containerRef.current.querySelector(`li:nth-of-type(${++this._tabIndex})`);
    //     //     if (li) {
    //     //         li.focus();
    //     //     }

    //     //     bStop = true;
    //     // }
    //     if (index !== undefined) {
    //         const li = this.containerRef.current.querySelector(`li:nth-of-type(${index})`);
    //         if (li) {
    //             li.focus();
    //         }
    //     }
    //     e.preventDefault();
    //     e.stopPropagation();
    // }

    private containerClick = (e: any): void => {
        // if (this.bMulti === false) {
        //     this.radioSort = false;
        // }
        const target = e.target;
        const tagName = target.nodeName;
        // if (tagName === 'LI') {
        //     const code = target.getAttribute('data-code');
        //     if (!code) {
        //         return;
        //     }
        //     this.onClick(code);
        // } else {
        //     this.focusLiItem(target);
        //     if (tagName === 'LABEL') {
        //         const index = target.getAttribute('data-index');
        //         if (!index) {
        //             return;
        //         }
        //         switch (index) {
        //             case '0': {
        //                 this.close();
        //                 break;
        //             }
        //             case '1': {
        //                 this.setReset();
        //                 break;
        //             }
        //             case '2': {
        //                 this.confirm();
        //                 break;
        //             }
        //         }
        //     }
        // }
        if (tagName === 'LABEL') {
            const index = target.getAttribute('data-index');
            if (!index) {
                return;
            }
            switch (index) {
                case '0': {
                    this.close();
                    break;
                }
                case '1': {
                    this.setReset();
                    break;
                }
                // case '2': {
                //     this.confirm();
                //     break;
                // }
            }
        }
        e.stopPropagation();
    }

    private focusLiItem(target: HTMLElement): HTMLElement {
        if (!target) {
            return;
        }
        if (target.nodeName === 'LI') {
            this._tabIndex = +target.getAttribute('tabIndex') + 1;
            return target;
        }

        return this.focusLiItem(target.parentNode as any);
    }

    private init(): void {
        this.reset();
        gEvent.addEvent(this.documentCore.getCurrentId(), gEventName.Mouseup, this.mousedownEvent);
    }

    private mousedownEvent = (e: any): void => {
        setTimeout(() => {
            if (!this.searchItems || this.searchItems.length === 0) {
                return;
            }
            const dom = this.containerRef.current;
            if (dom) {
                this._tabIndex = 1;
                // dom.querySelector('li').focus();
            }
        });
    }

    // private test(): void {
    //     const arrs = ['是的访问法个', '的佛那个人购买费共计', '低分狗溶剂法狗儿烹饪机构的交付给', '的方便么卤肉卷偶觉得发给我京东方',
    //     'fgml的烦恼够味儿解耦股么地方个人个', '么法平均打排位非贫困的分配给，二等分更肉片京东方', '人提供巨额融合柔而佛光和偶然间鹅肉感觉',
    //     '地方个人偶尔加工恶如，我的狗耳机而孤独感解耦'];
    //     const len = arrs.length;
    //     let arrIndex: number = 0;
    //     const datas: CodeValueItem[] = [];
    //     for (let index = 0; index < 1100; index++) {
    //         datas.push(new CodeValueItem(arrs[arrIndex++] + index, index.toString(), false));
    //         if (arrIndex === len) {
    //             arrIndex = 0;
    //         }
    //     }
    //     retrieve.search('s', 'a');
    //     this.searchItems = this.newControlItems = datas;
    // }

    private handleSearch = (value: string, name: string, e: any): void => {
        // if (this.bMulti === false) {
        //     this.radioSort = true;
        // }
        if (this.handleKeyDown(e)) {
            return;
        }
        const keyword = value || '';
        // if (/[\u4E00-\u9FA5]/.test(keyword)) {
        //     this.keyword = '';
        //     setTimeout(() => {
        //         e.target.value = '';
        //     });
        //     // this.setState({});
        //     return;
        // }
        this.keyword = keyword.toUpperCase();
        this.setState({bRefresh: !this.state.bRefresh});
        clearTimeout(this.searchTime);
        this.searchTime = setTimeout(() => {
            this.search();
        }, 50);
    }

    private handleFocus(): void {
        if (!this.inputRef || !this.inputRef.current) {
            return;
        }
        setTimeout(() => {
            this.inputRef.current.onFocus();
        }, 50);
    }

    private handleKeyDown = (e: any): boolean => {
        if (e.keyCode === 13) {
            this.confirm();
            e.stopPropagation();
            return true;
        }
        return false;
    }

    private async search(): Promise<void> {
        // const list = this.newControlItems;
        const list = this.nisItems;
        if (!list || list.length === 0) {
            return;
        }

        if (!this.keyword) {
            // this.searchItems = this.newControlItems;
            this.searchItems = this.nisItems;
            // this.selectItemsPos.clear();
            // this.bSelect = false;
            this.setState({bRefresh: !this.state.bRefresh});
            return;
        }

        if (this.unSearchKeyword && this.keyword.indexOf(this.unSearchKeyword) === 0) {
            return;
        }

        const items = this.searchItems = [];
        const keyword = this.keyword;
        for (const item of list) {
            const res = await retrieve.search(item.code, keyword);
            if (res.bSuccess === true) {
                items.push(item);
            }
        }

        if (items.length === 0) {
            this.unSearchKeyword = this.keyword;
            // this.selectItemsPos.clear();
            // this.bSelect = true;
        } else {
            items.sort((a, b) => {
                return a.code.length - b.code.length;
            });
            // console.log(items)
            // if (this.bMulti === false) {
            //     this.selectItemsPos.clear();
            //     this.selectItemsPos.add(items[0].code);
            // }
            // this.bSelect = true;
            this.unSearchKeyword = undefined;
            // this.setSelect(0);
        }
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private renderBtns(): any {
        // return (
        //     <div className='list-button'>
        //         <label onClick={this.setReset} data-index={1}>清空</label>
        //         <label onClick={this.close} data-index={0}>取消</label>
        //         <label onClick={this.confirm} data-index={2}>确认</label>
        //     </div>
        // );
        if (this.bMulti === false) {
            return (
                <div className='list-button'>
                    <label onClick={this.setReset} data-index={1}>清空</label>
                    <label onClick={this.close} data-index={0} style={{color: 'inherit'}}>取消</label>
                </div>
            );
        }
        return (
            <div className='list-button'>
                <label onClick={this.setReset} data-index={1}>清空</label>
                <label onClick={this.close} data-index={0}>取消</label>
                {/* <label onClick={this.confirm} data-index={2}>确认</label> */}
            </div>
        );
    }

    private renderSearchContent(): any {
        if (this.bSearch !== true) {
            return null;
        }
        return (
            <div className='search-container'>
                <Input
                    ref={this.inputRef}
                    value={this.keyword}
                    // onKeyDown={this.handleKeyDown}
                    onChange={this.handleSearch}
                />
            </div>
        );
    }

    private renderListHeader(): any {
        if (this.nisProperty.bShowCodeAndValue !== true || !this.nisItems.length) {
            return null;
        }

        let label = this.nisProperty.codeLabel;
        let value = this.nisProperty.valueLabel;
        if (label === '' && value === '') {
            return null;
        }

        if (label == null) {
            label = '名称';
        }

        if (value == null) {
            value = '值';
        }
        const mulStyle = {
            width: this.colWidth / 2 + 'px'
        };
        return (
            <React.Fragment>
                <div className='list-header'>
                    <li>
                        <label/>
                        <span className='mul-col' style={mulStyle}>{label}</span>
                        <span className='mul-col' style={mulStyle}>{value}</span>
                    </li>
                </div>
            </React.Fragment>
        );
    }

    private renderTable(): any {
        const list = this.searchItems;
        if ( null == list || 0 === list.length ) {
            return null;
        } else {
            return (
                <div className='combox-item-list'>
                    {this.renderList()}
                </div>
            );
        }
    }

    private renderList(): any {
        const list = this.searchItems;
        if (!list || 0 === list.length ) {
            return null;
        }
        const bMulti = this.bMulti;
        // this.selectItemsPos.clear();
        const selectItemsPos = this.selectItemsPos; // the items that are really checked(may not show)
        // console.log(selectItemsPos)
        // let sourceList = this.sortListItems;
        // if (sourceList.length > 0) {
        //     sourceList = this.sortListItems = this.sortList(list.slice(), selectItemsPos);
        // }
        // if (!bMulti) {
        //     sourceList = this.sortListItems = this.sortList(list.slice(), selectItemsPos);
        // } else {
        //     sourceList = list;
        // }
        // console.log(sourceList) // sourceList: the list to show in dropdown
        return list.map((item, index) => {
            const bSelect = selectItemsPos.has(item.code);
            if (bMulti) {
                return this.renderMultiLabel(item, bSelect, index);
            } else {
                return this.renderLabel(item, bSelect, index);
            }
        });
    }

    private renderMultiLabel(item: CodeValueItem, bChecked: boolean, index: number): any {
        return (
            <li key={item.code} data-code={item.code} tabIndex={index}>
                <Checkbox
                    value={bChecked}
                    name={item.code}
                    onChange={this.onChange}
                >
                    {this.renderLableText(item)}
                </Checkbox>
            </li>
        );
    }

    private renderLableText(item: CodeValueItem): any {
        if (this.nisProperty.bShowCodeAndValue !== true) {
            // return item.code;
            return <span className='one-col' style={{width: this.colWidth + 'px'}}>{item.code}</span>;
        }

        // return (
        //     <React.Fragment>
        //         <span>{item.code} </span>
        //         <span>  {item.value}</span>
        //     </React.Fragment>
        // );
        const mulStyle = {
            width: this.colWidth / 2 + 'px'
        };
        return (
            <React.Fragment>
                <span className='mul-col' style={mulStyle}>{item.code}</span>
                <span className='mul-col' style={mulStyle}>{item.value}</span>
            </React.Fragment>
        );
    }

    private renderLabel(item: CodeValueItem, bChecked: boolean, index: number): any {
        return (
            // <li key={item.code} className={bChecked ? 'active' : ''} data-code={item.code} tabIndex={index}>
            //     {item.code}
            // </li>
            <li key={item.code} data-code={item.code} tabIndex={index}>
                <Radiobox
                    value={bChecked}
                    name={item.code}
                    onChange={this.onRadioChange}
                    index={index}
                    // curIndex={this.state.curRadioIndex}
                >
                    {this.renderLableText(item)}
                </Radiobox>
            </li>
        );
    }

    private sortList(items: CodeValueItem[], selectedItems: Set<string>): CodeValueItem[] {
        if (selectedItems.size === 0) {
            return items;
        }

        let arrs = [];
        for (const code of selectedItems) {
            const index = items.findIndex((item) => item.code === code);
            if (index !== -1) {
                arrs.push(items.splice(index, 1)[0]);
            }
        }
        arrs = arrs.concat(items);
        return arrs;
    }

    private onChange = (value: boolean, name: string, e: any, bRadioIndex?: number): void => {
        if (bRadioIndex == null) {
            if (!this._dblTimer) {
                if (value === true) {
                    this.selectItemsPos.add(name);
                } else {
                    this.selectItemsPos.delete(name);
                }
                // tslint:disable-next-line: no-unused-expression
                if (this.getSelectedNISCell()) {
                    this.props.refresh(false);
                }
                this._dblTimer = setTimeout(() => {
                    this._dblTimer = null;
                }, 200);
            } else {
                clearTimeout(this._dblTimer);
                this._dblTimer = null;
                const delayFunc = async () => {
                    await Promise.resolve(true);
                    this.confirm();
                };
                delayFunc();
            }
        } else {
            if (value === true) {
                this.selectItemsPos.clear();
                this.selectItemsPos.add(name);
            } else {
                // if (this.selectItemsPos.size > 0) {
                //     this.selectItemsPos.clear();
                // }
            }
            // console.log(this.selectItemsPos)
            // this.setState({curRadioIndex: bRadioIndex})
            // need refresh, as to uncheck the potentially checked list
            this.setState({});
        }
    }

    private onRadioChange = (value: boolean, name: string, e: any, bRadioIndex?: number): void => {
        this.onClick(name);
    }

    private onClick = (code: string): void => {
        this.selectItemsPos.clear();
        this.selectItemsPos.add(code);
        this.confirm();
        // this.getSelectdNewControl();
    }

    // private getSelectdNewControl(): boolean {
    //     const { documentCore, newControlPropety } = this.props;
    //     const list = this.searchItems;
    //     if ( null == list || 0 === list.length ) {
    //         return;
    //     }

    //     const pos = [];
    //     const sourceList = this.newControlItems;
    //     // const bSmame = sourceList === list;
    //     this.selectItemsPos.forEach((value) => {
    //         const index = sourceList.findIndex((item) => item.code === value);
    //         pos.push(index);
    //     });
    //     // if (pos.length === 0) {
    //     //     return;
    //     // }
    //     // if (this.bMulti === false) {
    //     //     this.selectItemsPos.clear();
    //     // }
    //     // const newControl = documentCore.getNewControlByName(newControlPropety.newControlName);
    //     documentCore.setNewControlListItems(newControlPropety.newControlName, pos);
    //     return true;
    // }

    private getSelectedNISCell(): boolean {
        const { documentCore, nisProperty } = this.props;
        const list = this.searchItems;
        if ( null == list || 0 === list.length ) {
            return;
        }

        const pos = [];
        const sourceList = this.nisItems;
        // const bSmame = sourceList === list;
        this.selectItemsPos.forEach((value) => {
            const index = sourceList.findIndex((item) => item.code === value);
            pos.push(index);
        });
        // if (pos.length === 0) {
        //     return;
        // }
        // if (this.bMulti === false) {
        //     this.selectItemsPos.clear();
        // }
        // const newControl = documentCore.getNewControlByName(newControlPropety.newControlName);
        // documentCore.setNewControlListItems(newControlPropety.newControlName, pos);
        documentCore.setNISCellListItems(pos);
        return true;
    }

    private setReset = (): void => {
        this.keyword = '';
        this.searchItems = this.newControlItems;
        this.selectItemsPos.clear();
        this.resetSelectItems();
        // const newControl = this.documentCore.resetNewControlSelectItem(this.newControlPropety.newControlName);
        // newControl.resetSelectItems();
        // this.setState({bRefresh: !this.state.bRefresh});
        this.close();
        this.props.refresh();
    }

    private resetSelectItems(): void {
        const items = this.nisProperty.items;
        if ( null != items && 0 < items.length ) {
            items.forEach((item) => {
                item.bSelect = false;
            });
        }

        this.documentCore.setCellText('');

        // if (this.curNISCell != null) {
        //     // keep the first para's props and textProps
        //     let para = this.curNISCell.getContent()[0];
        //     let textProp = null;
        //     let paraProp = null;
        //     if (para != null && para instanceof Paragraph) {
        //         textProp = para.getDirectTextProperty().copy();
        //         paraProp = para.getParagraphProperty().copy();
        //     }

        //     this.curNISCell.content.clearContent();

        //     para = this.curNISCell.getContent()[0]; // empty para
        //     if (para != null && para instanceof Paragraph) {
        //         if (paraProp != null) {
        //             // para.setParagraphProperty(paraProp);
        //             para.paraProperty = paraProp;
        //         }
        //         const portion = new ParaPortion(para);
        //         if (textProp != null) {
        //             portion.textProperty = textProp;
        //         }
        //         portion.addText('');
        //         para.addToContent(0, portion);
        //     }

        //     this.documentCore.recalculate();
        // }
    }

    private reset = (bRefresh: boolean = false): void => {
        // const datas = this.newControlItems;
        const datas = this.nisItems;
        const selectItemsPos = this.selectItemsPos;
        selectItemsPos.clear();
        if ( null != datas && datas.length ) {
            datas.forEach((item) => {
                if (item.bSelect === true) {
                    selectItemsPos.add(item.code);
                }
            });
        }
        this.keyword = '';
        // this.searchItems = this.newControlItems;
        this.searchItems = this.nisItems;
        if (bRefresh === true) {
            this.setState({bRefresh: !this.state.bRefresh});
        }
        this.nisProperty.codeLabel = this.props.nisProperty?.codeLabel;
        this.nisProperty.valueLabel = this.props.nisProperty?.valueLabel;
    }

    private confirm = () => {
        // const res = this.getSelectdNewControl();
        const res = this.getSelectedNISCell();
        if (res === true) {
            this.props.refresh();
            return;
        }
        this.close();
    }

    private close = () => {
        // this.selectItemsPos.clear();
        this.props.closeNewComboxList();
    }

    // private renderListdd(): any {
    //     const list = this.searchItems;
    //     this.bMulti = ( NewControlType.MultiCombox === this.props.newControlPropety.newControlType
    //         || NewControlType.MultiListBox === this.props.newControlPropety.newControlType );
    //     if ( null == list || 0 === list.length ) {
    //         return ;
    //     }

    //     if ( false === this.bSelect ) {
    //         list.forEach((item, index) => {
    //             if ( true === item.bSelect ) {
    //                 this.selectItemsPos.add(index);
    //             }
    //         });
    //     }

    //     const newArry = [];
    //     if ( true === this.bMulti ) {
    //         for ( let index = 0; index < list.length; index++) {
    //             const item = list[index];
    //             const color = this.selectItemsPos.has(index) ? '#009FFF' : '' ;

    //             newArry.push((<tr className='table-tr' key={index} id={index.toString()} style={{background: color}}>
    //                         <td id={index.toString()}>
    //                             <input type='checkbox' id={index.toString()} checked={this.selectItemsPos.has(index)}
    // onChange={this.handleMouseDown.bind(this)}/>
    //                             <label htmlFor={index.toString()} id={index.toString()}>{item.code}</label>
    //                         </td>
    //                     </tr>));
    //         }
    //     } else {
    //         for ( let index = 0; index < list.length; index++) {
    //             const item = list[index];
    //             const color = this.selectItemsPos.has(index) ? '#009FFF' : '' ;

    //             newArry.push((<tr className='table-tr' key={index} id={index.toString()} style={{background: color}}>
    //                         <td id={index.toString()}>
    //                             <label id={index.toString()}>{item.code}</label>
    //                         </td>
    //                     </tr>));
    //         }
    //     }

    //     return newArry;
    // }

    // private handleMouseDown(type: string, e: any): void {
    //     if ( 'select-combox-item' === type ) {
    //         const table = document.getElementById('combox-item') as HTMLTableElement;
    //         if ( null != table && 0 < table.rows.length
    //             && ( 'TD' === e.target.tagName || 'INPUT' === e.target.tagName || 'LABEL' === e.target.tagName) ) {
    //             const curRowIndex = Number.parseInt(e.target.id, 0);
    //             this.bSelect = true;

    //             if ( null != curRowIndex ) {
    //                 if ( this.state.curIndex !== curRowIndex ) {
    //                     if ( -1 !== this.state.curIndex && table.rows[this.state.curIndex] ) {
    //                         table.rows[this.state.curIndex].bgColor = '';
    //                     }
    //                     table.rows[curRowIndex].bgColor = '#009FFF';

    //                     // const td = table.rows[curRowIndex].children[0];
    //                     // const input = td.firstElementChild as HTMLInputElement;

    //                     if ( false === this.bMulti ) {
    //                         this.selectItemsPos.clear();
    //                     }
    //                 }

    //                 if (this.bMulti === false) {
    //                     if (this.selectItemsPos.has(curRowIndex) === false) {
    //                         this.selectItemsPos.add(curRowIndex);
    //                     }

    //                     this.selectOneControl();
    //                     return;
    //                 }

    //                 this.handleFocus();

    //                 if ( false === this.selectItemsPos.has(curRowIndex) ) {
    //                     this.selectItemsPos.add(curRowIndex);
    //                 } else {
    //                     this.selectItemsPos.delete(curRowIndex);
    //                 }
    //                 this.setState({curIndex: curRowIndex});
    //             }
    //         }
    //     }
    // }

    // private handleButtonClick(e: any): void {
    //     const { documentCore, newControlPropety } = this.props;
    //     const newControl = documentCore.getNewControlByName(newControlPropety.newControlName);
    //     const list = this.searchItems;

    //     this.bSelect = false;

    //     if ( null == newControl ) {
    //         return ;
    //     }

    //     switch (e.target.innerHTML) {
    //         case '确定':
    //             if ( null == list || 0 === list.length ) {
    //                 break;
    //             }

    //             const pos = [];
    //             const sourceList = this.newControlItems;
    //             const bStart = sourceList.length !== list.length;
    //             this.selectItemsPos.forEach((value) => {
    //                 if (bStart) {
    //                     const data = list[value];
    //                     value = sourceList.findIndex((item) => item === data);
    //                 }
    //                 pos.push(value);
    //             });
    //             newControl.setNewControlListItems(pos);
    //             this.props.refresh();

    //         case '取消':
    //             break;

    //         case '复位':
    //             this.selectItemsPos.clear();
    //             newControl.resetSelectItems();
    //             this.props.refresh();
    //             break;

    //         default:
    //             break;
    //     }

    //     this.props.closeNewComboxList();
    // }
}
