{"version": 3, "file": "index.cjs.js", "sources": ["../src/timer.js", "../src/domHelpers.js", "../src/createGridComponent.js", "../src/VariableSizeGrid.js", "../src/createListComponent.js", "../src/VariableSizeList.js", "../src/FixedSizeGrid.js", "../src/FixedSizeList.js", "../src/shallowDiffers.js", "../src/areEqual.js", "../src/shouldComponentUpdate.js"], "sourcesContent": ["// @flow\n\n// Animation frame based implementation of setTimeout.\n// Inspired by <PERSON>, https://gist.github.com/joelambert/1002116#file-requesttimeout-js\n\nconst hasNativePerformanceNow =\n  typeof performance === 'object' && typeof performance.now === 'function';\n\nconst now = hasNativePerformanceNow\n  ? () => performance.now()\n  : () => Date.now();\n\nexport type TimeoutID = {|\n  id: AnimationFrameID,\n|};\n\nexport function cancelTimeout(timeoutID: TimeoutID) {\n  cancelAnimationFrame(timeoutID.id);\n}\n\nexport function requestTimeout(callback: Function, delay: number): TimeoutID {\n  const start = now();\n\n  function tick() {\n    if (now() - start >= delay) {\n      callback.call(null);\n    } else {\n      timeoutID.id = requestAnimationFrame(tick);\n    }\n  }\n\n  const timeoutID: TimeoutID = {\n    id: requestAnimationFrame(tick),\n  };\n\n  return timeoutID;\n}\n", "// @flow\n\nlet size: number = -1;\n\n// This utility copied from \"dom-helpers\" package.\nexport function getScrollbarSize(recalculate?: boolean = false): number {\n  if (size === -1 || recalculate) {\n    const div = document.createElement('div');\n    const style = div.style;\n    style.width = '50px';\n    style.height = '50px';\n    style.overflow = 'scroll';\n\n    ((document.body: any): HTMLBodyElement).appendChild(div);\n\n    size = div.offsetWidth - div.clientWidth;\n\n    ((document.body: any): HTMLBodyElement).removeChild(div);\n  }\n\n  return size;\n}\n\nexport type RTLOffsetType =\n  | 'negative'\n  | 'positive-descending'\n  | 'positive-ascending';\n\nlet cachedRTLResult: RTLOffsetType | null = null;\n\n// TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n// Chrome does not seem to adhere; its scrollLeft values are positive (measured relative to the left).\n// <PERSON><PERSON>'s elastic bounce makes detecting this even more complicated wrt potential false positives.\n// The safest way to check this is to intentionally set a negative offset,\n// and then verify that the subsequent \"scroll\" event matches the negative offset.\n// If it does not match, then we can assume a non-standard RTL scroll implementation.\nexport function getRTLOffsetType(recalculate?: boolean = false): RTLOffsetType {\n  if (cachedRTLResult === null || recalculate) {\n    const outerDiv = document.createElement('div');\n    const outerStyle = outerDiv.style;\n    outerStyle.width = '50px';\n    outerStyle.height = '50px';\n    outerStyle.overflow = 'scroll';\n    outerStyle.direction = 'rtl';\n\n    const innerDiv = document.createElement('div');\n    const innerStyle = innerDiv.style;\n    innerStyle.width = '100px';\n    innerStyle.height = '100px';\n\n    outerDiv.appendChild(innerDiv);\n\n    ((document.body: any): HTMLBodyElement).appendChild(outerDiv);\n\n    if (outerDiv.scrollLeft > 0) {\n      cachedRTLResult = 'positive-descending';\n    } else {\n      outerDiv.scrollLeft = 1;\n      if (outerDiv.scrollLeft === 0) {\n        cachedRTLResult = 'negative';\n      } else {\n        cachedRTLResult = 'positive-ascending';\n      }\n    }\n\n    ((document.body: any): HTMLBodyElement).removeChild(outerDiv);\n\n    return cachedRTLResult;\n  }\n\n  return cachedRTLResult;\n}\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getScrollbarSize, getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\ntype Direction = 'ltr' | 'rtl';\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n\ntype RenderComponentProps<T> = {|\n  columnIndex: number,\n  data: T,\n  isScrolling?: boolean,\n  rowIndex: number,\n  style: Object,\n|};\nexport type RenderComponent<T> = React$ComponentType<\n  $Shape<RenderComponentProps<T>>\n>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype OnItemsRenderedCallback = ({\n  overscanColumnStartIndex: number,\n  overscanColumnStopIndex: number,\n  overscanRowStartIndex: number,\n  overscanRowStopIndex: number,\n  visibleColumnStartIndex: number,\n  visibleColumnStopIndex: number,\n  visibleRowStartIndex: number,\n  visibleRowStopIndex: number,\n}) => void;\ntype OnScrollCallback = ({\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [key: string]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  columnCount: number,\n  columnWidth: itemSize,\n  direction: Direction,\n  height: number,\n  initialScrollLeft?: number,\n  initialScrollTop?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemData: T,\n  itemKey?: (params: {|\n    columnIndex: number,\n    data: T,\n    rowIndex: number,\n  |}) => any,\n  onItemsRendered?: OnItemsRenderedCallback,\n  onScroll?: OnScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanColumnCount?: number,\n  overscanColumnsCount?: number, // deprecated\n  overscanCount?: number, // deprecated\n  overscanRowCount?: number,\n  overscanRowsCount?: number, // deprecated\n  rowCount: number,\n  rowHeight: itemSize,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n|};\n\ntype getItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForItemAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any,\n  scrollbarSize: number\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = ({ columnIndex, data, rowIndex }) =>\n  `${rowIndex}:${columnIndex}`;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsOverscanCount = null;\nlet devWarningsOverscanRowsColumnsCount = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsOverscanCount = new WeakSet();\n    devWarningsOverscanRowsColumnsCount = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createGridComponent({\n  getColumnOffset,\n  getColumnStartIndexForOffset,\n  getColumnStopIndexForStartIndex,\n  getColumnWidth,\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n  getOffsetForColumnAndAlignment,\n  getOffsetForRowAndAlignment,\n  getRowHeight,\n  getRowOffset,\n  getRowStartIndexForOffset,\n  getRowStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getColumnOffset: getItemOffset,\n  getColumnStartIndexForOffset: GetStartIndexForOffset,\n  getColumnStopIndexForStartIndex: GetStopIndexForStartIndex,\n  getColumnWidth: getItemSize,\n  getEstimatedTotalHeight: getEstimatedTotalSize,\n  getEstimatedTotalWidth: getEstimatedTotalSize,\n  getOffsetForColumnAndAlignment: GetOffsetForItemAndAlignment,\n  getOffsetForRowAndAlignment: GetOffsetForItemAndAlignment,\n  getRowOffset: getItemOffset,\n  getRowHeight: getItemSize,\n  getRowStartIndexForOffset: GetStartIndexForOffset,\n  getRowStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class Grid<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n    _outerRef: ?HTMLDivElement;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      horizontalScrollDirection: 'forward',\n      scrollLeft:\n        typeof this.props.initialScrollLeft === 'number'\n          ? this.props.initialScrollLeft\n          : 0,\n      scrollTop:\n        typeof this.props.initialScrollTop === 'number'\n          ? this.props.initialScrollTop\n          : 0,\n      scrollUpdateWasRequested: false,\n      verticalScrollDirection: 'forward',\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo({\n      scrollLeft,\n      scrollTop,\n    }: {\n      scrollLeft: number,\n      scrollTop: number,\n    }): void {\n      if (scrollLeft !== undefined) {\n        scrollLeft = Math.max(0, scrollLeft);\n      }\n      if (scrollTop !== undefined) {\n        scrollTop = Math.max(0, scrollTop);\n      }\n\n      this.setState(prevState => {\n        if (scrollLeft === undefined) {\n          scrollLeft = prevState.scrollLeft;\n        }\n        if (scrollTop === undefined) {\n          scrollTop = prevState.scrollTop;\n        }\n\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          return null;\n        }\n\n        return {\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop,\n          scrollUpdateWasRequested: true,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem({\n      align = 'auto',\n      columnIndex,\n      rowIndex,\n    }: {\n      align: ScrollToAlign,\n      columnIndex?: number,\n      rowIndex?: number,\n    }): void {\n      const { columnCount, height, rowCount, width } = this.props;\n      const { scrollLeft, scrollTop } = this.state;\n      const scrollbarSize = getScrollbarSize();\n\n      if (columnIndex !== undefined) {\n        columnIndex = Math.max(0, Math.min(columnIndex, columnCount - 1));\n      }\n      if (rowIndex !== undefined) {\n        rowIndex = Math.max(0, Math.min(rowIndex, rowCount - 1));\n      }\n\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      // The scrollbar size should be considered when scrolling an item into view,\n      // to ensure it's fully visible.\n      // But we only need to account for its size when it's actually visible.\n      const horizontalScrollbarSize =\n        estimatedTotalWidth > width ? scrollbarSize : 0;\n      const verticalScrollbarSize =\n        estimatedTotalHeight > height ? scrollbarSize : 0;\n\n      this.scrollTo({\n        scrollLeft:\n          columnIndex !== undefined\n            ? getOffsetForColumnAndAlignment(\n                this.props,\n                columnIndex,\n                align,\n                scrollLeft,\n                this._instanceProps,\n                verticalScrollbarSize\n              )\n            : scrollLeft,\n        scrollTop:\n          rowIndex !== undefined\n            ? getOffsetForRowAndAlignment(\n                this.props,\n                rowIndex,\n                align,\n                scrollTop,\n                this._instanceProps,\n                horizontalScrollbarSize\n              )\n            : scrollTop,\n      });\n    }\n\n    componentDidMount() {\n      const { initialScrollLeft, initialScrollTop } = this.props;\n\n      if (this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (typeof initialScrollLeft === 'number') {\n          outerRef.scrollLeft = initialScrollLeft;\n        }\n        if (typeof initialScrollTop === 'number') {\n          outerRef.scrollTop = initialScrollTop;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction } = this.props;\n      const { scrollLeft, scrollTop, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // So we need to determine which browser behavior we're dealing with, and mimic it.\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              outerRef.scrollLeft = -scrollLeft;\n              break;\n            case 'positive-ascending':\n              outerRef.scrollLeft = scrollLeft;\n              break;\n            default:\n              const { clientWidth, scrollWidth } = outerRef;\n              outerRef.scrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        } else {\n          outerRef.scrollLeft = Math.max(0, scrollLeft);\n        }\n\n        outerRef.scrollTop = Math.max(0, scrollTop);\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        columnCount,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemData,\n        itemKey = defaultItemKey,\n        outerElementType,\n        outerTagName,\n        rowCount,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      const [\n        columnStartIndex,\n        columnStopIndex,\n      ] = this._getHorizontalRangeToRender();\n      const [rowStartIndex, rowStopIndex] = this._getVerticalRangeToRender();\n\n      const items = [];\n      if (columnCount > 0 && rowCount) {\n        for (\n          let rowIndex = rowStartIndex;\n          rowIndex <= rowStopIndex;\n          rowIndex++\n        ) {\n          for (\n            let columnIndex = columnStartIndex;\n            columnIndex <= columnStopIndex;\n            columnIndex++\n          ) {\n            items.push(\n              createElement(children, {\n                columnIndex,\n                data: itemData,\n                isScrolling: useIsScrolling ? isScrolling : undefined,\n                key: itemKey({ columnIndex, data: itemData, rowIndex }),\n                rowIndex,\n                style: this._getItemStyle(rowIndex, columnIndex),\n              })\n            );\n          }\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll: this._onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: estimatedTotalHeight,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: estimatedTotalWidth,\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanColumnStartIndex: number,\n      overscanColumnStopIndex: number,\n      overscanRowStartIndex: number,\n      overscanRowStopIndex: number,\n      visibleColumnStartIndex: number,\n      visibleColumnStopIndex: number,\n      visibleRowStartIndex: number,\n      visibleRowStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanColumnStartIndex: number,\n        overscanColumnStopIndex: number,\n        overscanRowStartIndex: number,\n        overscanRowStopIndex: number,\n        visibleColumnStartIndex: number,\n        visibleColumnStopIndex: number,\n        visibleRowStartIndex: number,\n        visibleRowStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): OnItemsRenderedCallback)({\n          overscanColumnStartIndex,\n          overscanColumnStopIndex,\n          overscanRowStartIndex,\n          overscanRowStopIndex,\n          visibleColumnStartIndex,\n          visibleColumnStopIndex,\n          visibleRowStartIndex,\n          visibleRowStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollLeft: number,\n      scrollTop: number,\n      horizontalScrollDirection: ScrollDirection,\n      verticalScrollDirection: ScrollDirection,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollLeft: number,\n        scrollTop: number,\n        horizontalScrollDirection: ScrollDirection,\n        verticalScrollDirection: ScrollDirection,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): OnScrollCallback)({\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          verticalScrollDirection,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      const { columnCount, onItemsRendered, onScroll, rowCount } = this.props;\n\n      if (typeof onItemsRendered === 'function') {\n        if (columnCount > 0 && rowCount > 0) {\n          const [\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n          ] = this._getHorizontalRangeToRender();\n          const [\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex,\n          ] = this._getVerticalRangeToRender();\n          this._callOnItemsRendered(\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex\n          );\n        }\n      }\n\n      if (typeof onScroll === 'function') {\n        const {\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          scrollUpdateWasRequested,\n          verticalScrollDirection,\n        } = this.state;\n        this._callOnScroll(\n          scrollLeft,\n          scrollTop,\n          horizontalScrollDirection,\n          verticalScrollDirection,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (rowIndex: number, columnIndex: number) => Object;\n    _getItemStyle = (rowIndex: number, columnIndex: number): Object => {\n      const { columnWidth, direction, rowHeight } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && columnWidth,\n        shouldResetStyleCacheOnItemSizeChange && direction,\n        shouldResetStyleCacheOnItemSizeChange && rowHeight\n      );\n\n      const key = `${rowIndex}:${columnIndex}`;\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(key)) {\n        style = itemStyleCache[key];\n      } else {\n        const offset = getColumnOffset(\n          this.props,\n          columnIndex,\n          this._instanceProps\n        );\n        const isRtl = direction === 'rtl';\n        itemStyleCache[key] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offset,\n          right: isRtl ? offset : undefined,\n          top: getRowOffset(this.props, rowIndex, this._instanceProps),\n          height: getRowHeight(this.props, rowIndex, this._instanceProps),\n          width: getColumnWidth(this.props, columnIndex, this._instanceProps),\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getHorizontalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanColumnCount,\n        overscanColumnsCount,\n        overscanCount,\n        rowCount,\n      } = this.props;\n      const { horizontalScrollDirection, isScrolling, scrollLeft } = this.state;\n\n      const overscanCountResolved: number =\n        overscanColumnCount || overscanColumnsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getColumnStartIndexForOffset(\n        this.props,\n        scrollLeft,\n        this._instanceProps\n      );\n      const stopIndex = getColumnStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollLeft,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || horizontalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || horizontalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(columnCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _getVerticalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanCount,\n        overscanRowCount,\n        overscanRowsCount,\n        rowCount,\n      } = this.props;\n      const { isScrolling, verticalScrollDirection, scrollTop } = this.state;\n\n      const overscanCountResolved: number =\n        overscanRowCount || overscanRowsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getRowStartIndexForOffset(\n        this.props,\n        scrollTop,\n        this._instanceProps\n      );\n      const stopIndex = getRowStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollTop,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || verticalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || verticalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(rowCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScroll = (event: ScrollEvent): void => {\n      const {\n        clientHeight,\n        clientWidth,\n        scrollLeft,\n        scrollTop,\n        scrollHeight,\n        scrollWidth,\n      } = event.currentTarget;\n      this.setState(prevState => {\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n        // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n        let calculatedScrollLeft = scrollLeft;\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              calculatedScrollLeft = -scrollLeft;\n              break;\n            case 'positive-descending':\n              calculatedScrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        calculatedScrollLeft = Math.max(\n          0,\n          Math.min(calculatedScrollLeft, scrollWidth - clientWidth)\n        );\n        const calculatedScrollTop = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: calculatedScrollLeft,\n          scrollTop: calculatedScrollTop,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1);\n      });\n    };\n  };\n}\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    innerTagName,\n    outerTagName,\n    overscanColumnsCount,\n    overscanCount,\n    overscanRowsCount,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof overscanCount === 'number') {\n      if (devWarningsOverscanCount && !devWarningsOverscanCount.has(instance)) {\n        devWarningsOverscanCount.add(instance);\n        console.warn(\n          'The overscanCount prop has been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (\n      typeof overscanColumnsCount === 'number' ||\n      typeof overscanRowsCount === 'number'\n    ) {\n      if (\n        devWarningsOverscanRowsColumnsCount &&\n        !devWarningsOverscanRowsColumnsCount.has(instance)\n      ) {\n        devWarningsOverscanRowsColumnsCount.add(instance);\n        console.warn(\n          'The overscanColumnsCount and overscanRowsCount props have been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    switch (direction) {\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    if (typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Grids must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    }\n\n    if (typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Grids must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createGridComponent from './createGridComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createGridComponent';\n\nconst DEFAULT_ESTIMATED_ITEM_SIZE = 50;\n\ntype VariableSizeProps = {|\n  estimatedColumnWidth: number,\n  estimatedRowHeight: number,\n  ...Props<any>,\n|};\n\ntype itemSizeGetter = (index: number) => number;\ntype ItemType = 'column' | 'row';\n\ntype ItemMetadata = {|\n  offset: number,\n  size: number,\n|};\ntype ItemMetadataMap = { [index: number]: ItemMetadata };\ntype InstanceProps = {|\n  columnMetadataMap: ItemMetadataMap,\n  estimatedColumnWidth: number,\n  estimatedRowHeight: number,\n  lastMeasuredColumnIndex: number,\n  lastMeasuredRowIndex: number,\n  rowMetadataMap: ItemMetadataMap,\n|};\n\nconst getEstimatedTotalHeight = (\n  { rowCount }: Props<any>,\n  { rowMetadataMap, estimatedRowHeight, lastMeasuredRowIndex }: InstanceProps\n) => {\n  let totalSizeOfMeasuredRows = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredRowIndex >= rowCount) {\n    lastMeasuredRowIndex = rowCount - 1;\n  }\n\n  if (lastMeasuredRowIndex >= 0) {\n    const itemMetadata = rowMetadataMap[lastMeasuredRowIndex];\n    totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = rowCount - lastMeasuredRowIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedRowHeight;\n\n  return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\n\nconst getEstimatedTotalWidth = (\n  { columnCount }: Props<any>,\n  {\n    columnMetadataMap,\n    estimatedColumnWidth,\n    lastMeasuredColumnIndex,\n  }: InstanceProps\n) => {\n  let totalSizeOfMeasuredRows = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredColumnIndex >= columnCount) {\n    lastMeasuredColumnIndex = columnCount - 1;\n  }\n\n  if (lastMeasuredColumnIndex >= 0) {\n    const itemMetadata = columnMetadataMap[lastMeasuredColumnIndex];\n    totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = columnCount - lastMeasuredColumnIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedColumnWidth;\n\n  return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\n\nconst getItemMetadata = (\n  itemType: ItemType,\n  props: Props<any>,\n  index: number,\n  instanceProps: InstanceProps\n): ItemMetadata => {\n  let itemMetadataMap, itemSize, lastMeasuredIndex;\n  if (itemType === 'column') {\n    itemMetadataMap = instanceProps.columnMetadataMap;\n    itemSize = ((props.columnWidth: any): itemSizeGetter);\n    lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n  } else {\n    itemMetadataMap = instanceProps.rowMetadataMap;\n    itemSize = ((props.rowHeight: any): itemSizeGetter);\n    lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n  }\n\n  if (index > lastMeasuredIndex) {\n    let offset = 0;\n    if (lastMeasuredIndex >= 0) {\n      const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n      offset = itemMetadata.offset + itemMetadata.size;\n    }\n\n    for (let i = lastMeasuredIndex + 1; i <= index; i++) {\n      let size = itemSize(i);\n\n      itemMetadataMap[i] = {\n        offset,\n        size,\n      };\n\n      offset += size;\n    }\n\n    if (itemType === 'column') {\n      instanceProps.lastMeasuredColumnIndex = index;\n    } else {\n      instanceProps.lastMeasuredRowIndex = index;\n    }\n  }\n\n  return itemMetadataMap[index];\n};\n\nconst findNearestItem = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  offset: number\n) => {\n  let itemMetadataMap, lastMeasuredIndex;\n  if (itemType === 'column') {\n    itemMetadataMap = instanceProps.columnMetadataMap;\n    lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n  } else {\n    itemMetadataMap = instanceProps.rowMetadataMap;\n    lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n  }\n\n  const lastMeasuredItemOffset =\n    lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n\n  if (lastMeasuredItemOffset >= offset) {\n    // If we've already measured items within this range just use a binary search as it's faster.\n    return findNearestItemBinarySearch(\n      itemType,\n      props,\n      instanceProps,\n      lastMeasuredIndex,\n      0,\n      offset\n    );\n  } else {\n    // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n    // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n    // The overall complexity for this approach is O(log n).\n    return findNearestItemExponentialSearch(\n      itemType,\n      props,\n      instanceProps,\n      Math.max(0, lastMeasuredIndex),\n      offset\n    );\n  }\n};\n\nconst findNearestItemBinarySearch = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  high: number,\n  low: number,\n  offset: number\n): number => {\n  while (low <= high) {\n    const middle = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemMetadata(\n      itemType,\n      props,\n      middle,\n      instanceProps\n    ).offset;\n\n    if (currentOffset === offset) {\n      return middle;\n    } else if (currentOffset < offset) {\n      low = middle + 1;\n    } else if (currentOffset > offset) {\n      high = middle - 1;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nconst findNearestItemExponentialSearch = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  index: number,\n  offset: number\n): number => {\n  const itemCount = itemType === 'column' ? props.columnCount : props.rowCount;\n  let interval = 1;\n\n  while (\n    index < itemCount &&\n    getItemMetadata(itemType, props, index, instanceProps).offset < offset\n  ) {\n    index += interval;\n    interval *= 2;\n  }\n\n  return findNearestItemBinarySearch(\n    itemType,\n    props,\n    instanceProps,\n    Math.min(index, itemCount - 1),\n    Math.floor(index / 2),\n    offset\n  );\n};\n\nconst getOffsetForIndexAndAlignment = (\n  itemType: ItemType,\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: InstanceProps,\n  scrollbarSize: number\n): number => {\n  const size = itemType === 'column' ? props.width : props.height;\n  const itemMetadata = getItemMetadata(itemType, props, index, instanceProps);\n\n  // Get estimated total size after ItemMetadata is computed,\n  // To ensure it reflects actual measurements instead of just estimates.\n  const estimatedTotalSize =\n    itemType === 'column'\n      ? getEstimatedTotalWidth(props, instanceProps)\n      : getEstimatedTotalHeight(props, instanceProps);\n\n  const maxOffset = Math.max(\n    0,\n    Math.min(estimatedTotalSize - size, itemMetadata.offset)\n  );\n  const minOffset = Math.max(\n    0,\n    itemMetadata.offset - size + scrollbarSize + itemMetadata.size\n  );\n\n  if (align === 'smart') {\n    if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n      align = 'auto';\n    } else {\n      align = 'center';\n    }\n  }\n\n  switch (align) {\n    case 'start':\n      return maxOffset;\n    case 'end':\n      return minOffset;\n    case 'center':\n      return Math.round(minOffset + (maxOffset - minOffset) / 2);\n    case 'auto':\n    default:\n      if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n        return scrollOffset;\n      } else if (minOffset > maxOffset) {\n        // Because we only take into account the scrollbar size when calculating minOffset\n        // this value can be larger than maxOffset when at the end of the list\n        return minOffset;\n      } else if (scrollOffset < minOffset) {\n        return minOffset;\n      } else {\n        return maxOffset;\n      }\n  }\n};\n\nconst VariableSizeGrid = createGridComponent({\n  getColumnOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata('column', props, index, instanceProps).offset,\n\n  getColumnStartIndexForOffset: (\n    props: Props<any>,\n    scrollLeft: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem('column', props, instanceProps, scrollLeft),\n\n  getColumnStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollLeft: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { columnCount, width } = props;\n\n    const itemMetadata = getItemMetadata(\n      'column',\n      props,\n      startIndex,\n      instanceProps\n    );\n    const maxOffset = scrollLeft + width;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < columnCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata('column', props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  getColumnWidth: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.columnMetadataMap[index].size,\n\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n\n  getOffsetForColumnAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number =>\n    getOffsetForIndexAndAlignment(\n      'column',\n      props,\n      index,\n      align,\n      scrollOffset,\n      instanceProps,\n      scrollbarSize\n    ),\n\n  getOffsetForRowAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number =>\n    getOffsetForIndexAndAlignment(\n      'row',\n      props,\n      index,\n      align,\n      scrollOffset,\n      instanceProps,\n      scrollbarSize\n    ),\n\n  getRowOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata('row', props, index, instanceProps).offset,\n\n  getRowHeight: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.rowMetadataMap[index].size,\n\n  getRowStartIndexForOffset: (\n    props: Props<any>,\n    scrollTop: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem('row', props, instanceProps, scrollTop),\n\n  getRowStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollTop: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { rowCount, height } = props;\n\n    const itemMetadata = getItemMetadata(\n      'row',\n      props,\n      startIndex,\n      instanceProps\n    );\n    const maxOffset = scrollTop + height;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < rowCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata('row', props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  initInstanceProps(props: Props<any>, instance: any): InstanceProps {\n    const {\n      estimatedColumnWidth,\n      estimatedRowHeight,\n    } = ((props: any): VariableSizeProps);\n\n    const instanceProps = {\n      columnMetadataMap: {},\n      estimatedColumnWidth: estimatedColumnWidth || DEFAULT_ESTIMATED_ITEM_SIZE,\n      estimatedRowHeight: estimatedRowHeight || DEFAULT_ESTIMATED_ITEM_SIZE,\n      lastMeasuredColumnIndex: -1,\n      lastMeasuredRowIndex: -1,\n      rowMetadataMap: {},\n    };\n\n    instance.resetAfterColumnIndex = (\n      columnIndex: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instance.resetAfterIndices({ columnIndex, shouldForceUpdate });\n    };\n\n    instance.resetAfterRowIndex = (\n      rowIndex: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instance.resetAfterIndices({ rowIndex, shouldForceUpdate });\n    };\n\n    instance.resetAfterIndices = ({\n      columnIndex,\n      rowIndex,\n      shouldForceUpdate = true,\n    }: {\n      columnIndex?: number,\n      rowIndex?: number,\n      shouldForceUpdate: boolean,\n    }) => {\n      if (typeof columnIndex === 'number') {\n        instanceProps.lastMeasuredColumnIndex = Math.min(\n          instanceProps.lastMeasuredColumnIndex,\n          columnIndex - 1\n        );\n      }\n      if (typeof rowIndex === 'number') {\n        instanceProps.lastMeasuredRowIndex = Math.min(\n          instanceProps.lastMeasuredRowIndex,\n          rowIndex - 1\n        );\n      }\n\n      // We could potentially optimize further by only evicting styles after this index,\n      // But since styles are only cached while scrolling is in progress-\n      // It seems an unnecessary optimization.\n      // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n      instance._getItemStyleCache(-1);\n\n      if (shouldForceUpdate) {\n        instance.forceUpdate();\n      }\n    };\n\n    return instanceProps;\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: false,\n\n  validateProps: ({ columnWidth, rowHeight }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof columnWidth !== 'function') {\n        throw Error(\n          'An invalid \"columnWidth\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${\n              columnWidth === null ? 'null' : typeof columnWidth\n            }\" was specified.`\n        );\n      } else if (typeof rowHeight !== 'function') {\n        throw Error(\n          'An invalid \"rowHeight\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${rowHeight === null ? 'null' : typeof rowHeight}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default VariableSizeGrid;\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n// TODO Deprecate directions \"horizontal\" and \"vertical\"\ntype Direction = 'ltr' | 'rtl' | 'horizontal' | 'vertical';\ntype Layout = 'horizontal' | 'vertical';\n\ntype RenderComponentProps<T> = {|\n  data: T,\n  index: number,\n  isScrolling?: boolean,\n  style: Object,\n|};\ntype RenderComponent<T> = React$ComponentType<$Shape<RenderComponentProps<T>>>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype onItemsRenderedCallback = ({\n  overscanStartIndex: number,\n  overscanStopIndex: number,\n  visibleStartIndex: number,\n  visibleStopIndex: number,\n}) => void;\ntype onScrollCallback = ({\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [index: number]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  direction: Direction,\n  height: number | string,\n  initialScrollOffset?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemCount: number,\n  itemData: T,\n  itemKey?: (index: number, data: T) => any,\n  itemSize: itemSize,\n  layout: Layout,\n  onItemsRendered?: onItemsRenderedCallback,\n  onScroll?: onScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanCount: number,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number | string,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n|};\n\ntype GetItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForIndexAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = (index: number, data: any) => index;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsDirection = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsDirection = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createListComponent({\n  getItemOffset,\n  getEstimatedTotalSize,\n  getItemSize,\n  getOffsetForIndexAndAlignment,\n  getStartIndexForOffset,\n  getStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getItemOffset: GetItemOffset,\n  getEstimatedTotalSize: GetEstimatedTotalSize,\n  getItemSize: GetItemSize,\n  getOffsetForIndexAndAlignment: GetOffsetForIndexAndAlignment,\n  getStartIndexForOffset: GetStartIndexForOffset,\n  getStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class List<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _outerRef: ?HTMLDivElement;\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      layout: 'vertical',\n      overscanCount: 2,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      scrollDirection: 'forward',\n      scrollOffset:\n        typeof this.props.initialScrollOffset === 'number'\n          ? this.props.initialScrollOffset\n          : 0,\n      scrollUpdateWasRequested: false,\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo(scrollOffset: number): void {\n      scrollOffset = Math.max(0, scrollOffset);\n\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollOffset) {\n          return null;\n        }\n        return {\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset: scrollOffset,\n          scrollUpdateWasRequested: true,\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem(index: number, align: ScrollToAlign = 'auto'): void {\n      const { itemCount } = this.props;\n      const { scrollOffset } = this.state;\n\n      index = Math.max(0, Math.min(index, itemCount - 1));\n\n      this.scrollTo(\n        getOffsetForIndexAndAlignment(\n          this.props,\n          index,\n          align,\n          scrollOffset,\n          this._instanceProps\n        )\n      );\n    }\n\n    componentDidMount() {\n      const { direction, initialScrollOffset, layout } = this.props;\n\n      if (typeof initialScrollOffset === 'number' && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          outerRef.scrollLeft = initialScrollOffset;\n        } else {\n          outerRef.scrollTop = initialScrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction, layout } = this.props;\n      const { scrollOffset, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          if (direction === 'rtl') {\n            // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n            // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n            // So we need to determine which browser behavior we're dealing with, and mimic it.\n            switch (getRTLOffsetType()) {\n              case 'negative':\n                outerRef.scrollLeft = -scrollOffset;\n                break;\n              case 'positive-ascending':\n                outerRef.scrollLeft = scrollOffset;\n                break;\n              default:\n                const { clientWidth, scrollWidth } = outerRef;\n                outerRef.scrollLeft = scrollWidth - clientWidth - scrollOffset;\n                break;\n            }\n          } else {\n            outerRef.scrollLeft = scrollOffset;\n          }\n        } else {\n          outerRef.scrollTop = scrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemCount,\n        itemData,\n        itemKey = defaultItemKey,\n        layout,\n        outerElementType,\n        outerTagName,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      // TODO Deprecate direction \"horizontal\"\n      const isHorizontal =\n        direction === 'horizontal' || layout === 'horizontal';\n\n      const onScroll = isHorizontal\n        ? this._onScrollHorizontal\n        : this._onScrollVertical;\n\n      const [startIndex, stopIndex] = this._getRangeToRender();\n\n      const items = [];\n      if (itemCount > 0) {\n        for (let index = startIndex; index <= stopIndex; index++) {\n          items.push(\n            createElement(children, {\n              data: itemData,\n              key: itemKey(index, itemData),\n              index,\n              isScrolling: useIsScrolling ? isScrolling : undefined,\n              style: this._getItemStyle(index),\n            })\n          );\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalSize = getEstimatedTotalSize(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: isHorizontal ? '100%' : estimatedTotalSize,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: isHorizontal ? estimatedTotalSize : '100%',\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanStartIndex: number,\n      overscanStopIndex: number,\n      visibleStartIndex: number,\n      visibleStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanStartIndex: number,\n        overscanStopIndex: number,\n        visibleStartIndex: number,\n        visibleStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): onItemsRenderedCallback)({\n          overscanStartIndex,\n          overscanStopIndex,\n          visibleStartIndex,\n          visibleStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollDirection: ScrollDirection,\n      scrollOffset: number,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollDirection: ScrollDirection,\n        scrollOffset: number,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): onScrollCallback)({\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      if (typeof this.props.onItemsRendered === 'function') {\n        const { itemCount } = this.props;\n        if (itemCount > 0) {\n          const [\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex,\n          ] = this._getRangeToRender();\n          this._callOnItemsRendered(\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex\n          );\n        }\n      }\n\n      if (typeof this.props.onScroll === 'function') {\n        const {\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        } = this.state;\n        this._callOnScroll(\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (index: number) => Object;\n    _getItemStyle = (index: number): Object => {\n      const { direction, itemSize, layout } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && itemSize,\n        shouldResetStyleCacheOnItemSizeChange && layout,\n        shouldResetStyleCacheOnItemSizeChange && direction\n      );\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(index)) {\n        style = itemStyleCache[index];\n      } else {\n        const offset = getItemOffset(this.props, index, this._instanceProps);\n        const size = getItemSize(this.props, index, this._instanceProps);\n\n        // TODO Deprecate direction \"horizontal\"\n        const isHorizontal =\n          direction === 'horizontal' || layout === 'horizontal';\n\n        const isRtl = direction === 'rtl';\n        const offsetHorizontal = isHorizontal ? offset : 0;\n        itemStyleCache[index] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offsetHorizontal,\n          right: isRtl ? offsetHorizontal : undefined,\n          top: !isHorizontal ? offset : 0,\n          height: !isHorizontal ? size : '100%',\n          width: isHorizontal ? size : '100%',\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getRangeToRender(): [number, number, number, number] {\n      const { itemCount, overscanCount } = this.props;\n      const { isScrolling, scrollDirection, scrollOffset } = this.state;\n\n      if (itemCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getStartIndexForOffset(\n        this.props,\n        scrollOffset,\n        this._instanceProps\n      );\n      const stopIndex = getStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollOffset,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || scrollDirection === 'backward'\n          ? Math.max(1, overscanCount)\n          : 1;\n      const overscanForward =\n        !isScrolling || scrollDirection === 'forward'\n          ? Math.max(1, overscanCount)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(itemCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScrollHorizontal = (event: ScrollEvent): void => {\n      const { clientWidth, scrollLeft, scrollWidth } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollLeft) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        let scrollOffset = scrollLeft;\n        if (direction === 'rtl') {\n          // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n          // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n          // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n          // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              scrollOffset = -scrollLeft;\n              break;\n            case 'positive-descending':\n              scrollOffset = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        scrollOffset = Math.max(\n          0,\n          Math.min(scrollOffset, scrollWidth - clientWidth)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollLeft ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _onScrollVertical = (event: ScrollEvent): void => {\n      const { clientHeight, scrollHeight, scrollTop } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollTop) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        const scrollOffset = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1, null);\n      });\n    };\n  };\n}\n\n// NOTE: I considered further wrapping individual items with a pure ListItem component.\n// This would avoid ever calling the render function for the same index more than once,\n// But it would also add the overhead of a lot of components/fibers.\n// I assume people already do this (render function returning a class component),\n// So my doing it would just unnecessarily double the wrappers.\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    layout,\n    innerTagName,\n    outerTagName,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n\n    switch (direction) {\n      case 'horizontal':\n      case 'vertical':\n        if (devWarningsDirection && !devWarningsDirection.has(instance)) {\n          devWarningsDirection.add(instance);\n          console.warn(\n            'The direction prop should be either \"ltr\" (default) or \"rtl\". ' +\n              'Please use the layout prop to specify \"vertical\" (default) or \"horizontal\" orientation.'\n          );\n        }\n        break;\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    switch (layout) {\n      case 'horizontal':\n      case 'vertical':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"layout\" prop has been specified. ' +\n            'Value should be either \"horizontal\" or \"vertical\". ' +\n            `\"${layout}\" was specified.`\n        );\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    if (isHorizontal && typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Horizontal lists must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    } else if (!isHorizontal && typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Vertical lists must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createListComponent';\n\nconst DEFAULT_ESTIMATED_ITEM_SIZE = 50;\n\ntype VariableSizeProps = {|\n  estimatedItemSize: number,\n  ...Props<any>,\n|};\n\ntype itemSizeGetter = (index: number) => number;\n\ntype ItemMetadata = {|\n  offset: number,\n  size: number,\n|};\ntype InstanceProps = {|\n  itemMetadataMap: { [index: number]: ItemMetadata },\n  estimatedItemSize: number,\n  lastMeasuredIndex: number,\n|};\n\nconst getItemMetadata = (\n  props: Props<any>,\n  index: number,\n  instanceProps: InstanceProps\n): ItemMetadata => {\n  const { itemSize } = ((props: any): VariableSizeProps);\n  const { itemMetadataMap, lastMeasuredIndex } = instanceProps;\n\n  if (index > lastMeasuredIndex) {\n    let offset = 0;\n    if (lastMeasuredIndex >= 0) {\n      const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n      offset = itemMetadata.offset + itemMetadata.size;\n    }\n\n    for (let i = lastMeasuredIndex + 1; i <= index; i++) {\n      let size = ((itemSize: any): itemSizeGetter)(i);\n\n      itemMetadataMap[i] = {\n        offset,\n        size,\n      };\n\n      offset += size;\n    }\n\n    instanceProps.lastMeasuredIndex = index;\n  }\n\n  return itemMetadataMap[index];\n};\n\nconst findNearestItem = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  offset: number\n) => {\n  const { itemMetadataMap, lastMeasuredIndex } = instanceProps;\n\n  const lastMeasuredItemOffset =\n    lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n\n  if (lastMeasuredItemOffset >= offset) {\n    // If we've already measured items within this range just use a binary search as it's faster.\n    return findNearestItemBinarySearch(\n      props,\n      instanceProps,\n      lastMeasuredIndex,\n      0,\n      offset\n    );\n  } else {\n    // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n    // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n    // The overall complexity for this approach is O(log n).\n    return findNearestItemExponentialSearch(\n      props,\n      instanceProps,\n      Math.max(0, lastMeasuredIndex),\n      offset\n    );\n  }\n};\n\nconst findNearestItemBinarySearch = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  high: number,\n  low: number,\n  offset: number\n): number => {\n  while (low <= high) {\n    const middle = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemMetadata(props, middle, instanceProps).offset;\n\n    if (currentOffset === offset) {\n      return middle;\n    } else if (currentOffset < offset) {\n      low = middle + 1;\n    } else if (currentOffset > offset) {\n      high = middle - 1;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nconst findNearestItemExponentialSearch = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  index: number,\n  offset: number\n): number => {\n  const { itemCount } = props;\n  let interval = 1;\n\n  while (\n    index < itemCount &&\n    getItemMetadata(props, index, instanceProps).offset < offset\n  ) {\n    index += interval;\n    interval *= 2;\n  }\n\n  return findNearestItemBinarySearch(\n    props,\n    instanceProps,\n    Math.min(index, itemCount - 1),\n    Math.floor(index / 2),\n    offset\n  );\n};\n\nconst getEstimatedTotalSize = (\n  { itemCount }: Props<any>,\n  { itemMetadataMap, estimatedItemSize, lastMeasuredIndex }: InstanceProps\n) => {\n  let totalSizeOfMeasuredItems = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredIndex >= itemCount) {\n    lastMeasuredIndex = itemCount - 1;\n  }\n\n  if (lastMeasuredIndex >= 0) {\n    const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n    totalSizeOfMeasuredItems = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = itemCount - lastMeasuredIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedItemSize;\n\n  return totalSizeOfMeasuredItems + totalSizeOfUnmeasuredItems;\n};\n\nconst VariableSizeList = createListComponent({\n  getItemOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata(props, index, instanceProps).offset,\n\n  getItemSize: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.itemMetadataMap[index].size,\n\n  getEstimatedTotalSize,\n\n  getOffsetForIndexAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { direction, height, layout, width } = props;\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const itemMetadata = getItemMetadata(props, index, instanceProps);\n\n    // Get estimated total size after ItemMetadata is computed,\n    // To ensure it reflects actual measurements instead of just estimates.\n    const estimatedTotalSize = getEstimatedTotalSize(props, instanceProps);\n\n    const maxOffset = Math.max(\n      0,\n      Math.min(estimatedTotalSize - size, itemMetadata.offset)\n    );\n    const minOffset = Math.max(\n      0,\n      itemMetadata.offset - size + itemMetadata.size\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        return Math.round(minOffset + (maxOffset - minOffset) / 2);\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    props: Props<any>,\n    offset: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem(props, instanceProps, offset),\n\n  getStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollOffset: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { direction, height, itemCount, layout, width } = props;\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const itemMetadata = getItemMetadata(props, startIndex, instanceProps);\n    const maxOffset = scrollOffset + size;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < itemCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata(props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  initInstanceProps(props: Props<any>, instance: any): InstanceProps {\n    const { estimatedItemSize } = ((props: any): VariableSizeProps);\n\n    const instanceProps = {\n      itemMetadataMap: {},\n      estimatedItemSize: estimatedItemSize || DEFAULT_ESTIMATED_ITEM_SIZE,\n      lastMeasuredIndex: -1,\n    };\n\n    instance.resetAfterIndex = (\n      index: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instanceProps.lastMeasuredIndex = Math.min(\n        instanceProps.lastMeasuredIndex,\n        index - 1\n      );\n\n      // We could potentially optimize further by only evicting styles after this index,\n      // But since styles are only cached while scrolling is in progress-\n      // It seems an unnecessary optimization.\n      // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n      instance._getItemStyleCache(-1);\n\n      if (shouldForceUpdate) {\n        instance.forceUpdate();\n      }\n    };\n\n    return instanceProps;\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: false,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'function') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default VariableSizeList;\n", "// @flow\n\nimport createGridComponent from './createGridComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createGridComponent';\n\nconst FixedSizeGrid = createGridComponent({\n  getColumnOffset: ({ columnWidth }: Props<any>, index: number): number =>\n    index * ((columnWidth: any): number),\n\n  getColumnWidth: ({ columnWidth }: Props<any>, index: number): number =>\n    ((columnWidth: any): number),\n\n  getRowOffset: ({ rowHeight }: Props<any>, index: number): number =>\n    index * ((rowHeight: any): number),\n\n  getRowHeight: ({ rowHeight }: Props<any>, index: number): number =>\n    ((rowHeight: any): number),\n\n  getEstimatedTotalHeight: ({ rowCount, rowHeight }: Props<any>) =>\n    ((rowHeight: any): number) * rowCount,\n\n  getEstimatedTotalWidth: ({ columnCount, columnWidth }: Props<any>) =>\n    ((columnWidth: any): number) * columnCount,\n\n  getOffsetForColumnAndAlignment: (\n    { columnCount, columnWidth, width }: Props<any>,\n    columnIndex: number,\n    align: ScrollToAlign,\n    scrollLeft: number,\n    instanceProps: typeof undefined,\n    scrollbarSize: number\n  ): number => {\n    const lastColumnOffset = Math.max(\n      0,\n      columnCount * ((columnWidth: any): number) - width\n    );\n    const maxOffset = Math.min(\n      lastColumnOffset,\n      columnIndex * ((columnWidth: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      columnIndex * ((columnWidth: any): number) -\n        width +\n        scrollbarSize +\n        ((columnWidth: any): number)\n    );\n\n    if (align === 'smart') {\n      if (scrollLeft >= minOffset - width && scrollLeft <= maxOffset + width) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(width / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastColumnOffset + Math.floor(width / 2)) {\n          return lastColumnOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      case 'auto':\n      default:\n        if (scrollLeft >= minOffset && scrollLeft <= maxOffset) {\n          return scrollLeft;\n        } else if (minOffset > maxOffset) {\n          // Because we only take into account the scrollbar size when calculating minOffset\n          // this value can be larger than maxOffset when at the end of the list\n          return minOffset;\n        } else if (scrollLeft < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getOffsetForRowAndAlignment: (\n    { rowHeight, height, rowCount }: Props<any>,\n    rowIndex: number,\n    align: ScrollToAlign,\n    scrollTop: number,\n    instanceProps: typeof undefined,\n    scrollbarSize: number\n  ): number => {\n    const lastRowOffset = Math.max(\n      0,\n      rowCount * ((rowHeight: any): number) - height\n    );\n    const maxOffset = Math.min(\n      lastRowOffset,\n      rowIndex * ((rowHeight: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      rowIndex * ((rowHeight: any): number) -\n        height +\n        scrollbarSize +\n        ((rowHeight: any): number)\n    );\n\n    if (align === 'smart') {\n      if (scrollTop >= minOffset - height && scrollTop <= maxOffset + height) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(height / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastRowOffset + Math.floor(height / 2)) {\n          return lastRowOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      case 'auto':\n      default:\n        if (scrollTop >= minOffset && scrollTop <= maxOffset) {\n          return scrollTop;\n        } else if (minOffset > maxOffset) {\n          // Because we only take into account the scrollbar size when calculating minOffset\n          // this value can be larger than maxOffset when at the end of the list\n          return minOffset;\n        } else if (scrollTop < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getColumnStartIndexForOffset: (\n    { columnWidth, columnCount }: Props<any>,\n    scrollLeft: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(\n        columnCount - 1,\n        Math.floor(scrollLeft / ((columnWidth: any): number))\n      )\n    ),\n\n  getColumnStopIndexForStartIndex: (\n    { columnWidth, columnCount, width }: Props<any>,\n    startIndex: number,\n    scrollLeft: number\n  ): number => {\n    const left = startIndex * ((columnWidth: any): number);\n    const numVisibleColumns = Math.ceil(\n      (width + scrollLeft - left) / ((columnWidth: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        columnCount - 1,\n        startIndex + numVisibleColumns - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  getRowStartIndexForOffset: (\n    { rowHeight, rowCount }: Props<any>,\n    scrollTop: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(rowCount - 1, Math.floor(scrollTop / ((rowHeight: any): number)))\n    ),\n\n  getRowStopIndexForStartIndex: (\n    { rowHeight, rowCount, height }: Props<any>,\n    startIndex: number,\n    scrollTop: number\n  ): number => {\n    const top = startIndex * ((rowHeight: any): number);\n    const numVisibleRows = Math.ceil(\n      (height + scrollTop - top) / ((rowHeight: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        rowCount - 1,\n        startIndex + numVisibleRows - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ columnWidth, rowHeight }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof columnWidth !== 'number') {\n        throw Error(\n          'An invalid \"columnWidth\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${\n              columnWidth === null ? 'null' : typeof columnWidth\n            }\" was specified.`\n        );\n      }\n\n      if (typeof rowHeight !== 'number') {\n        throw Error(\n          'An invalid \"rowHeight\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${rowHeight === null ? 'null' : typeof rowHeight}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeGrid;\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Props, ScrollToAlign } from './createListComponent';\n\nconst FixedSizeList = createListComponent({\n  getItemOffset: ({ itemSize }: Props<any>, index: number): number =>\n    index * ((itemSize: any): number),\n\n  getItemSize: ({ itemSize }: Props<any>, index: number): number =>\n    ((itemSize: any): number),\n\n  getEstimatedTotalSize: ({ itemCount, itemSize }: Props<any>) =>\n    ((itemSize: any): number) * itemCount,\n\n  getOffsetForIndexAndAlignment: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const lastItemOffset = Math.max(\n      0,\n      itemCount * ((itemSize: any): number) - size\n    );\n    const maxOffset = Math.min(\n      lastItemOffset,\n      index * ((itemSize: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      index * ((itemSize: any): number) - size + ((itemSize: any): number)\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center': {\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(size / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastItemOffset + Math.floor(size / 2)) {\n          return lastItemOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      }\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    { itemCount, itemSize }: Props<any>,\n    offset: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(itemCount - 1, Math.floor(offset / ((itemSize: any): number)))\n    ),\n\n  getStopIndexForStartIndex: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    startIndex: number,\n    scrollOffset: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const offset = startIndex * ((itemSize: any): number);\n    const size = (((isHorizontal ? width : height): any): number);\n    const numVisibleItems = Math.ceil(\n      (size + scrollOffset - offset) / ((itemSize: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        itemCount - 1,\n        startIndex + numVisibleItems - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'number') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeList;\n", "// @flow\n\n// Pulled from react-compat\n// https://github.com/developit/preact-compat/blob/7c5de00e7c85e2ffd011bf3af02899b63f699d3a/src/index.js#L349\nexport default function shallowDiffers(prev: Object, next: Object): boolean {\n  for (let attribute in prev) {\n    if (!(attribute in next)) {\n      return true;\n    }\n  }\n  for (let attribute in next) {\n    if (prev[attribute] !== next[attribute]) {\n      return true;\n    }\n  }\n  return false;\n}\n", "// @flow\n\nimport shallowDiffers from './shallowDiffers';\n\n// Custom comparison function for React.memo().\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-api.html#reactmemo\nexport default function areEqual(\n  prevProps: Object,\n  nextProps: Object\n): boolean {\n  const { style: prevStyle, ...prevRest } = prevProps;\n  const { style: nextStyle, ...nextRest } = nextProps;\n\n  return (\n    !shallowDiffers(prevStyle, nextStyle) && !shallowDiffers(prevRest, nextRest)\n  );\n}\n", "// @flow\n\nimport areEqual from './areEqual';\nimport shallowDiffers from './shallowDiffers';\n\n// Custom shouldComponentUpdate for class components.\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-component.html#shouldcomponentupdate\nexport default function shouldComponentUpdate(\n  nextProps: Object,\n  nextState: Object\n): boolean {\n  return (\n    !areEqual(this.props, nextProps) || shallowDiffers(this.state, nextState)\n  );\n}\n"], "names": ["hasNativePerformanceNow", "performance", "now", "Date", "cancelTimeout", "timeoutID", "cancelAnimationFrame", "id", "requestTimeout", "callback", "delay", "start", "tick", "call", "requestAnimationFrame", "size", "getScrollbarSize", "recalculate", "div", "document", "createElement", "style", "width", "height", "overflow", "body", "append<PERSON><PERSON><PERSON>", "offsetWidth", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "cachedRTLResult", "getRTLOffsetType", "outerDiv", "outerStyle", "direction", "innerDiv", "innerStyle", "scrollLeft", "IS_SCROLLING_DEBOUNCE_INTERVAL", "defaultItemKey", "columnIndex", "data", "rowIndex", "devWarningsOverscanCount", "devWarningsOverscanRowsColumnsCount", "devWarningsTagName", "process", "env", "NODE_ENV", "window", "WeakSet", "createGridComponent", "getColumnOffset", "getColumnStartIndexForOffset", "getColumnStopIndexForStartIndex", "getColumnWidth", "getEstimatedTotalHeight", "getEstimatedTotalWidth", "getOffsetForColumnAndAlignment", "getOffsetForRowAndAlignment", "getRowHeight", "getRowOffset", "getRowStartIndexForOffset", "getRowStopIndexForStartIndex", "initInstanceProps", "shouldResetStyleCacheOnItemSizeChange", "validateProps", "props", "_instanceProps", "_resetIsScrollingTimeoutId", "_outerRef", "state", "instance", "isScrolling", "horizontalScrollDirection", "initialScrollLeft", "scrollTop", "initialScrollTop", "scrollUpdateWasRequested", "verticalScrollDirection", "_callOnItemsRendered", "memoizeOne", "overscanColumnStartIndex", "overscanColumnStopIndex", "overscanRowStartIndex", "overscanRowStopIndex", "visibleColumnStartIndex", "visibleColumnStopIndex", "visibleRowStartIndex", "visibleRowStopIndex", "onItemsRendered", "_callOnScroll", "onScroll", "_getItemStyle", "columnWidth", "rowHeight", "itemStyleCache", "_getItemStyleCache", "key", "hasOwnProperty", "offset", "isRtl", "position", "left", "undefined", "right", "top", "_", "__", "___", "_onScroll", "event", "currentTarget", "clientHeight", "scrollHeight", "scrollWidth", "setState", "prevState", "calculatedScrollLeft", "Math", "max", "min", "calculatedScrollTop", "_resetIsScrollingDebounced", "_outerRefSetter", "ref", "outerRef", "current", "_resetIsScrolling", "getDerivedStateFromProps", "nextProps", "validateSharedProps", "scrollTo", "scrollToItem", "align", "columnCount", "rowCount", "scrollbarSize", "estimatedTotalHeight", "estimatedTotalWidth", "horizontalScrollbarSize", "verticalScrollbarSize", "componentDidMount", "_callPropsCallbacks", "componentDidUpdate", "componentWillUnmount", "render", "children", "className", "innerRef", "innerElementType", "innerTagName", "itemData", "itemKey", "outerElementType", "outerTagName", "useIsScrolling", "_getHorizontalRangeToRender", "columnStartIndex", "columnStopIndex", "_getVerticalRangeToRender", "rowStartIndex", "rowStopIndex", "items", "push", "WebkitOverflowScrolling", "<PERSON><PERSON><PERSON><PERSON>", "pointerEvents", "overscanColumnCount", "overscanColumnsCount", "overscanCount", "overscanCountResolved", "startIndex", "stopIndex", "overscanBackward", "overscanForward", "overscanRowCount", "overscanRowsCount", "PureComponent", "defaultProps", "has", "add", "console", "warn", "Error", "DEFAULT_ESTIMATED_ITEM_SIZE", "rowMetadataMap", "estimatedRowHeight", "lastMeasuredRowIndex", "totalSizeOfMeasuredRows", "itemMetadata", "numUnmeasuredItems", "totalSizeOfUnmeasuredItems", "columnMetadataMap", "estimatedColumnWidth", "lastMeasuredColumnIndex", "getItemMetadata", "itemType", "index", "instanceProps", "itemMetadataMap", "itemSize", "lastMeasuredIndex", "i", "findNearestItem", "lastMeasuredItemOffset", "findNearestItemBinarySearch", "findNearestItemExponentialSearch", "high", "low", "middle", "floor", "currentOffset", "itemCount", "interval", "getOffsetForIndexAndAlignment", "scrollOffset", "estimatedTotalSize", "maxOffset", "minOffset", "round", "VariableSizeGrid", "resetAfterColumnIndex", "shouldForceUpdate", "resetAfterIndices", "resetAfterRowIndex", "forceUpdate", "devWarningsDirection", "createListComponent", "getItemOffset", "getEstimatedTotalSize", "getItemSize", "getStartIndexForOffset", "getStopIndexForStartIndex", "scrollDirection", "initialScrollOffset", "overscanStartIndex", "overscanStopIndex", "visibleStartIndex", "visibleStopIndex", "layout", "isHorizontal", "offsetHorizontal", "_onScrollHorizontal", "_onScrollVertical", "_getRangeToRender", "estimatedItemSize", "totalSizeOfMeasuredItems", "VariableSizeList", "resetAfterIndex", "FixedSizeGrid", "lastColumnOffset", "middleOffset", "ceil", "lastRowOffset", "numVisibleColumns", "numVisibleRows", "FixedSizeList", "lastItemOffset", "numVisibleItems", "shallow<PERSON>iffers", "prev", "next", "attribute", "areEqual", "prevProps", "prevStyle", "prevRest", "nextStyle", "nextRest", "shouldComponentUpdate", "nextState"], "mappings": ";;;;;;;;;;;;;AAEA;;AAGA,IAAMA,uBAAuB,GAC3B,OAAOC,WAAP,KAAuB,QAAvB,IAAmC,OAAOA,WAAW,CAACC,GAAnB,KAA2B,UADhE;AAGA,IAAMA,GAAG,GAAGF,uBAAuB,GAC/B;SAAMC,WAAW,CAACC,GAAZ,EAAN;CAD+B,GAE/B;SAAMC,IAAI,CAACD,GAAL,EAAN;CAFJ;AAQA,AAAO,SAASE,aAAT,CAAuBC,SAAvB,EAA6C;EAClDC,oBAAoB,CAACD,SAAS,CAACE,EAAX,CAApB;;AAGF,AAAO,SAASC,cAAT,CAAwBC,QAAxB,EAA4CC,KAA5C,EAAsE;MACrEC,KAAK,GAAGT,GAAG,EAAjB;;WAESU,IAAT,GAAgB;QACVV,GAAG,KAAKS,KAAR,IAAiBD,KAArB,EAA4B;MAC1BD,QAAQ,CAACI,IAAT,CAAc,IAAd;KADF,MAEO;MACLR,SAAS,CAACE,EAAV,GAAeO,qBAAqB,CAACF,IAAD,CAApC;;;;MAIEP,SAAoB,GAAG;IAC3BE,EAAE,EAAEO,qBAAqB,CAACF,IAAD;GAD3B;SAIOP,SAAP;;;ACjCF,IAAIU,IAAY,GAAG,CAAC,CAApB;;AAGA,AAAO,SAASC,gBAAT,CAA0BC,WAA1B,EAAiE;MAAvCA,WAAuC;IAAvCA,WAAuC,GAAf,KAAe;;;MAClEF,IAAI,KAAK,CAAC,CAAV,IAAeE,WAAnB,EAAgC;QACxBC,GAAG,GAAGC,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAZ;QACMC,KAAK,GAAGH,GAAG,CAACG,KAAlB;IACAA,KAAK,CAACC,KAAN,GAAc,MAAd;IACAD,KAAK,CAACE,MAAN,GAAe,MAAf;IACAF,KAAK,CAACG,QAAN,GAAiB,QAAjB;IAEEL,QAAQ,CAACM,IAAX,CAAwCC,WAAxC,CAAoDR,GAApD;IAEAH,IAAI,GAAGG,GAAG,CAACS,WAAJ,GAAkBT,GAAG,CAACU,WAA7B;IAEET,QAAQ,CAACM,IAAX,CAAwCI,WAAxC,CAAoDX,GAApD;;;SAGKH,IAAP;;AAQF,IAAIe,eAAqC,GAAG,IAA5C;;;;;;;AAQA,AAAO,SAASC,gBAAT,CAA0Bd,WAA1B,EAAwE;MAA9CA,WAA8C;IAA9CA,WAA8C,GAAtB,KAAsB;;;MACzEa,eAAe,KAAK,IAApB,IAA4Bb,WAAhC,EAA6C;QACrCe,QAAQ,GAAGb,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAjB;QACMa,UAAU,GAAGD,QAAQ,CAACX,KAA5B;IACAY,UAAU,CAACX,KAAX,GAAmB,MAAnB;IACAW,UAAU,CAACV,MAAX,GAAoB,MAApB;IACAU,UAAU,CAACT,QAAX,GAAsB,QAAtB;IACAS,UAAU,CAACC,SAAX,GAAuB,KAAvB;QAEMC,QAAQ,GAAGhB,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAjB;QACMgB,UAAU,GAAGD,QAAQ,CAACd,KAA5B;IACAe,UAAU,CAACd,KAAX,GAAmB,OAAnB;IACAc,UAAU,CAACb,MAAX,GAAoB,OAApB;IAEAS,QAAQ,CAACN,WAAT,CAAqBS,QAArB;IAEEhB,QAAQ,CAACM,IAAX,CAAwCC,WAAxC,CAAoDM,QAApD;;QAEIA,QAAQ,CAACK,UAAT,GAAsB,CAA1B,EAA6B;MAC3BP,eAAe,GAAG,qBAAlB;KADF,MAEO;MACLE,QAAQ,CAACK,UAAT,GAAsB,CAAtB;;UACIL,QAAQ,CAACK,UAAT,KAAwB,CAA5B,EAA+B;QAC7BP,eAAe,GAAG,UAAlB;OADF,MAEO;QACLA,eAAe,GAAG,oBAAlB;;;;IAIFX,QAAQ,CAACM,IAAX,CAAwCI,WAAxC,CAAoDG,QAApD;WAEOF,eAAP;;;SAGKA,eAAP;;;ACwEF,IAAMQ,8BAA8B,GAAG,GAAvC;;AAEA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB;MAAGC,WAAH,QAAGA,WAAH;MAAgBC,IAAhB,QAAgBA,IAAhB;MAAsBC,QAAtB,QAAsBA,QAAtB;SAClBA,QADkB,SACNF,WADM;CAAvB;;;;AAKA,IAAIG,wBAAwB,GAAG,IAA/B;AACA,IAAIC,mCAAmC,GAAG,IAA1C;AACA,IAAIC,kBAAkB,GAAG,IAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;MACrC,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,OAAOA,MAAM,CAACC,OAAd,KAA0B,WAA/D,EAA4E;IAC1EP,wBAAwB;;QAAOO,OAAJ,EAA3B;IACAN,mCAAmC;;QAAOM,OAAJ,EAAtC;IACAL,kBAAkB;;QAAOK,OAAJ,EAArB;;;;AAIJ,AAAe,SAASC,mBAAT,QAgCX;;;MA/BFC,eA+BE,SA/BFA,eA+BE;MA9BFC,4BA8BE,SA9BFA,4BA8BE;MA7BFC,+BA6BE,SA7BFA,+BA6BE;MA5BFC,cA4BE,SA5BFA,cA4BE;MA3BFC,uBA2BE,SA3BFA,uBA2BE;MA1BFC,sBA0BE,SA1BFA,sBA0BE;MAzBFC,8BAyBE,SAzBFA,8BAyBE;MAxBFC,2BAwBE,SAxBFA,2BAwBE;MAvBFC,YAuBE,SAvBFA,YAuBE;MAtBFC,YAsBE,SAtBFA,YAsBE;MArBFC,yBAqBE,SArBFA,yBAqBE;MApBFC,4BAoBE,SApBFA,4BAoBE;MAnBFC,iBAmBE,SAnBFA,iBAmBE;MAlBFC,qCAkBE,SAlBFA,qCAkBE;MAjBFC,aAiBE,SAjBFA,aAiBE;;;;;;;;;kBA+BYC,KAAZ,EAA6B;;;wCACrBA,KAAN;YA9BFC,cA6B6B,GA7BPJ,iBAAiB,CAAC,MAAKG,KAAN,wDA6BV;YA5B7BE,0BA4B6B,GA5BkB,IA4BlB;YA3B7BC,SA2B6B;YAnB7BC,KAmB6B,GAnBd;QACbC,QAAQ,uDADK;QAEbC,WAAW,EAAE,KAFA;QAGbC,yBAAyB,EAAE,SAHd;QAIbrC,UAAU,EACR,OAAO,MAAK8B,KAAL,CAAWQ,iBAAlB,KAAwC,QAAxC,GACI,MAAKR,KAAL,CAAWQ,iBADf,GAEI,CAPO;QAQbC,SAAS,EACP,OAAO,MAAKT,KAAL,CAAWU,gBAAlB,KAAuC,QAAvC,GACI,MAAKV,KAAL,CAAWU,gBADf,GAEI,CAXO;QAYbC,wBAAwB,EAAE,KAZb;QAabC,uBAAuB,EAAE;OAME;YAwQ7BC,oBAxQ6B;YAkR7BA,oBAlR6B,GAkRNC,UAAU,CAC/B,UACEC,wBADF,EAEEC,uBAFF,EAGEC,qBAHF,EAIEC,oBAJF,EAKEC,uBALF,EAMEC,sBANF,EAOEC,oBAPF,EAQEC,mBARF;eAUI,MAAKtB,KAAL,CAAWuB,eAAb,CAA6D;UAC3DR,wBAAwB,EAAxBA,wBAD2D;UAE3DC,uBAAuB,EAAvBA,uBAF2D;UAG3DC,qBAAqB,EAArBA,qBAH2D;UAI3DC,oBAAoB,EAApBA,oBAJ2D;UAK3DC,uBAAuB,EAAvBA,uBAL2D;UAM3DC,sBAAsB,EAAtBA,sBAN2D;UAO3DC,oBAAoB,EAApBA,oBAP2D;UAQ3DC,mBAAmB,EAAnBA;SARF,CAVF;OAD+B,CAlRJ;YAyS7BE,aAzS6B;YAgT7BA,aAhT6B,GAgTbV,UAAU,CACxB,UACE5C,UADF,EAEEuC,SAFF,EAGEF,yBAHF,EAIEK,uBAJF,EAKED,wBALF;eAOI,MAAKX,KAAL,CAAWyB,QAAb,CAA+C;UAC7ClB,yBAAyB,EAAzBA,yBAD6C;UAE7CrC,UAAU,EAAVA,UAF6C;UAG7CuC,SAAS,EAATA,SAH6C;UAI7CG,uBAAuB,EAAvBA,uBAJ6C;UAK7CD,wBAAwB,EAAxBA;SALF,CAPF;OADwB,CAhTG;YAqX7Be,aArX6B;;YAsX7BA,aAtX6B,GAsXb,UAACnD,QAAD,EAAmBF,WAAnB,EAAmD;0BACnB,MAAK2B,KADc;YACzD2B,WADyD,eACzDA,WADyD;YAC5C5D,SAD4C,eAC5CA,SAD4C;YACjC6D,SADiC,eACjCA,SADiC;;YAG3DC,cAAc,GAAG,MAAKC,kBAAL,CACrBhC,qCAAqC,IAAI6B,WADpB,EAErB7B,qCAAqC,IAAI/B,SAFpB,EAGrB+B,qCAAqC,IAAI8B,SAHpB,CAAvB;;YAMMG,GAAG,GAAMxD,QAAN,SAAkBF,WAA3B;YAEInB,KAAJ;;YACI2E,cAAc,CAACG,cAAf,CAA8BD,GAA9B,CAAJ,EAAwC;UACtC7E,KAAK,GAAG2E,cAAc,CAACE,GAAD,CAAtB;SADF,MAEO;cACCE,OAAM,GAAGhD,eAAe,CAC5B,MAAKe,KADuB,EAE5B3B,WAF4B,EAG5B,MAAK4B,cAHuB,CAA9B;;cAKMiC,KAAK,GAAGnE,SAAS,KAAK,KAA5B;UACA8D,cAAc,CAACE,GAAD,CAAd,GAAsB7E,KAAK,GAAG;YAC5BiF,QAAQ,EAAE,UADkB;YAE5BC,IAAI,EAAEF,KAAK,GAAGG,SAAH,GAAeJ,OAFE;YAG5BK,KAAK,EAAEJ,KAAK,GAAGD,OAAH,GAAYI,SAHI;YAI5BE,GAAG,EAAE7C,YAAY,CAAC,MAAKM,KAAN,EAAazB,QAAb,EAAuB,MAAK0B,cAA5B,CAJW;YAK5B7C,MAAM,EAAEqC,YAAY,CAAC,MAAKO,KAAN,EAAazB,QAAb,EAAuB,MAAK0B,cAA5B,CALQ;YAM5B9C,KAAK,EAAEiC,cAAc,CAAC,MAAKY,KAAN,EAAa3B,WAAb,EAA0B,MAAK4B,cAA/B;WANvB;;;eAUK/C,KAAP;OArZ2B;;YAwZ7B4E,kBAxZ6B;YAyZ7BA,kBAzZ6B,GAyZRhB,UAAU,CAAC,UAAC0B,CAAD,EAASC,EAAT,EAAkBC,GAAlB;eAAgC,EAAhC;OAAD,CAzZF;;YA2f7BC,SA3f6B,GA2fjB,UAACC,KAAD,EAA8B;mCAQpCA,KAAK,CAACC,aAR8B;YAEtCC,YAFsC,wBAEtCA,YAFsC;YAGtCrF,WAHsC,wBAGtCA,WAHsC;YAItCS,UAJsC,wBAItCA,UAJsC;YAKtCuC,SALsC,wBAKtCA,SALsC;YAMtCsC,YANsC,wBAMtCA,YANsC;YAOtCC,WAPsC,wBAOtCA,WAPsC;;cASnCC,QAAL,CAAc,UAAAC,SAAS,EAAI;cAEvBA,SAAS,CAAChF,UAAV,KAAyBA,UAAzB,IACAgF,SAAS,CAACzC,SAAV,KAAwBA,SAF1B,EAGE;;;;mBAIO,IAAP;;;cAGM1C,SAXiB,GAWH,MAAKiC,KAXF,CAWjBjC,SAXiB;;;;;cAiBrBoF,oBAAoB,GAAGjF,UAA3B;;cACIH,SAAS,KAAK,KAAlB,EAAyB;oBACfH,gBAAgB,EAAxB;mBACO,UAAL;gBACEuF,oBAAoB,GAAG,CAACjF,UAAxB;;;mBAEG,qBAAL;gBACEiF,oBAAoB,GAAGH,WAAW,GAAGvF,WAAd,GAA4BS,UAAnD;;;WAxBmB;;;UA8BzBiF,oBAAoB,GAAGC,IAAI,CAACC,GAAL,CACrB,CADqB,EAErBD,IAAI,CAACE,GAAL,CAASH,oBAAT,EAA+BH,WAAW,GAAGvF,WAA7C,CAFqB,CAAvB;cAIM8F,mBAAmB,GAAGH,IAAI,CAACC,GAAL,CAC1B,CAD0B,EAE1BD,IAAI,CAACE,GAAL,CAAS7C,SAAT,EAAoBsC,YAAY,GAAGD,YAAnC,CAF0B,CAA5B;iBAKO;YACLxC,WAAW,EAAE,IADR;YAELC,yBAAyB,EACvB2C,SAAS,CAAChF,UAAV,GAAuBA,UAAvB,GAAoC,SAApC,GAAgD,UAH7C;YAILA,UAAU,EAAEiF,oBAJP;YAKL1C,SAAS,EAAE8C,mBALN;YAML3C,uBAAuB,EACrBsC,SAAS,CAACzC,SAAV,GAAsBA,SAAtB,GAAkC,SAAlC,GAA8C,UAP3C;YAQLE,wBAAwB,EAAE;WAR5B;SAvCF,EAiDG,MAAK6C,0BAjDR;OApgB2B;;YAwjB7BC,eAxjB6B,GAwjBX,UAACC,GAAD,EAAoB;YAC5BC,QAD4B,GACf,MAAK3D,KADU,CAC5B2D,QAD4B;cAG/BxD,SAAL,GAAmBuD,GAAnB;;YAEI,OAAOC,QAAP,KAAoB,UAAxB,EAAoC;UAClCA,QAAQ,CAACD,GAAD,CAAR;SADF,MAEO,IACLC,QAAQ,IAAI,IAAZ,IACA,OAAOA,QAAP,KAAoB,QADpB,IAEAA,QAAQ,CAAC3B,cAAT,CAAwB,SAAxB,CAHK,EAIL;UACA2B,QAAQ,CAACC,OAAT,GAAmBF,GAAnB;;OApkByB;;YAwkB7BF,0BAxkB6B,GAwkBA,YAAM;YAC7B,MAAKtD,0BAAL,KAAoC,IAAxC,EAA8C;UAC5CjE,aAAa,CAAC,MAAKiE,0BAAN,CAAb;;;cAGGA,0BAAL,GAAkC7D,cAAc,CAC9C,MAAKwH,iBADyC,EAE9C1F,8BAF8C,CAAhD;OA7kB2B;;YAmlB7B0F,iBAnlB6B,GAmlBT,YAAM;cACnB3D,0BAAL,GAAkC,IAAlC;;cAEK+C,QAAL,CAAc;UAAE3C,WAAW,EAAE;SAA7B,EAAsC,YAAM;;;gBAGrCwB,kBAAL,CAAwB,CAAC,CAAzB;SAHF;OAtlB2B;;;;;SAItBgC,wBAlCT,qCAmCIC,SAnCJ,EAoCIb,SApCJ,EAqC0B;MACtBc,mBAAmB,CAACD,SAAD,EAAYb,SAAZ,CAAnB;MACAnD,aAAa,CAACgE,SAAD,CAAb;aACO,IAAP;KAxCJ;;;;WA2CEE,QA3CF,4BAiDW;UALP/F,UAKO,SALPA,UAKO;UAJPuC,SAIO,SAJPA,SAIO;;UACHvC,UAAU,KAAKmE,SAAnB,EAA8B;QAC5BnE,UAAU,GAAGkF,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYnF,UAAZ,CAAb;;;UAEEuC,SAAS,KAAK4B,SAAlB,EAA6B;QAC3B5B,SAAS,GAAG2C,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY5C,SAAZ,CAAZ;;;WAGGwC,QAAL,CAAc,UAAAC,SAAS,EAAI;YACrBhF,UAAU,KAAKmE,SAAnB,EAA8B;UAC5BnE,UAAU,GAAGgF,SAAS,CAAChF,UAAvB;;;YAEEuC,SAAS,KAAK4B,SAAlB,EAA6B;UAC3B5B,SAAS,GAAGyC,SAAS,CAACzC,SAAtB;;;YAIAyC,SAAS,CAAChF,UAAV,KAAyBA,UAAzB,IACAgF,SAAS,CAACzC,SAAV,KAAwBA,SAF1B,EAGE;iBACO,IAAP;;;eAGK;UACLF,yBAAyB,EACvB2C,SAAS,CAAChF,UAAV,GAAuBA,UAAvB,GAAoC,SAApC,GAAgD,UAF7C;UAGLA,UAAU,EAAEA,UAHP;UAILuC,SAAS,EAAEA,SAJN;UAKLE,wBAAwB,EAAE,IALrB;UAMLC,uBAAuB,EACrBsC,SAAS,CAACzC,SAAV,GAAsBA,SAAtB,GAAkC,SAAlC,GAA8C;SAPlD;OAfF,EAwBG,KAAK+C,0BAxBR;KAzDJ;;WAoFEU,YApFF,gCA4FW;8BAPPC,KAOO;UAPPA,KAOO,4BAPC,MAOD;UANP9F,WAMO,SANPA,WAMO;UALPE,QAKO,SALPA,QAKO;yBAC0C,KAAKyB,KAD/C;UACCoE,WADD,gBACCA,WADD;UACchH,MADd,gBACcA,MADd;UACsBiH,QADtB,gBACsBA,QADtB;UACgClH,KADhC,gBACgCA,KADhC;wBAE2B,KAAKiD,KAFhC;UAEClC,UAFD,eAECA,UAFD;UAEauC,SAFb,eAEaA,SAFb;UAGD6D,aAAa,GAAGzH,gBAAgB,EAAtC;;UAEIwB,WAAW,KAAKgE,SAApB,EAA+B;QAC7BhE,WAAW,GAAG+E,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAASjF,WAAT,EAAsB+F,WAAW,GAAG,CAApC,CAAZ,CAAd;;;UAEE7F,QAAQ,KAAK8D,SAAjB,EAA4B;QAC1B9D,QAAQ,GAAG6E,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS/E,QAAT,EAAmB8F,QAAQ,GAAG,CAA9B,CAAZ,CAAX;;;UAGIE,oBAAoB,GAAGlF,uBAAuB,CAClD,KAAKW,KAD6C,EAElD,KAAKC,cAF6C,CAApD;UAIMuE,mBAAmB,GAAGlF,sBAAsB,CAChD,KAAKU,KAD2C,EAEhD,KAAKC,cAF2C,CAAlD,CAhBO;;;;UAwBDwE,uBAAuB,GAC3BD,mBAAmB,GAAGrH,KAAtB,GAA8BmH,aAA9B,GAA8C,CADhD;UAEMI,qBAAqB,GACzBH,oBAAoB,GAAGnH,MAAvB,GAAgCkH,aAAhC,GAAgD,CADlD;WAGKL,QAAL,CAAc;QACZ/F,UAAU,EACRG,WAAW,KAAKgE,SAAhB,GACI9C,8BAA8B,CAC5B,KAAKS,KADuB,EAE5B3B,WAF4B,EAG5B8F,KAH4B,EAI5BjG,UAJ4B,EAK5B,KAAK+B,cALuB,EAM5ByE,qBAN4B,CADlC,GASIxG,UAXM;QAYZuC,SAAS,EACPlC,QAAQ,KAAK8D,SAAb,GACI7C,2BAA2B,CACzB,KAAKQ,KADoB,EAEzBzB,QAFyB,EAGzB4F,KAHyB,EAIzB1D,SAJyB,EAKzB,KAAKR,cALoB,EAMzBwE,uBANyB,CAD/B,GASIhE;OAtBR;KAzHJ;;WAmJEkE,iBAnJF,gCAmJsB;yBAC8B,KAAK3E,KADnC;UACVQ,iBADU,gBACVA,iBADU;UACSE,gBADT,gBACSA,gBADT;;UAGd,KAAKP,SAAL,IAAkB,IAAtB,EAA4B;YACpBwD,QAAQ,GAAK,KAAKxD,SAAxB;;YACI,OAAOK,iBAAP,KAA6B,QAAjC,EAA2C;UACzCmD,QAAQ,CAACzF,UAAT,GAAsBsC,iBAAtB;;;YAEE,OAAOE,gBAAP,KAA4B,QAAhC,EAA0C;UACxCiD,QAAQ,CAAClD,SAAT,GAAqBC,gBAArB;;;;WAICkE,mBAAL;KAhKJ;;WAmKEC,kBAnKF,iCAmKuB;UACX9G,SADW,GACG,KAAKiC,KADR,CACXjC,SADW;yBAEyC,KAAKqC,KAF9C;UAEXlC,UAFW,gBAEXA,UAFW;UAECuC,SAFD,gBAECA,SAFD;UAEYE,wBAFZ,gBAEYA,wBAFZ;;UAIfA,wBAAwB,IAAI,KAAKR,SAAL,IAAkB,IAAlD,EAAwD;;;;YAIhDwD,QAAQ,GAAK,KAAKxD,SAAxB;;YACIpC,SAAS,KAAK,KAAlB,EAAyB;kBACfH,gBAAgB,EAAxB;iBACO,UAAL;cACE+F,QAAQ,CAACzF,UAAT,GAAsB,CAACA,UAAvB;;;iBAEG,oBAAL;cACEyF,QAAQ,CAACzF,UAAT,GAAsBA,UAAtB;;;;kBAGQT,WADV,GACuCkG,QADvC,CACUlG,WADV;kBACuBuF,WADvB,GACuCW,QADvC,CACuBX,WADvB;cAEEW,QAAQ,CAACzF,UAAT,GAAsB8E,WAAW,GAAGvF,WAAd,GAA4BS,UAAlD;;;SAVN,MAaO;UACLyF,QAAQ,CAACzF,UAAT,GAAsBkF,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYnF,UAAZ,CAAtB;;;QAGFyF,QAAQ,CAAClD,SAAT,GAAqB2C,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY5C,SAAZ,CAArB;;;WAGGmE,mBAAL;KAhMJ;;WAmMEE,oBAnMF,mCAmMyB;UACjB,KAAK5E,0BAAL,KAAoC,IAAxC,EAA8C;QAC5CjE,aAAa,CAAC,KAAKiE,0BAAN,CAAb;;KArMN;;WAyME6E,MAzMF,qBAyMW;yBAkBH,KAAK/E,KAlBF;UAELgF,QAFK,gBAELA,QAFK;UAGLC,SAHK,gBAGLA,SAHK;UAILb,WAJK,gBAILA,WAJK;UAKLrG,SALK,gBAKLA,SALK;UAMLX,MANK,gBAMLA,MANK;UAOL8H,QAPK,gBAOLA,QAPK;UAQLC,gBARK,gBAQLA,gBARK;UASLC,YATK,gBASLA,YATK;UAULC,QAVK,gBAULA,QAVK;8CAWLC,OAXK;UAWLA,OAXK,qCAWKlH,cAXL;UAYLmH,gBAZK,gBAYLA,gBAZK;UAaLC,YAbK,gBAaLA,YAbK;UAcLnB,QAdK,gBAcLA,QAdK;UAeLnH,KAfK,gBAeLA,KAfK;UAgBLuI,cAhBK,gBAgBLA,cAhBK;UAiBLtI,KAjBK,gBAiBLA,KAjBK;UAmBCmD,WAnBD,GAmBiB,KAAKF,KAnBtB,CAmBCE,WAnBD;;kCAwBH,KAAKoF,2BAAL,EAxBG;UAsBLC,gBAtBK;UAuBLC,eAvBK;;kCAyB+B,KAAKC,yBAAL,EAzB/B;UAyBAC,aAzBA;UAyBeC,YAzBf;;UA2BDC,KAAK,GAAG,EAAd;;UACI5B,WAAW,GAAG,CAAd,IAAmBC,QAAvB,EAAiC;aAE7B,IAAI9F,SAAQ,GAAGuH,aADjB,EAEEvH,SAAQ,IAAIwH,YAFd,EAGExH,SAAQ,EAHV,EAIE;eAEE,IAAIF,YAAW,GAAGsH,gBADpB,EAEEtH,YAAW,IAAIuH,eAFjB,EAGEvH,YAAW,EAHb,EAIE;YACA2H,KAAK,CAACC,IAAN,CACEhJ,mBAAa,CAAC+H,QAAD,EAAW;cACtB3G,WAAW,EAAXA,YADsB;cAEtBC,IAAI,EAAE+G,QAFgB;cAGtB/E,WAAW,EAAEmF,cAAc,GAAGnF,WAAH,GAAiB+B,SAHtB;cAItBN,GAAG,EAAEuD,OAAO,CAAC;gBAAEjH,WAAW,EAAXA,YAAF;gBAAeC,IAAI,EAAE+G,QAArB;gBAA+B9G,QAAQ,EAARA;eAAhC,CAJU;cAKtBA,QAAQ,EAARA,SALsB;cAMtBrB,KAAK,EAAE,KAAKwE,aAAL,CAAmBnD,SAAnB,EAA6BF,YAA7B;aANI,CADf;;;OAvCC;;;;UAuDDkG,oBAAoB,GAAGlF,uBAAuB,CAClD,KAAKW,KAD6C,EAElD,KAAKC,cAF6C,CAApD;UAIMuE,mBAAmB,GAAGlF,sBAAsB,CAChD,KAAKU,KAD2C,EAEhD,KAAKC,cAF2C,CAAlD;aAKOhD,mBAAa,CAClBsI,gBAAgB,IAAIC,YAApB,IAAoC,KADlB,EAElB;QACEP,SAAS,EAATA,SADF;QAEExD,QAAQ,EAAE,KAAKkB,SAFjB;QAGEe,GAAG,EAAE,KAAKD,eAHZ;QAIEvG,KAAK;UACHiF,QAAQ,EAAE,UADP;UAEH/E,MAAM,EAANA,MAFG;UAGHD,KAAK,EAALA,KAHG;UAIHE,QAAQ,EAAE,MAJP;UAKH6I,uBAAuB,EAAE,OALtB;UAMHC,UAAU,EAAE,WANT;UAOHpI,SAAS,EAATA;WACGb,KARA;OANW,EAiBlBD,mBAAa,CAACkI,gBAAgB,IAAIC,YAApB,IAAoC,KAArC,EAA4C;QACvDJ,QAAQ,EAAEgB,KAD6C;QAEvDtC,GAAG,EAAEwB,QAFkD;QAGvDhI,KAAK,EAAE;UACLE,MAAM,EAAEmH,oBADH;UAEL6B,aAAa,EAAE9F,WAAW,GAAG,MAAH,GAAY+B,SAFjC;UAGLlF,KAAK,EAAEqH;;OANE,CAjBK,CAApB;KAzQJ;;WA+VEI,mBA/VF,kCA+VwB;yBACyC,KAAK5E,KAD9C;UACZoE,WADY,gBACZA,WADY;UACC7C,eADD,gBACCA,eADD;UACkBE,QADlB,gBACkBA,QADlB;UAC4B4C,QAD5B,gBAC4BA,QAD5B;;UAGhB,OAAO9C,eAAP,KAA2B,UAA/B,EAA2C;YACrC6C,WAAW,GAAG,CAAd,IAAmBC,QAAQ,GAAG,CAAlC,EAAqC;uCAM/B,KAAKqB,2BAAL,EAN+B;cAEjC3E,yBAFiC;cAGjCC,wBAHiC;cAIjCG,wBAJiC;cAKjCC,uBALiC;;uCAY/B,KAAKyE,yBAAL,EAZ+B;cAQjC5E,sBARiC;cASjCC,qBATiC;cAUjCG,qBAViC;cAWjCC,oBAXiC;;eAa9BT,oBAAL,CACEE,yBADF,EAEEC,wBAFF,EAGEC,sBAHF,EAIEC,qBAJF,EAKEC,wBALF,EAMEC,uBANF,EAOEC,qBAPF,EAQEC,oBARF;;;;UAaA,OAAOG,QAAP,KAAoB,UAAxB,EAAoC;2BAO9B,KAAKrB,KAPyB;YAEhCG,0BAFgC,gBAEhCA,yBAFgC;YAGhCrC,WAHgC,gBAGhCA,UAHgC;YAIhCuC,UAJgC,gBAIhCA,SAJgC;YAKhCE,yBALgC,gBAKhCA,wBALgC;YAMhCC,wBANgC,gBAMhCA,uBANgC;;aAQ7BY,aAAL,CACEtD,WADF,EAEEuC,UAFF,EAGEF,0BAHF,EAIEK,wBAJF,EAKED,yBALF;;KArYN;;;;;;WAybE+E,2BAzbF,0CAybkE;yBAO1D,KAAK1F,KAPqD;UAE5DoE,WAF4D,gBAE5DA,WAF4D;UAG5DiC,mBAH4D,gBAG5DA,mBAH4D;UAI5DC,oBAJ4D,gBAI5DA,oBAJ4D;UAK5DC,aAL4D,gBAK5DA,aAL4D;UAM5DlC,QAN4D,gBAM5DA,QAN4D;yBAQC,KAAKjE,KARN;UAQtDG,yBARsD,gBAQtDA,yBARsD;UAQ3BD,WAR2B,gBAQ3BA,WAR2B;UAQdpC,UARc,gBAQdA,UARc;UAUxDsI,qBAA6B,GACjCH,mBAAmB,IAAIC,oBAAvB,IAA+CC,aAA/C,IAAgE,CADlE;;UAGInC,WAAW,KAAK,CAAhB,IAAqBC,QAAQ,KAAK,CAAtC,EAAyC;eAChC,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,CAAP;;;UAGIoC,UAAU,GAAGvH,4BAA4B,CAC7C,KAAKc,KADwC,EAE7C9B,UAF6C,EAG7C,KAAK+B,cAHwC,CAA/C;UAKMyG,SAAS,GAAGvH,+BAA+B,CAC/C,KAAKa,KAD0C,EAE/CyG,UAF+C,EAG/CvI,UAH+C,EAI/C,KAAK+B,cAJ0C,CAAjD,CAtB8D;;;UA+BxD0G,gBAAgB,GACpB,CAACrG,WAAD,IAAgBC,yBAAyB,KAAK,UAA9C,GACI6C,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYmD,qBAAZ,CADJ,GAEI,CAHN;UAIMI,eAAe,GACnB,CAACtG,WAAD,IAAgBC,yBAAyB,KAAK,SAA9C,GACI6C,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYmD,qBAAZ,CADJ,GAEI,CAHN;aAKO,CACLpD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYoD,UAAU,GAAGE,gBAAzB,CADK,EAELvD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAASc,WAAW,GAAG,CAAvB,EAA0BsC,SAAS,GAAGE,eAAtC,CAAZ,CAFK,EAGLH,UAHK,EAILC,SAJK,CAAP;KAjeJ;;WAyeEb,yBAzeF,wCAyegE;yBAOxD,KAAK7F,KAPmD;UAE1DoE,WAF0D,gBAE1DA,WAF0D;UAG1DmC,aAH0D,gBAG1DA,aAH0D;UAI1DM,gBAJ0D,gBAI1DA,gBAJ0D;UAK1DC,iBAL0D,gBAK1DA,iBAL0D;UAM1DzC,QAN0D,gBAM1DA,QAN0D;yBAQA,KAAKjE,KARL;UAQpDE,WARoD,gBAQpDA,WARoD;UAQvCM,uBARuC,gBAQvCA,uBARuC;UAQdH,SARc,gBAQdA,SARc;UAUtD+F,qBAA6B,GACjCK,gBAAgB,IAAIC,iBAApB,IAAyCP,aAAzC,IAA0D,CAD5D;;UAGInC,WAAW,KAAK,CAAhB,IAAqBC,QAAQ,KAAK,CAAtC,EAAyC;eAChC,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,CAAP;;;UAGIoC,UAAU,GAAG9G,yBAAyB,CAC1C,KAAKK,KADqC,EAE1CS,SAF0C,EAG1C,KAAKR,cAHqC,CAA5C;UAKMyG,SAAS,GAAG9G,4BAA4B,CAC5C,KAAKI,KADuC,EAE5CyG,UAF4C,EAG5ChG,SAH4C,EAI5C,KAAKR,cAJuC,CAA9C,CAtB4D;;;UA+BtD0G,gBAAgB,GACpB,CAACrG,WAAD,IAAgBM,uBAAuB,KAAK,UAA5C,GACIwC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYmD,qBAAZ,CADJ,GAEI,CAHN;UAIMI,eAAe,GACnB,CAACtG,WAAD,IAAgBM,uBAAuB,KAAK,SAA5C,GACIwC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYmD,qBAAZ,CADJ,GAEI,CAHN;aAKO,CACLpD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYoD,UAAU,GAAGE,gBAAzB,CADK,EAELvD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAASe,QAAQ,GAAG,CAApB,EAAuBqC,SAAS,GAAGE,eAAnC,CAAZ,CAFK,EAGLH,UAHK,EAILC,SAJK,CAAP;KAjhBJ;;;IAA6BK,mBAA7B,UAKSC,YALT,GAKwB;IACpBjJ,SAAS,EAAE,KADS;IAEpBsH,QAAQ,EAAEhD,SAFU;IAGpBoD,cAAc,EAAE;GARpB;;;AA6nBF,IAAMzB,mBAAmB,GAAG,SAAtBA,mBAAsB,eAajB;MAXPgB,QAWO,SAXPA,QAWO;MAVPjH,SAUO,SAVPA,SAUO;MATPX,MASO,SATPA,MASO;MARPgI,YAQO,SARPA,YAQO;MAPPI,YAOO,SAPPA,YAOO;MANPc,oBAMO,SANPA,oBAMO;MALPC,aAKO,SALPA,aAKO;MAJPO,iBAIO,SAJPA,iBAIO;MAHP3J,KAGO,SAHPA,KAGO;MADPkD,QACO,SADPA,QACO;;MACL1B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;QACrC,OAAO0H,aAAP,KAAyB,QAA7B,EAAuC;UACjC/H,wBAAwB,IAAI,CAACA,wBAAwB,CAACyI,GAAzB,CAA6B5G,QAA7B,CAAjC,EAAyE;QACvE7B,wBAAwB,CAAC0I,GAAzB,CAA6B7G,QAA7B;QACA8G,OAAO,CAACC,IAAR,CACE,iDACE,wEAFJ;;;;QAQF,OAAOd,oBAAP,KAAgC,QAAhC,IACA,OAAOQ,iBAAP,KAA6B,QAF/B,EAGE;UAEErI,mCAAmC,IACnC,CAACA,mCAAmC,CAACwI,GAApC,CAAwC5G,QAAxC,CAFH,EAGE;QACA5B,mCAAmC,CAACyI,GAApC,CAAwC7G,QAAxC;QACA8G,OAAO,CAACC,IAAR,CACE,gFACE,wEAFJ;;;;QAOAhC,YAAY,IAAI,IAAhB,IAAwBI,YAAY,IAAI,IAA5C,EAAkD;UAC5C9G,kBAAkB,IAAI,CAACA,kBAAkB,CAACuI,GAAnB,CAAuB5G,QAAvB,CAA3B,EAA6D;QAC3D3B,kBAAkB,CAACwI,GAAnB,CAAuB7G,QAAvB;QACA8G,OAAO,CAACC,IAAR,CACE,mEACE,qEAFJ;;;;QAOApC,QAAQ,IAAI,IAAhB,EAAsB;YACdqC,KAAK,CACT,oDACE,qCADF,YAEMrC,QAAQ,KAAK,IAAb,GAAoB,MAApB,GAA6B,OAAOA,QAF1C,wBADS,CAAX;;;YAOMjH,SAAR;WACO,KAAL;WACK,KAAL;;;;;cAIQsJ,KAAK,CACT,qDACE,yCADF,WAEMtJ,SAFN,uBADS,CAAX;;;QAOA,OAAOZ,KAAP,KAAiB,QAArB,EAA+B;YACvBkK,KAAK,CACT,iDACE,yCADF,YAEMlK,KAAK,KAAK,IAAV,GAAiB,MAAjB,GAA0B,OAAOA,KAFvC,wBADS,CAAX;;;QAOE,OAAOC,MAAP,KAAkB,QAAtB,EAAgC;YACxBiK,KAAK,CACT,kDACE,0CADF,YAEMjK,MAAM,KAAK,IAAX,GAAkB,MAAlB,GAA2B,OAAOA,MAFxC,wBADS,CAAX;;;CAjFN;;ACxzBA,IAAMkK,2BAA2B,GAAG,EAApC;;AAyBA,IAAMjI,uBAAuB,GAAG,SAA1BA,uBAA0B,cAG3B;MAFDgF,QAEC,QAFDA,QAEC;MADDkD,cACC,SADDA,cACC;MADeC,kBACf,SADeA,kBACf;MADmCC,oBACnC,SADmCA,oBACnC;MACCC,uBAAuB,GAAG,CAA9B,CADG;;;MAKCD,oBAAoB,IAAIpD,QAA5B,EAAsC;IACpCoD,oBAAoB,GAAGpD,QAAQ,GAAG,CAAlC;;;MAGEoD,oBAAoB,IAAI,CAA5B,EAA+B;QACvBE,YAAY,GAAGJ,cAAc,CAACE,oBAAD,CAAnC;IACAC,uBAAuB,GAAGC,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAA7D;;;MAGIgL,kBAAkB,GAAGvD,QAAQ,GAAGoD,oBAAX,GAAkC,CAA7D;MACMI,0BAA0B,GAAGD,kBAAkB,GAAGJ,kBAAxD;SAEOE,uBAAuB,GAAGG,0BAAjC;CApBF;;AAuBA,IAAMvI,sBAAsB,GAAG,SAAzBA,sBAAyB,eAO1B;MAND8E,WAMC,SANDA,WAMC;MAJD0D,iBAIC,SAJDA,iBAIC;MAHDC,oBAGC,SAHDA,oBAGC;MAFDC,uBAEC,SAFDA,uBAEC;MACCN,uBAAuB,GAAG,CAA9B,CADG;;;MAKCM,uBAAuB,IAAI5D,WAA/B,EAA4C;IAC1C4D,uBAAuB,GAAG5D,WAAW,GAAG,CAAxC;;;MAGE4D,uBAAuB,IAAI,CAA/B,EAAkC;QAC1BL,YAAY,GAAGG,iBAAiB,CAACE,uBAAD,CAAtC;IACAN,uBAAuB,GAAGC,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAA7D;;;MAGIgL,kBAAkB,GAAGxD,WAAW,GAAG4D,uBAAd,GAAwC,CAAnE;MACMH,0BAA0B,GAAGD,kBAAkB,GAAGG,oBAAxD;SAEOL,uBAAuB,GAAGG,0BAAjC;CAxBF;;AA2BA,IAAMI,eAAe,GAAG,SAAlBA,eAAkB,CACtBC,QADsB,EAEtBlI,KAFsB,EAGtBmI,KAHsB,EAItBC,aAJsB,EAKL;MACbC,eAAJ,EAAqBC,QAArB,EAA+BC,iBAA/B;;MACIL,QAAQ,KAAK,QAAjB,EAA2B;IACzBG,eAAe,GAAGD,aAAa,CAACN,iBAAhC;IACAQ,QAAQ,GAAKtI,KAAK,CAAC2B,WAAnB;IACA4G,iBAAiB,GAAGH,aAAa,CAACJ,uBAAlC;GAHF,MAIO;IACLK,eAAe,GAAGD,aAAa,CAACb,cAAhC;IACAe,QAAQ,GAAKtI,KAAK,CAAC4B,SAAnB;IACA2G,iBAAiB,GAAGH,aAAa,CAACX,oBAAlC;;;MAGEU,KAAK,GAAGI,iBAAZ,EAA+B;QACzBtG,MAAM,GAAG,CAAb;;QACIsG,iBAAiB,IAAI,CAAzB,EAA4B;UACpBZ,YAAY,GAAGU,eAAe,CAACE,iBAAD,CAApC;MACAtG,MAAM,GAAG0F,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAA5C;;;SAGG,IAAI4L,CAAC,GAAGD,iBAAiB,GAAG,CAAjC,EAAoCC,CAAC,IAAIL,KAAzC,EAAgDK,CAAC,EAAjD,EAAqD;UAC/C5L,IAAI,GAAG0L,QAAQ,CAACE,CAAD,CAAnB;MAEAH,eAAe,CAACG,CAAD,CAAf,GAAqB;QACnBvG,MAAM,EAANA,MADmB;QAEnBrF,IAAI,EAAJA;OAFF;MAKAqF,MAAM,IAAIrF,IAAV;;;QAGEsL,QAAQ,KAAK,QAAjB,EAA2B;MACzBE,aAAa,CAACJ,uBAAd,GAAwCG,KAAxC;KADF,MAEO;MACLC,aAAa,CAACX,oBAAd,GAAqCU,KAArC;;;;SAIGE,eAAe,CAACF,KAAD,CAAtB;CA1CF;;AA6CA,IAAMM,eAAe,GAAG,SAAlBA,eAAkB,CACtBP,QADsB,EAEtBlI,KAFsB,EAGtBoI,aAHsB,EAItBnG,MAJsB,EAKnB;MACCoG,eAAJ,EAAqBE,iBAArB;;MACIL,QAAQ,KAAK,QAAjB,EAA2B;IACzBG,eAAe,GAAGD,aAAa,CAACN,iBAAhC;IACAS,iBAAiB,GAAGH,aAAa,CAACJ,uBAAlC;GAFF,MAGO;IACLK,eAAe,GAAGD,aAAa,CAACb,cAAhC;IACAgB,iBAAiB,GAAGH,aAAa,CAACX,oBAAlC;;;MAGIiB,sBAAsB,GAC1BH,iBAAiB,GAAG,CAApB,GAAwBF,eAAe,CAACE,iBAAD,CAAf,CAAmCtG,MAA3D,GAAoE,CADtE;;MAGIyG,sBAAsB,IAAIzG,MAA9B,EAAsC;;WAE7B0G,2BAA2B,CAChCT,QADgC,EAEhClI,KAFgC,EAGhCoI,aAHgC,EAIhCG,iBAJgC,EAKhC,CALgC,EAMhCtG,MANgC,CAAlC;GAFF,MAUO;;;;WAIE2G,gCAAgC,CACrCV,QADqC,EAErClI,KAFqC,EAGrCoI,aAHqC,EAIrChF,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYkF,iBAAZ,CAJqC,EAKrCtG,MALqC,CAAvC;;CAhCJ;;AA0CA,IAAM0G,2BAA2B,GAAG,SAA9BA,2BAA8B,CAClCT,QADkC,EAElClI,KAFkC,EAGlCoI,aAHkC,EAIlCS,IAJkC,EAKlCC,GALkC,EAMlC7G,MANkC,EAOvB;SACJ6G,GAAG,IAAID,IAAd,EAAoB;QACZE,MAAM,GAAGD,GAAG,GAAG1F,IAAI,CAAC4F,KAAL,CAAW,CAACH,IAAI,GAAGC,GAAR,IAAe,CAA1B,CAArB;QACMG,aAAa,GAAGhB,eAAe,CACnCC,QADmC,EAEnClI,KAFmC,EAGnC+I,MAHmC,EAInCX,aAJmC,CAAf,CAKpBnG,MALF;;QAOIgH,aAAa,KAAKhH,MAAtB,EAA8B;aACrB8G,MAAP;KADF,MAEO,IAAIE,aAAa,GAAGhH,MAApB,EAA4B;MACjC6G,GAAG,GAAGC,MAAM,GAAG,CAAf;KADK,MAEA,IAAIE,aAAa,GAAGhH,MAApB,EAA4B;MACjC4G,IAAI,GAAGE,MAAM,GAAG,CAAhB;;;;MAIAD,GAAG,GAAG,CAAV,EAAa;WACJA,GAAG,GAAG,CAAb;GADF,MAEO;WACE,CAAP;;CA7BJ;;AAiCA,IAAMF,gCAAgC,GAAG,SAAnCA,gCAAmC,CACvCV,QADuC,EAEvClI,KAFuC,EAGvCoI,aAHuC,EAIvCD,KAJuC,EAKvClG,MALuC,EAM5B;MACLiH,SAAS,GAAGhB,QAAQ,KAAK,QAAb,GAAwBlI,KAAK,CAACoE,WAA9B,GAA4CpE,KAAK,CAACqE,QAApE;MACI8E,QAAQ,GAAG,CAAf;;SAGEhB,KAAK,GAAGe,SAAR,IACAjB,eAAe,CAACC,QAAD,EAAWlI,KAAX,EAAkBmI,KAAlB,EAAyBC,aAAzB,CAAf,CAAuDnG,MAAvD,GAAgEA,MAFlE,EAGE;IACAkG,KAAK,IAAIgB,QAAT;IACAA,QAAQ,IAAI,CAAZ;;;SAGKR,2BAA2B,CAChCT,QADgC,EAEhClI,KAFgC,EAGhCoI,aAHgC,EAIhChF,IAAI,CAACE,GAAL,CAAS6E,KAAT,EAAgBe,SAAS,GAAG,CAA5B,CAJgC,EAKhC9F,IAAI,CAAC4F,KAAL,CAAWb,KAAK,GAAG,CAAnB,CALgC,EAMhClG,MANgC,CAAlC;CAlBF;;AA4BA,IAAMmH,6BAA6B,GAAG,SAAhCA,6BAAgC,CACpClB,QADoC,EAEpClI,KAFoC,EAGpCmI,KAHoC,EAIpChE,KAJoC,EAKpCkF,YALoC,EAMpCjB,aANoC,EAOpC9D,aAPoC,EAQzB;MACL1H,IAAI,GAAGsL,QAAQ,KAAK,QAAb,GAAwBlI,KAAK,CAAC7C,KAA9B,GAAsC6C,KAAK,CAAC5C,MAAzD;MACMuK,YAAY,GAAGM,eAAe,CAACC,QAAD,EAAWlI,KAAX,EAAkBmI,KAAlB,EAAyBC,aAAzB,CAApC,CAFW;;;MAMLkB,kBAAkB,GACtBpB,QAAQ,KAAK,QAAb,GACI5I,sBAAsB,CAACU,KAAD,EAAQoI,aAAR,CAD1B,GAEI/I,uBAAuB,CAACW,KAAD,EAAQoI,aAAR,CAH7B;MAKMmB,SAAS,GAAGnG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhBD,IAAI,CAACE,GAAL,CAASgG,kBAAkB,GAAG1M,IAA9B,EAAoC+K,YAAY,CAAC1F,MAAjD,CAFgB,CAAlB;MAIMuH,SAAS,GAAGpG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhBsE,YAAY,CAAC1F,MAAb,GAAsBrF,IAAtB,GAA6B0H,aAA7B,GAA6CqD,YAAY,CAAC/K,IAF1C,CAAlB;;MAKIuH,KAAK,KAAK,OAAd,EAAuB;QACjBkF,YAAY,IAAIG,SAAS,GAAG5M,IAA5B,IAAoCyM,YAAY,IAAIE,SAAS,GAAG3M,IAApE,EAA0E;MACxEuH,KAAK,GAAG,MAAR;KADF,MAEO;MACLA,KAAK,GAAG,QAAR;;;;UAIIA,KAAR;SACO,OAAL;aACSoF,SAAP;;SACG,KAAL;aACSC,SAAP;;SACG,QAAL;aACSpG,IAAI,CAACqG,KAAL,CAAWD,SAAS,GAAG,CAACD,SAAS,GAAGC,SAAb,IAA0B,CAAjD,CAAP;;SACG,MAAL;;UAEMH,YAAY,IAAIG,SAAhB,IAA6BH,YAAY,IAAIE,SAAjD,EAA4D;eACnDF,YAAP;OADF,MAEO,IAAIG,SAAS,GAAGD,SAAhB,EAA2B;;;eAGzBC,SAAP;OAHK,MAIA,IAAIH,YAAY,GAAGG,SAAnB,EAA8B;eAC5BA,SAAP;OADK,MAEA;eACED,SAAP;;;;CAtDR;;AA2DA,IAAMG,gBAAgB;;AAAG1K,mBAAmB,CAAC;EAC3CC,eAAe,EAAE,yBACfe,KADe,EAEfmI,KAFe,EAGfC,aAHe;WAIJH,eAAe,CAAC,QAAD,EAAWjI,KAAX,EAAkBmI,KAAlB,EAAyBC,aAAzB,CAAf,CAAuDnG,MAJnD;GAD0B;EAO3C/C,4BAA4B,EAAE,sCAC5Bc,KAD4B,EAE5B9B,UAF4B,EAG5BkK,aAH4B;WAIjBK,eAAe,CAAC,QAAD,EAAWzI,KAAX,EAAkBoI,aAAlB,EAAiClK,UAAjC,CAJE;GAPa;EAa3CiB,+BAA+B,EAAE,yCAC/Ba,KAD+B,EAE/ByG,UAF+B,EAG/BvI,UAH+B,EAI/BkK,aAJ+B,EAKpB;QACHhE,WADG,GACoBpE,KADpB,CACHoE,WADG;QACUjH,KADV,GACoB6C,KADpB,CACU7C,KADV;QAGLwK,YAAY,GAAGM,eAAe,CAClC,QADkC,EAElCjI,KAFkC,EAGlCyG,UAHkC,EAIlC2B,aAJkC,CAApC;QAMMmB,SAAS,GAAGrL,UAAU,GAAGf,KAA/B;QAEI8E,MAAM,GAAG0F,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAAhD;QACI8J,SAAS,GAAGD,UAAhB;;WAEOC,SAAS,GAAGtC,WAAW,GAAG,CAA1B,IAA+BnC,MAAM,GAAGsH,SAA/C,EAA0D;MACxD7C,SAAS;MACTzE,MAAM,IAAIgG,eAAe,CAAC,QAAD,EAAWjI,KAAX,EAAkB0G,SAAlB,EAA6B0B,aAA7B,CAAf,CAA2DxL,IAArE;;;WAGK8J,SAAP;GArCyC;EAwC3CtH,cAAc,EAAE,wBACdY,KADc,EAEdmI,KAFc,EAGdC,aAHc;WAIHA,aAAa,CAACN,iBAAd,CAAgCK,KAAhC,EAAuCvL,IAJpC;GAxC2B;EA8C3CyC,uBAAuB,EAAvBA,uBA9C2C;EA+C3CC,sBAAsB,EAAtBA,sBA/C2C;EAiD3CC,8BAA8B,EAAE,wCAC9BS,KAD8B,EAE9BmI,KAF8B,EAG9BhE,KAH8B,EAI9BkF,YAJ8B,EAK9BjB,aAL8B,EAM9B9D,aAN8B;WAQ9B8E,6BAA6B,CAC3B,QAD2B,EAE3BpJ,KAF2B,EAG3BmI,KAH2B,EAI3BhE,KAJ2B,EAK3BkF,YAL2B,EAM3BjB,aAN2B,EAO3B9D,aAP2B,CARC;GAjDW;EAmE3C9E,2BAA2B,EAAE,qCAC3BQ,KAD2B,EAE3BmI,KAF2B,EAG3BhE,KAH2B,EAI3BkF,YAJ2B,EAK3BjB,aAL2B,EAM3B9D,aAN2B;WAQ3B8E,6BAA6B,CAC3B,KAD2B,EAE3BpJ,KAF2B,EAG3BmI,KAH2B,EAI3BhE,KAJ2B,EAK3BkF,YAL2B,EAM3BjB,aAN2B,EAO3B9D,aAP2B,CARF;GAnEc;EAqF3C5E,YAAY,EAAE,sBACZM,KADY,EAEZmI,KAFY,EAGZC,aAHY;WAIDH,eAAe,CAAC,KAAD,EAAQjI,KAAR,EAAemI,KAAf,EAAsBC,aAAtB,CAAf,CAAoDnG,MAJnD;GArF6B;EA2F3CxC,YAAY,EAAE,sBACZO,KADY,EAEZmI,KAFY,EAGZC,aAHY;WAIDA,aAAa,CAACb,cAAd,CAA6BY,KAA7B,EAAoCvL,IAJnC;GA3F6B;EAiG3C+C,yBAAyB,EAAE,mCACzBK,KADyB,EAEzBS,SAFyB,EAGzB2H,aAHyB;WAIdK,eAAe,CAAC,KAAD,EAAQzI,KAAR,EAAeoI,aAAf,EAA8B3H,SAA9B,CAJD;GAjGgB;EAuG3Cb,4BAA4B,EAAE,sCAC5BI,KAD4B,EAE5ByG,UAF4B,EAG5BhG,SAH4B,EAI5B2H,aAJ4B,EAKjB;QACH/D,QADG,GACkBrE,KADlB,CACHqE,QADG;QACOjH,MADP,GACkB4C,KADlB,CACO5C,MADP;QAGLuK,YAAY,GAAGM,eAAe,CAClC,KADkC,EAElCjI,KAFkC,EAGlCyG,UAHkC,EAIlC2B,aAJkC,CAApC;QAMMmB,SAAS,GAAG9I,SAAS,GAAGrD,MAA9B;QAEI6E,MAAM,GAAG0F,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAAhD;QACI8J,SAAS,GAAGD,UAAhB;;WAEOC,SAAS,GAAGrC,QAAQ,GAAG,CAAvB,IAA4BpC,MAAM,GAAGsH,SAA5C,EAAuD;MACrD7C,SAAS;MACTzE,MAAM,IAAIgG,eAAe,CAAC,KAAD,EAAQjI,KAAR,EAAe0G,SAAf,EAA0B0B,aAA1B,CAAf,CAAwDxL,IAAlE;;;WAGK8J,SAAP;GA/HyC;EAkI3C7G,iBAlI2C,6BAkIzBG,KAlIyB,EAkINK,QAlIM,EAkIwB;gBAI3DL,KAJ2D;QAE/D+H,oBAF+D,SAE/DA,oBAF+D;QAG/DP,kBAH+D,SAG/DA,kBAH+D;QAM3DY,aAAa,GAAG;MACpBN,iBAAiB,EAAE,EADC;MAEpBC,oBAAoB,EAAEA,oBAAoB,IAAIT,2BAF1B;MAGpBE,kBAAkB,EAAEA,kBAAkB,IAAIF,2BAHtB;MAIpBU,uBAAuB,EAAE,CAAC,CAJN;MAKpBP,oBAAoB,EAAE,CAAC,CALH;MAMpBF,cAAc,EAAE;KANlB;;IASAlH,QAAQ,CAACsJ,qBAAT,GAAiC,UAC/BtL,WAD+B,EAE/BuL,iBAF+B,EAG5B;UADHA,iBACG;QADHA,iBACG,GAD2B,IAC3B;;;MACHvJ,QAAQ,CAACwJ,iBAAT,CAA2B;QAAExL,WAAW,EAAXA,WAAF;QAAeuL,iBAAiB,EAAjBA;OAA1C;KAJF;;IAOAvJ,QAAQ,CAACyJ,kBAAT,GAA8B,UAC5BvL,QAD4B,EAE5BqL,iBAF4B,EAGzB;UADHA,iBACG;QADHA,iBACG,GAD2B,IAC3B;;;MACHvJ,QAAQ,CAACwJ,iBAAT,CAA2B;QAAEtL,QAAQ,EAARA,QAAF;QAAYqL,iBAAiB,EAAjBA;OAAvC;KAJF;;IAOAvJ,QAAQ,CAACwJ,iBAAT,GAA6B,iBAQvB;UAPJxL,WAOI,SAPJA,WAOI;UANJE,QAMI,SANJA,QAMI;wCALJqL,iBAKI;UALJA,iBAKI,sCALgB,IAKhB;;UACA,OAAOvL,WAAP,KAAuB,QAA3B,EAAqC;QACnC+J,aAAa,CAACJ,uBAAd,GAAwC5E,IAAI,CAACE,GAAL,CACtC8E,aAAa,CAACJ,uBADwB,EAEtC3J,WAAW,GAAG,CAFwB,CAAxC;;;UAKE,OAAOE,QAAP,KAAoB,QAAxB,EAAkC;QAChC6J,aAAa,CAACX,oBAAd,GAAqCrE,IAAI,CAACE,GAAL,CACnC8E,aAAa,CAACX,oBADqB,EAEnClJ,QAAQ,GAAG,CAFwB,CAArC;OARE;;;;;;MAkBJ8B,QAAQ,CAACyB,kBAAT,CAA4B,CAAC,CAA7B;;UAEI8H,iBAAJ,EAAuB;QACrBvJ,QAAQ,CAAC0J,WAAT;;KA7BJ;;WAiCO3B,aAAP;GAhMyC;EAmM3CtI,qCAAqC,EAAE,KAnMI;EAqM3CC,aAAa,EAAE,8BAAkD;QAA/C4B,WAA+C,SAA/CA,WAA+C;QAAlCC,SAAkC,SAAlCA,SAAkC;;QAC3DjD,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;UACrC,OAAO8C,WAAP,KAAuB,UAA3B,EAAuC;cAC/B0F,KAAK,CACT,uDACE,8BADF,YAGI1F,WAAW,KAAK,IAAhB,GAAuB,MAAvB,GAAgC,OAAOA,WAH3C,wBADS,CAAX;OADF,MAQO,IAAI,OAAOC,SAAP,KAAqB,UAAzB,EAAqC;cACpCyF,KAAK,CACT,qDACE,8BADF,YAEMzF,SAAS,KAAK,IAAd,GAAqB,MAArB,GAA8B,OAAOA,SAF3C,wBADS,CAAX;;;;CAhNoC,CAA5C;;ACtKA,IAAMzD,gCAA8B,GAAG,GAAvC;;AAEA,IAAMC,gBAAc,GAAG,SAAjBA,cAAiB,CAAC+J,KAAD,EAAgB7J,IAAhB;SAA8B6J,KAA9B;CAAvB;;;;AAIA,IAAI6B,oBAAoB,GAAG,IAA3B;AACA,IAAItL,oBAAkB,GAAG,IAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;MACrC,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,OAAOA,MAAM,CAACC,OAAd,KAA0B,WAA/D,EAA4E;IAC1EiL,oBAAoB;;QAAOjL,OAAJ,EAAvB;IACAL,oBAAkB;;QAAOK,OAAJ,EAArB;;;;AAIJ,AAAe,SAASkL,mBAAT,OAoBX;;;MAnBFC,aAmBE,QAnBFA,aAmBE;MAlBFC,qBAkBE,QAlBFA,qBAkBE;MAjBFC,WAiBE,QAjBFA,WAiBE;MAhBFhB,6BAgBE,QAhBFA,6BAgBE;MAfFiB,sBAeE,QAfFA,sBAeE;MAdFC,yBAcE,QAdFA,yBAcE;MAbFzK,iBAaE,QAbFA,iBAaE;MAZFC,qCAYE,QAZFA,qCAYE;MAXFC,aAWE,QAXFA,aAWE;;;;;;;;;kBA4BYC,KAAZ,EAA6B;;;wCACrBA,KAAN;YA3BFC,cA0B6B,GA1BPJ,iBAAiB,CAAC,MAAKG,KAAN,wDA0BV;YAzB7BG,SAyB6B;YAxB7BD,0BAwB6B,GAxBkB,IAwBlB;YAd7BE,KAc6B,GAdd;QACbC,QAAQ,uDADK;QAEbC,WAAW,EAAE,KAFA;QAGbiK,eAAe,EAAE,SAHJ;QAIblB,YAAY,EACV,OAAO,MAAKrJ,KAAL,CAAWwK,mBAAlB,KAA0C,QAA1C,GACI,MAAKxK,KAAL,CAAWwK,mBADf,GAEI,CAPO;QAQb7J,wBAAwB,EAAE;OAMC;YA0L7BE,oBA1L6B;YAgM7BA,oBAhM6B,GAgMNC,UAAU,CAC/B,UACE2J,kBADF,EAEEC,iBAFF,EAGEC,iBAHF,EAIEC,gBAJF;eAMI,MAAK5K,KAAL,CAAWuB,eAAb,CAA6D;UAC3DkJ,kBAAkB,EAAlBA,kBAD2D;UAE3DC,iBAAiB,EAAjBA,iBAF2D;UAG3DC,iBAAiB,EAAjBA,iBAH2D;UAI3DC,gBAAgB,EAAhBA;SAJF,CANF;OAD+B,CAhMJ;YA+M7BpJ,aA/M6B;YAoN7BA,aApN6B,GAoNbV,UAAU,CACxB,UACEyJ,eADF,EAEElB,YAFF,EAGE1I,wBAHF;eAKI,MAAKX,KAAL,CAAWyB,QAAb,CAA+C;UAC7C8I,eAAe,EAAfA,eAD6C;UAE7ClB,YAAY,EAAZA,YAF6C;UAG7C1I,wBAAwB,EAAxBA;SAHF,CALF;OADwB,CApNG;YAsQ7Be,aAtQ6B;;YAuQ7BA,aAvQ6B,GAuQb,UAACyG,KAAD,EAA2B;0BACD,MAAKnI,KADJ;YACjCjC,SADiC,eACjCA,SADiC;YACtBuK,QADsB,eACtBA,QADsB;YACZuC,MADY,eACZA,MADY;;YAGnChJ,cAAc,GAAG,MAAKC,kBAAL,CACrBhC,qCAAqC,IAAIwI,QADpB,EAErBxI,qCAAqC,IAAI+K,MAFpB,EAGrB/K,qCAAqC,IAAI/B,SAHpB,CAAvB;;YAMIb,KAAJ;;YACI2E,cAAc,CAACG,cAAf,CAA8BmG,KAA9B,CAAJ,EAA0C;UACxCjL,KAAK,GAAG2E,cAAc,CAACsG,KAAD,CAAtB;SADF,MAEO;cACClG,OAAM,GAAGiI,aAAa,CAAC,MAAKlK,KAAN,EAAamI,KAAb,EAAoB,MAAKlI,cAAzB,CAA5B;;cACMrD,IAAI,GAAGwN,WAAW,CAAC,MAAKpK,KAAN,EAAamI,KAAb,EAAoB,MAAKlI,cAAzB,CAAxB,CAFK;;cAKC6K,YAAY,GAChB/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAD3C;cAGM3I,KAAK,GAAGnE,SAAS,KAAK,KAA5B;cACMgN,gBAAgB,GAAGD,YAAY,GAAG7I,OAAH,GAAY,CAAjD;UACAJ,cAAc,CAACsG,KAAD,CAAd,GAAwBjL,KAAK,GAAG;YAC9BiF,QAAQ,EAAE,UADoB;YAE9BC,IAAI,EAAEF,KAAK,GAAGG,SAAH,GAAe0I,gBAFI;YAG9BzI,KAAK,EAAEJ,KAAK,GAAG6I,gBAAH,GAAsB1I,SAHJ;YAI9BE,GAAG,EAAE,CAACuI,YAAD,GAAgB7I,OAAhB,GAAyB,CAJA;YAK9B7E,MAAM,EAAE,CAAC0N,YAAD,GAAgBlO,IAAhB,GAAuB,MALD;YAM9BO,KAAK,EAAE2N,YAAY,GAAGlO,IAAH,GAAU;WAN/B;;;eAUKM,KAAP;OAvS2B;;YA0S7B4E,kBA1S6B;YA2S7BA,kBA3S6B,GA2SRhB,UAAU,CAAC,UAAC0B,CAAD,EAASC,EAAT,EAAkBC,GAAlB;eAAgC,EAAhC;OAAD,CA3SF;;YAoV7BsI,mBApV6B,GAoVP,UAACpI,KAAD,EAA8B;mCACDA,KAAK,CAACC,aADL;YAC1CpF,WAD0C,wBAC1CA,WAD0C;YAC7BS,UAD6B,wBAC7BA,UAD6B;YACjB8E,WADiB,wBACjBA,WADiB;;cAE7CC,QAAL,CAAc,UAAAC,SAAS,EAAI;cACrBA,SAAS,CAACmG,YAAV,KAA2BnL,UAA/B,EAA2C;;;;mBAIlC,IAAP;;;cAGMH,SARiB,GAQH,MAAKiC,KARF,CAQjBjC,SARiB;cAUrBsL,YAAY,GAAGnL,UAAnB;;cACIH,SAAS,KAAK,KAAlB,EAAyB;;;;;oBAKfH,gBAAgB,EAAxB;mBACO,UAAL;gBACEyL,YAAY,GAAG,CAACnL,UAAhB;;;mBAEG,qBAAL;gBACEmL,YAAY,GAAGrG,WAAW,GAAGvF,WAAd,GAA4BS,UAA3C;;;WArBmB;;;UA2BzBmL,YAAY,GAAGjG,IAAI,CAACC,GAAL,CACb,CADa,EAEbD,IAAI,CAACE,GAAL,CAAS+F,YAAT,EAAuBrG,WAAW,GAAGvF,WAArC,CAFa,CAAf;iBAKO;YACL6C,WAAW,EAAE,IADR;YAELiK,eAAe,EACbrH,SAAS,CAACmG,YAAV,GAAyBnL,UAAzB,GAAsC,SAAtC,GAAkD,UAH/C;YAILmL,YAAY,EAAZA,YAJK;YAKL1I,wBAAwB,EAAE;WAL5B;SAhCF,EAuCG,MAAK6C,0BAvCR;OAtV2B;;YAgY7ByH,iBAhY6B,GAgYT,UAACrI,KAAD,EAA8B;oCACEA,KAAK,CAACC,aADR;YACxCC,YADwC,yBACxCA,YADwC;YAC1BC,YAD0B,yBAC1BA,YAD0B;YACZtC,SADY,yBACZA,SADY;;cAE3CwC,QAAL,CAAc,UAAAC,SAAS,EAAI;cACrBA,SAAS,CAACmG,YAAV,KAA2B5I,SAA/B,EAA0C;;;;mBAIjC,IAAP;WALuB;;;cASnB4I,YAAY,GAAGjG,IAAI,CAACC,GAAL,CACnB,CADmB,EAEnBD,IAAI,CAACE,GAAL,CAAS7C,SAAT,EAAoBsC,YAAY,GAAGD,YAAnC,CAFmB,CAArB;iBAKO;YACLxC,WAAW,EAAE,IADR;YAELiK,eAAe,EACbrH,SAAS,CAACmG,YAAV,GAAyBA,YAAzB,GAAwC,SAAxC,GAAoD,UAHjD;YAILA,YAAY,EAAZA,YAJK;YAKL1I,wBAAwB,EAAE;WAL5B;SAdF,EAqBG,MAAK6C,0BArBR;OAlY2B;;YA0Z7BC,eA1Z6B,GA0ZX,UAACC,GAAD,EAAoB;YAC5BC,QAD4B,GACf,MAAK3D,KADU,CAC5B2D,QAD4B;cAG/BxD,SAAL,GAAmBuD,GAAnB;;YAEI,OAAOC,QAAP,KAAoB,UAAxB,EAAoC;UAClCA,QAAQ,CAACD,GAAD,CAAR;SADF,MAEO,IACLC,QAAQ,IAAI,IAAZ,IACA,OAAOA,QAAP,KAAoB,QADpB,IAEAA,QAAQ,CAAC3B,cAAT,CAAwB,SAAxB,CAHK,EAIL;UACA2B,QAAQ,CAACC,OAAT,GAAmBF,GAAnB;;OAtayB;;YA0a7BF,0BA1a6B,GA0aA,YAAM;YAC7B,MAAKtD,0BAAL,KAAoC,IAAxC,EAA8C;UAC5CjE,aAAa,CAAC,MAAKiE,0BAAN,CAAb;;;cAGGA,0BAAL,GAAkC7D,cAAc,CAC9C,MAAKwH,iBADyC,EAE9C1F,gCAF8C,CAAhD;OA/a2B;;YAqb7B0F,iBArb6B,GAqbT,YAAM;cACnB3D,0BAAL,GAAkC,IAAlC;;cAEK+C,QAAL,CAAc;UAAE3C,WAAW,EAAE;SAA7B,EAAsC,YAAM;;;gBAGrCwB,kBAAL,CAAwB,CAAC,CAAzB,EAA4B,IAA5B;SAHF;OAxb2B;;;;;SAItBgC,wBA/BT,qCAgCIC,SAhCJ,EAiCIb,SAjCJ,EAkC0B;MACtBc,qBAAmB,CAACD,SAAD,EAAYb,SAAZ,CAAnB;MACAnD,aAAa,CAACgE,SAAD,CAAb;aACO,IAAP;KArCJ;;;;WAwCEE,QAxCF,qBAwCWoF,YAxCX,EAwCuC;MACnCA,YAAY,GAAGjG,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYgG,YAAZ,CAAf;WAEKpG,QAAL,CAAc,UAAAC,SAAS,EAAI;YACrBA,SAAS,CAACmG,YAAV,KAA2BA,YAA/B,EAA6C;iBACpC,IAAP;;;eAEK;UACLkB,eAAe,EACbrH,SAAS,CAACmG,YAAV,GAAyBA,YAAzB,GAAwC,SAAxC,GAAoD,UAFjD;UAGLA,YAAY,EAAEA,YAHT;UAIL1I,wBAAwB,EAAE;SAJ5B;OAJF,EAUG,KAAK6C,0BAVR;KA3CJ;;WAwDEU,YAxDF,yBAwDeiE,KAxDf,EAwD8BhE,KAxD9B,EAwDmE;UAArCA,KAAqC;QAArCA,KAAqC,GAAd,MAAc;;;UACvD+E,SADuD,GACzC,KAAKlJ,KADoC,CACvDkJ,SADuD;UAEvDG,YAFuD,GAEtC,KAAKjJ,KAFiC,CAEvDiJ,YAFuD;MAI/DlB,KAAK,GAAG/E,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS6E,KAAT,EAAgBe,SAAS,GAAG,CAA5B,CAAZ,CAAR;WAEKjF,QAAL,CACEmF,6BAA6B,CAC3B,KAAKpJ,KADsB,EAE3BmI,KAF2B,EAG3BhE,KAH2B,EAI3BkF,YAJ2B,EAK3B,KAAKpJ,cALsB,CAD/B;KA9DJ;;WAyEE0E,iBAzEF,gCAyEsB;yBACiC,KAAK3E,KADtC;UACVjC,SADU,gBACVA,SADU;UACCyM,mBADD,gBACCA,mBADD;UACsBK,MADtB,gBACsBA,MADtB;;UAGd,OAAOL,mBAAP,KAA+B,QAA/B,IAA2C,KAAKrK,SAAL,IAAkB,IAAjE,EAAuE;YAC/DwD,QAAQ,GAAK,KAAKxD,SAAxB,CADqE;;YAGjEpC,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA7C,EAA2D;UACzDlH,QAAQ,CAACzF,UAAT,GAAsBsM,mBAAtB;SADF,MAEO;UACL7G,QAAQ,CAAClD,SAAT,GAAqB+J,mBAArB;;;;WAIC5F,mBAAL;KAtFJ;;WAyFEC,kBAzFF,iCAyFuB;yBACW,KAAK7E,KADhB;UACXjC,SADW,gBACXA,SADW;UACA8M,MADA,gBACAA,MADA;wBAEgC,KAAKzK,KAFrC;UAEXiJ,YAFW,eAEXA,YAFW;UAEG1I,wBAFH,eAEGA,wBAFH;;UAIfA,wBAAwB,IAAI,KAAKR,SAAL,IAAkB,IAAlD,EAAwD;YAChDwD,QAAQ,GAAK,KAAKxD,SAAxB,CADsD;;YAIlDpC,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA7C,EAA2D;cACrD9M,SAAS,KAAK,KAAlB,EAAyB;;;;oBAIfH,gBAAgB,EAAxB;mBACO,UAAL;gBACE+F,QAAQ,CAACzF,UAAT,GAAsB,CAACmL,YAAvB;;;mBAEG,oBAAL;gBACE1F,QAAQ,CAACzF,UAAT,GAAsBmL,YAAtB;;;;oBAGQ5L,WADV,GACuCkG,QADvC,CACUlG,WADV;oBACuBuF,WADvB,GACuCW,QADvC,CACuBX,WADvB;gBAEEW,QAAQ,CAACzF,UAAT,GAAsB8E,WAAW,GAAGvF,WAAd,GAA4B4L,YAAlD;;;WAbN,MAgBO;YACL1F,QAAQ,CAACzF,UAAT,GAAsBmL,YAAtB;;SAlBJ,MAoBO;UACL1F,QAAQ,CAAClD,SAAT,GAAqB4I,YAArB;;;;WAICzE,mBAAL;KA1HJ;;WA6HEE,oBA7HF,mCA6HyB;UACjB,KAAK5E,0BAAL,KAAoC,IAAxC,EAA8C;QAC5CjE,aAAa,CAAC,KAAKiE,0BAAN,CAAb;;KA/HN;;WAmIE6E,MAnIF,qBAmIW;yBAkBH,KAAK/E,KAlBF;UAELgF,QAFK,gBAELA,QAFK;UAGLC,SAHK,gBAGLA,SAHK;UAILlH,SAJK,gBAILA,SAJK;UAKLX,MALK,gBAKLA,MALK;UAML8H,QANK,gBAMLA,QANK;UAOLC,gBAPK,gBAOLA,gBAPK;UAQLC,YARK,gBAQLA,YARK;UASL8D,SATK,gBASLA,SATK;UAUL7D,QAVK,gBAULA,QAVK;8CAWLC,OAXK;UAWLA,OAXK,qCAWKlH,gBAXL;UAYLyM,MAZK,gBAYLA,MAZK;UAaLtF,gBAbK,gBAaLA,gBAbK;UAcLC,YAdK,gBAcLA,YAdK;UAeLtI,KAfK,gBAeLA,KAfK;UAgBLuI,cAhBK,gBAgBLA,cAhBK;UAiBLtI,KAjBK,gBAiBLA,KAjBK;UAmBCmD,WAnBD,GAmBiB,KAAKF,KAnBtB,CAmBCE,WAnBD;;UAsBDwK,YAAY,GAChB/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAD3C;UAGMpJ,QAAQ,GAAGqJ,YAAY,GACzB,KAAKE,mBADoB,GAEzB,KAAKC,iBAFT;;kCAIgC,KAAKC,iBAAL,EA7BzB;UA6BAzE,UA7BA;UA6BYC,SA7BZ;;UA+BDV,KAAK,GAAG,EAAd;;UACIkD,SAAS,GAAG,CAAhB,EAAmB;aACZ,IAAIf,MAAK,GAAG1B,UAAjB,EAA6B0B,MAAK,IAAIzB,SAAtC,EAAiDyB,MAAK,EAAtD,EAA0D;UACxDnC,KAAK,CAACC,IAAN,CACEhJ,mBAAa,CAAC+H,QAAD,EAAW;YACtB1G,IAAI,EAAE+G,QADgB;YAEtBtD,GAAG,EAAEuD,OAAO,CAAC6C,MAAD,EAAQ9C,QAAR,CAFU;YAGtB8C,KAAK,EAALA,MAHsB;YAItB7H,WAAW,EAAEmF,cAAc,GAAGnF,WAAH,GAAiB+B,SAJtB;YAKtBnF,KAAK,EAAE,KAAKwE,aAAL,CAAmByG,MAAnB;WALI,CADf;;OAlCG;;;;UAgDDmB,kBAAkB,GAAGa,qBAAqB,CAC9C,KAAKnK,KADyC,EAE9C,KAAKC,cAFyC,CAAhD;aAKOhD,mBAAa,CAClBsI,gBAAgB,IAAIC,YAApB,IAAoC,KADlB,EAElB;QACEP,SAAS,EAATA,SADF;QAEExD,QAAQ,EAARA,QAFF;QAGEiC,GAAG,EAAE,KAAKD,eAHZ;QAIEvG,KAAK;UACHiF,QAAQ,EAAE,UADP;UAEH/E,MAAM,EAANA,MAFG;UAGHD,KAAK,EAALA,KAHG;UAIHE,QAAQ,EAAE,MAJP;UAKH6I,uBAAuB,EAAE,OALtB;UAMHC,UAAU,EAAE,WANT;UAOHpI,SAAS,EAATA;WACGb,KARA;OANW,EAiBlBD,mBAAa,CAACkI,gBAAgB,IAAIC,YAApB,IAAoC,KAArC,EAA4C;QACvDJ,QAAQ,EAAEgB,KAD6C;QAEvDtC,GAAG,EAAEwB,QAFkD;QAGvDhI,KAAK,EAAE;UACLE,MAAM,EAAE0N,YAAY,GAAG,MAAH,GAAYxB,kBAD3B;UAELlD,aAAa,EAAE9F,WAAW,GAAG,MAAH,GAAY+B,SAFjC;UAGLlF,KAAK,EAAE2N,YAAY,GAAGxB,kBAAH,GAAwB;;OANlC,CAjBK,CAApB;KAxLJ;;WA4PE1E,mBA5PF,kCA4PwB;UAChB,OAAO,KAAK5E,KAAL,CAAWuB,eAAlB,KAAsC,UAA1C,EAAsD;YAC5C2H,SAD4C,GAC9B,KAAKlJ,KADyB,CAC5CkJ,SAD4C;;YAEhDA,SAAS,GAAG,CAAhB,EAAmB;uCAMb,KAAKgC,iBAAL,EANa;cAEfT,mBAFe;cAGfC,kBAHe;cAIfC,kBAJe;cAKfC,iBALe;;eAOZ/J,oBAAL,CACE4J,mBADF,EAEEC,kBAFF,EAGEC,kBAHF,EAIEC,iBAJF;;;;UASA,OAAO,KAAK5K,KAAL,CAAWyB,QAAlB,KAA+B,UAAnC,EAA+C;2BAKzC,KAAKrB,KALoC;YAE3CmK,gBAF2C,gBAE3CA,eAF2C;YAG3ClB,aAH2C,gBAG3CA,YAH2C;YAI3C1I,yBAJ2C,gBAI3CA,wBAJ2C;;aAMxCa,aAAL,CACE+I,gBADF,EAEElB,aAFF,EAGE1I,yBAHF;;KArRN;;;;;;WAwUEuK,iBAxUF,gCAwUwD;yBACf,KAAKlL,KADU;UAC5CkJ,SAD4C,gBAC5CA,SAD4C;UACjC3C,aADiC,gBACjCA,aADiC;yBAEG,KAAKnG,KAFR;UAE5CE,WAF4C,gBAE5CA,WAF4C;UAE/BiK,eAF+B,gBAE/BA,eAF+B;UAEdlB,YAFc,gBAEdA,YAFc;;UAIhDH,SAAS,KAAK,CAAlB,EAAqB;eACZ,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,CAAP;;;UAGIzC,UAAU,GAAG4D,sBAAsB,CACvC,KAAKrK,KADkC,EAEvCqJ,YAFuC,EAGvC,KAAKpJ,cAHkC,CAAzC;UAKMyG,SAAS,GAAG4D,yBAAyB,CACzC,KAAKtK,KADoC,EAEzCyG,UAFyC,EAGzC4C,YAHyC,EAIzC,KAAKpJ,cAJoC,CAA3C,CAboD;;;UAsB9C0G,gBAAgB,GACpB,CAACrG,WAAD,IAAgBiK,eAAe,KAAK,UAApC,GACInH,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYkD,aAAZ,CADJ,GAEI,CAHN;UAIMK,eAAe,GACnB,CAACtG,WAAD,IAAgBiK,eAAe,KAAK,SAApC,GACInH,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYkD,aAAZ,CADJ,GAEI,CAHN;aAKO,CACLnD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYoD,UAAU,GAAGE,gBAAzB,CADK,EAELvD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS4F,SAAS,GAAG,CAArB,EAAwBxC,SAAS,GAAGE,eAApC,CAAZ,CAFK,EAGLH,UAHK,EAILC,SAJK,CAAP;KAvWJ;;;IAA6BK,mBAA7B,UAKSC,YALT,GAKwB;IACpBjJ,SAAS,EAAE,KADS;IAEpBsH,QAAQ,EAAEhD,SAFU;IAGpBwI,MAAM,EAAE,UAHY;IAIpBtE,aAAa,EAAE,CAJK;IAKpBd,cAAc,EAAE;GAVpB;;;;;;;AAkeF,IAAMzB,qBAAmB,GAAG,SAAtBA,mBAAsB,eAWjB;MATPgB,QASO,SATPA,QASO;MARPjH,SAQO,SARPA,SAQO;MAPPX,MAOO,SAPPA,MAOO;MANPyN,MAMO,SANPA,MAMO;MALPzF,YAKO,SALPA,YAKO;MAJPI,YAIO,SAJPA,YAIO;MAHPrI,KAGO,SAHPA,KAGO;MADPkD,QACO,SADPA,QACO;;MACL1B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;QACrCuG,YAAY,IAAI,IAAhB,IAAwBI,YAAY,IAAI,IAA5C,EAAkD;UAC5C9G,oBAAkB,IAAI,CAACA,oBAAkB,CAACuI,GAAnB,CAAuB5G,QAAvB,CAA3B,EAA6D;QAC3D3B,oBAAkB,CAACwI,GAAnB,CAAuB7G,QAAvB;QACA8G,OAAO,CAACC,IAAR,CACE,mEACE,qEAFJ;;KAJqC;;;QAYnC0D,YAAY,GAAG/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA9D;;YAEQ9M,SAAR;WACO,YAAL;WACK,UAAL;YACMiM,oBAAoB,IAAI,CAACA,oBAAoB,CAAC/C,GAArB,CAAyB5G,QAAzB,CAA7B,EAAiE;UAC/D2J,oBAAoB,CAAC9C,GAArB,CAAyB7G,QAAzB;UACA8G,OAAO,CAACC,IAAR,CACE,mEACE,yFAFJ;;;;;WAMC,KAAL;WACK,KAAL;;;;;cAIQC,KAAK,CACT,qDACE,yCADF,WAEMtJ,SAFN,uBADS,CAAX;;;YAOI8M,MAAR;WACO,YAAL;WACK,UAAL;;;;;cAIQxD,KAAK,CACT,kDACE,qDADF,WAEMwD,MAFN,uBADS,CAAX;;;QAOA7F,QAAQ,IAAI,IAAhB,EAAsB;YACdqC,KAAK,CACT,oDACE,qCADF,YAEMrC,QAAQ,KAAK,IAAb,GAAoB,MAApB,GAA6B,OAAOA,QAF1C,wBADS,CAAX;;;QAOE8F,YAAY,IAAI,OAAO3N,KAAP,KAAiB,QAArC,EAA+C;YACvCkK,KAAK,CACT,iDACE,oDADF,YAEMlK,KAAK,KAAK,IAAV,GAAiB,MAAjB,GAA0B,OAAOA,KAFvC,wBADS,CAAX;KADF,MAMO,IAAI,CAAC2N,YAAD,IAAiB,OAAO1N,MAAP,KAAkB,QAAvC,EAAiD;YAChDiK,KAAK,CACT,kDACE,mDADF,YAEMjK,MAAM,KAAK,IAAX,GAAkB,MAAlB,GAA2B,OAAOA,MAFxC,wBADS,CAAX;;;CA7EN;;AC1nBA,IAAMkK,6BAA2B,GAAG,EAApC;;AAmBA,IAAMW,iBAAe,GAAG,SAAlBA,eAAkB,CACtBjI,KADsB,EAEtBmI,KAFsB,EAGtBC,aAHsB,EAIL;aACMpI,KADN;MACTsI,QADS,QACTA,QADS;MAETD,eAFS,GAE8BD,aAF9B,CAETC,eAFS;MAEQE,iBAFR,GAE8BH,aAF9B,CAEQG,iBAFR;;MAIbJ,KAAK,GAAGI,iBAAZ,EAA+B;QACzBtG,MAAM,GAAG,CAAb;;QACIsG,iBAAiB,IAAI,CAAzB,EAA4B;UACpBZ,YAAY,GAAGU,eAAe,CAACE,iBAAD,CAApC;MACAtG,MAAM,GAAG0F,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAA5C;;;SAGG,IAAI4L,CAAC,GAAGD,iBAAiB,GAAG,CAAjC,EAAoCC,CAAC,IAAIL,KAAzC,EAAgDK,CAAC,EAAjD,EAAqD;UAC/C5L,IAAI,GAAK0L,QAAF,CAAkCE,CAAlC,CAAX;MAEAH,eAAe,CAACG,CAAD,CAAf,GAAqB;QACnBvG,MAAM,EAANA,MADmB;QAEnBrF,IAAI,EAAJA;OAFF;MAKAqF,MAAM,IAAIrF,IAAV;;;IAGFwL,aAAa,CAACG,iBAAd,GAAkCJ,KAAlC;;;SAGKE,eAAe,CAACF,KAAD,CAAtB;CA7BF;;AAgCA,IAAMM,iBAAe,GAAG,SAAlBA,eAAkB,CACtBzI,KADsB,EAEtBoI,aAFsB,EAGtBnG,MAHsB,EAInB;MACKoG,eADL,GAC4CD,aAD5C,CACKC,eADL;MACsBE,iBADtB,GAC4CH,aAD5C,CACsBG,iBADtB;MAGGG,sBAAsB,GAC1BH,iBAAiB,GAAG,CAApB,GAAwBF,eAAe,CAACE,iBAAD,CAAf,CAAmCtG,MAA3D,GAAoE,CADtE;;MAGIyG,sBAAsB,IAAIzG,MAA9B,EAAsC;;WAE7B0G,6BAA2B,CAChC3I,KADgC,EAEhCoI,aAFgC,EAGhCG,iBAHgC,EAIhC,CAJgC,EAKhCtG,MALgC,CAAlC;GAFF,MASO;;;;WAIE2G,kCAAgC,CACrC5I,KADqC,EAErCoI,aAFqC,EAGrChF,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYkF,iBAAZ,CAHqC,EAIrCtG,MAJqC,CAAvC;;CAvBJ;;AAgCA,IAAM0G,6BAA2B,GAAG,SAA9BA,2BAA8B,CAClC3I,KADkC,EAElCoI,aAFkC,EAGlCS,IAHkC,EAIlCC,GAJkC,EAKlC7G,MALkC,EAMvB;SACJ6G,GAAG,IAAID,IAAd,EAAoB;QACZE,MAAM,GAAGD,GAAG,GAAG1F,IAAI,CAAC4F,KAAL,CAAW,CAACH,IAAI,GAAGC,GAAR,IAAe,CAA1B,CAArB;QACMG,aAAa,GAAGhB,iBAAe,CAACjI,KAAD,EAAQ+I,MAAR,EAAgBX,aAAhB,CAAf,CAA8CnG,MAApE;;QAEIgH,aAAa,KAAKhH,MAAtB,EAA8B;aACrB8G,MAAP;KADF,MAEO,IAAIE,aAAa,GAAGhH,MAApB,EAA4B;MACjC6G,GAAG,GAAGC,MAAM,GAAG,CAAf;KADK,MAEA,IAAIE,aAAa,GAAGhH,MAApB,EAA4B;MACjC4G,IAAI,GAAGE,MAAM,GAAG,CAAhB;;;;MAIAD,GAAG,GAAG,CAAV,EAAa;WACJA,GAAG,GAAG,CAAb;GADF,MAEO;WACE,CAAP;;CAvBJ;;AA2BA,IAAMF,kCAAgC,GAAG,SAAnCA,gCAAmC,CACvC5I,KADuC,EAEvCoI,aAFuC,EAGvCD,KAHuC,EAIvClG,MAJuC,EAK5B;MACHiH,SADG,GACWlJ,KADX,CACHkJ,SADG;MAEPC,QAAQ,GAAG,CAAf;;SAGEhB,KAAK,GAAGe,SAAR,IACAjB,iBAAe,CAACjI,KAAD,EAAQmI,KAAR,EAAeC,aAAf,CAAf,CAA6CnG,MAA7C,GAAsDA,MAFxD,EAGE;IACAkG,KAAK,IAAIgB,QAAT;IACAA,QAAQ,IAAI,CAAZ;;;SAGKR,6BAA2B,CAChC3I,KADgC,EAEhCoI,aAFgC,EAGhChF,IAAI,CAACE,GAAL,CAAS6E,KAAT,EAAgBe,SAAS,GAAG,CAA5B,CAHgC,EAIhC9F,IAAI,CAAC4F,KAAL,CAAWb,KAAK,GAAG,CAAnB,CAJgC,EAKhClG,MALgC,CAAlC;CAjBF;;AA0BA,IAAMkI,qBAAqB,GAAG,SAAxBA,qBAAwB,eAGzB;MAFDjB,SAEC,SAFDA,SAEC;MADDb,eACC,SADDA,eACC;MADgB8C,iBAChB,SADgBA,iBAChB;MADmC5C,iBACnC,SADmCA,iBACnC;MACC6C,wBAAwB,GAAG,CAA/B,CADG;;;MAKC7C,iBAAiB,IAAIW,SAAzB,EAAoC;IAClCX,iBAAiB,GAAGW,SAAS,GAAG,CAAhC;;;MAGEX,iBAAiB,IAAI,CAAzB,EAA4B;QACpBZ,YAAY,GAAGU,eAAe,CAACE,iBAAD,CAApC;IACA6C,wBAAwB,GAAGzD,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAA9D;;;MAGIgL,kBAAkB,GAAGsB,SAAS,GAAGX,iBAAZ,GAAgC,CAA3D;MACMV,0BAA0B,GAAGD,kBAAkB,GAAGuD,iBAAxD;SAEOC,wBAAwB,GAAGvD,0BAAlC;CApBF;;AAuBA,IAAMwD,gBAAgB;;AAAGpB,mBAAmB,CAAC;EAC3CC,aAAa,EAAE,uBACblK,KADa,EAEbmI,KAFa,EAGbC,aAHa;WAIFH,iBAAe,CAACjI,KAAD,EAAQmI,KAAR,EAAeC,aAAf,CAAf,CAA6CnG,MAJ3C;GAD4B;EAO3CmI,WAAW,EAAE,qBACXpK,KADW,EAEXmI,KAFW,EAGXC,aAHW;WAIAA,aAAa,CAACC,eAAd,CAA8BF,KAA9B,EAAqCvL,IAJrC;GAP8B;EAa3CuN,qBAAqB,EAArBA,qBAb2C;EAe3Cf,6BAA6B,EAAE,uCAC7BpJ,KAD6B,EAE7BmI,KAF6B,EAG7BhE,KAH6B,EAI7BkF,YAJ6B,EAK7BjB,aAL6B,EAMlB;QACHrK,SADG,GACkCiC,KADlC,CACHjC,SADG;QACQX,MADR,GACkC4C,KADlC,CACQ5C,MADR;QACgByN,MADhB,GACkC7K,KADlC,CACgB6K,MADhB;QACwB1N,KADxB,GACkC6C,KADlC,CACwB7C,KADxB;;QAIL2N,YAAY,GAAG/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA9D;QACMjO,IAAI,GAAMkO,YAAY,GAAG3N,KAAH,GAAWC,MAAvC;QACMuK,YAAY,GAAGM,iBAAe,CAACjI,KAAD,EAAQmI,KAAR,EAAeC,aAAf,CAApC,CANW;;;QAULkB,kBAAkB,GAAGa,qBAAqB,CAACnK,KAAD,EAAQoI,aAAR,CAAhD;QAEMmB,SAAS,GAAGnG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhBD,IAAI,CAACE,GAAL,CAASgG,kBAAkB,GAAG1M,IAA9B,EAAoC+K,YAAY,CAAC1F,MAAjD,CAFgB,CAAlB;QAIMuH,SAAS,GAAGpG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhBsE,YAAY,CAAC1F,MAAb,GAAsBrF,IAAtB,GAA6B+K,YAAY,CAAC/K,IAF1B,CAAlB;;QAKIuH,KAAK,KAAK,OAAd,EAAuB;UAEnBkF,YAAY,IAAIG,SAAS,GAAG5M,IAA5B,IACAyM,YAAY,IAAIE,SAAS,GAAG3M,IAF9B,EAGE;QACAuH,KAAK,GAAG,MAAR;OAJF,MAKO;QACLA,KAAK,GAAG,QAAR;;;;YAIIA,KAAR;WACO,OAAL;eACSoF,SAAP;;WACG,KAAL;eACSC,SAAP;;WACG,QAAL;eACSpG,IAAI,CAACqG,KAAL,CAAWD,SAAS,GAAG,CAACD,SAAS,GAAGC,SAAb,IAA0B,CAAjD,CAAP;;WACG,MAAL;;YAEMH,YAAY,IAAIG,SAAhB,IAA6BH,YAAY,IAAIE,SAAjD,EAA4D;iBACnDF,YAAP;SADF,MAEO,IAAIA,YAAY,GAAGG,SAAnB,EAA8B;iBAC5BA,SAAP;SADK,MAEA;iBACED,SAAP;;;;GAnEmC;EAwE3Cc,sBAAsB,EAAE,gCACtBrK,KADsB,EAEtBiC,MAFsB,EAGtBmG,aAHsB;WAIXK,iBAAe,CAACzI,KAAD,EAAQoI,aAAR,EAAuBnG,MAAvB,CAJJ;GAxEmB;EA8E3CqI,yBAAyB,EAAE,mCACzBtK,KADyB,EAEzByG,UAFyB,EAGzB4C,YAHyB,EAIzBjB,aAJyB,EAKd;QACHrK,SADG,GAC6CiC,KAD7C,CACHjC,SADG;QACQX,MADR,GAC6C4C,KAD7C,CACQ5C,MADR;QACgB8L,SADhB,GAC6ClJ,KAD7C,CACgBkJ,SADhB;QAC2B2B,MAD3B,GAC6C7K,KAD7C,CAC2B6K,MAD3B;QACmC1N,KADnC,GAC6C6C,KAD7C,CACmC7C,KADnC;;QAIL2N,YAAY,GAAG/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA9D;QACMjO,IAAI,GAAMkO,YAAY,GAAG3N,KAAH,GAAWC,MAAvC;QACMuK,YAAY,GAAGM,iBAAe,CAACjI,KAAD,EAAQyG,UAAR,EAAoB2B,aAApB,CAApC;QACMmB,SAAS,GAAGF,YAAY,GAAGzM,IAAjC;QAEIqF,MAAM,GAAG0F,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAAhD;QACI8J,SAAS,GAAGD,UAAhB;;WAEOC,SAAS,GAAGwC,SAAS,GAAG,CAAxB,IAA6BjH,MAAM,GAAGsH,SAA7C,EAAwD;MACtD7C,SAAS;MACTzE,MAAM,IAAIgG,iBAAe,CAACjI,KAAD,EAAQ0G,SAAR,EAAmB0B,aAAnB,CAAf,CAAiDxL,IAA3D;;;WAGK8J,SAAP;GApGyC;EAuG3C7G,iBAvG2C,6BAuGzBG,KAvGyB,EAuGNK,QAvGM,EAuGwB;gBACjCL,KADiC;QACzDmL,iBADyD,SACzDA,iBADyD;QAG3D/C,aAAa,GAAG;MACpBC,eAAe,EAAE,EADG;MAEpB8C,iBAAiB,EAAEA,iBAAiB,IAAI7D,6BAFpB;MAGpBiB,iBAAiB,EAAE,CAAC;KAHtB;;IAMAlI,QAAQ,CAACiL,eAAT,GAA2B,UACzBnD,KADyB,EAEzByB,iBAFyB,EAGtB;UADHA,iBACG;QADHA,iBACG,GAD2B,IAC3B;;;MACHxB,aAAa,CAACG,iBAAd,GAAkCnF,IAAI,CAACE,GAAL,CAChC8E,aAAa,CAACG,iBADkB,EAEhCJ,KAAK,GAAG,CAFwB,CAAlC,CADG;;;;;MAUH9H,QAAQ,CAACyB,kBAAT,CAA4B,CAAC,CAA7B;;UAEI8H,iBAAJ,EAAuB;QACrBvJ,QAAQ,CAAC0J,WAAT;;KAhBJ;;WAoBO3B,aAAP;GApIyC;EAuI3CtI,qCAAqC,EAAE,KAvII;EAyI3CC,aAAa,EAAE,8BAAoC;QAAjCuI,QAAiC,SAAjCA,QAAiC;;QAC7C3J,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;UACrC,OAAOyJ,QAAP,KAAoB,UAAxB,EAAoC;cAC5BjB,KAAK,CACT,oDACE,8BADF,YAEMiB,QAAQ,KAAK,IAAb,GAAoB,MAApB,GAA6B,OAAOA,QAF1C,wBADS,CAAX;;;;CA5IoC,CAA5C;;AC/JA,IAAMiD,aAAa;;AAAGvM,mBAAmB,CAAC;EACxCC,eAAe,EAAE,+BAA8BkJ,KAA9B;QAAGxG,WAAH,QAAGA,WAAH;WACfwG,KAAK,GAAKxG,WADK;GADuB;EAIxCvC,cAAc,EAAE,+BAA8B+I,KAA9B;QAAGxG,WAAH,SAAGA,WAAH;WACZA,WADY;GAJwB;EAOxCjC,YAAY,EAAE,6BAA4ByI,KAA5B;QAAGvG,SAAH,SAAGA,SAAH;WACZuG,KAAK,GAAKvG,SADE;GAP0B;EAUxCnC,YAAY,EAAE,6BAA4B0I,KAA5B;QAAGvG,SAAH,SAAGA,SAAH;WACVA,SADU;GAV0B;EAaxCvC,uBAAuB,EAAE;QAAGgF,QAAH,SAAGA,QAAH;QAAazC,SAAb,SAAaA,SAAb;WACrBA,SAAF,GAA6ByC,QADN;GAbe;EAgBxC/E,sBAAsB,EAAE;QAAG8E,WAAH,SAAGA,WAAH;QAAgBzC,WAAhB,SAAgBA,WAAhB;WACpBA,WAAF,GAA+ByC,WADT;GAhBgB;EAmBxC7E,8BAA8B,EAAE,+CAE9BlB,WAF8B,EAG9B8F,KAH8B,EAI9BjG,UAJ8B,EAK9BkK,aAL8B,EAM9B9D,aAN8B,EAOnB;QANTF,WAMS,SANTA,WAMS;QANIzC,WAMJ,SANIA,WAMJ;QANiBxE,KAMjB,SANiBA,KAMjB;QACLqO,gBAAgB,GAAGpI,IAAI,CAACC,GAAL,CACvB,CADuB,EAEvBe,WAAW,GAAKzC,WAAhB,GAA6CxE,KAFtB,CAAzB;QAIMoM,SAAS,GAAGnG,IAAI,CAACE,GAAL,CAChBkI,gBADgB,EAEhBnN,WAAW,GAAKsD,WAFA,CAAlB;QAIM6H,SAAS,GAAGpG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhBhF,WAAW,GAAKsD,WAAhB,GACExE,KADF,GAEEmH,aAFF,GAGI3C,WALY,CAAlB;;QAQIwC,KAAK,KAAK,OAAd,EAAuB;UACjBjG,UAAU,IAAIsL,SAAS,GAAGrM,KAA1B,IAAmCe,UAAU,IAAIqL,SAAS,GAAGpM,KAAjE,EAAwE;QACtEgH,KAAK,GAAG,MAAR;OADF,MAEO;QACLA,KAAK,GAAG,QAAR;;;;YAIIA,KAAR;WACO,OAAL;eACSoF,SAAP;;WACG,KAAL;eACSC,SAAP;;WACG,QAAL;;;YAGQiC,YAAY,GAAGrI,IAAI,CAACqG,KAAL,CACnBD,SAAS,GAAG,CAACD,SAAS,GAAGC,SAAb,IAA0B,CADnB,CAArB;;YAGIiC,YAAY,GAAGrI,IAAI,CAACsI,IAAL,CAAUvO,KAAK,GAAG,CAAlB,CAAnB,EAAyC;iBAChC,CAAP,CADuC;SAAzC,MAEO,IAAIsO,YAAY,GAAGD,gBAAgB,GAAGpI,IAAI,CAAC4F,KAAL,CAAW7L,KAAK,GAAG,CAAnB,CAAtC,EAA6D;iBAC3DqO,gBAAP,CADkE;SAA7D,MAEA;iBACEC,YAAP;;;WAEC,MAAL;;YAEMvN,UAAU,IAAIsL,SAAd,IAA2BtL,UAAU,IAAIqL,SAA7C,EAAwD;iBAC/CrL,UAAP;SADF,MAEO,IAAIsL,SAAS,GAAGD,SAAhB,EAA2B;;;iBAGzBC,SAAP;SAHK,MAIA,IAAItL,UAAU,GAAGsL,SAAjB,EAA4B;iBAC1BA,SAAP;SADK,MAEA;iBACED,SAAP;;;;GAhFgC;EAqFxC/J,2BAA2B,EAAE,4CAE3BjB,QAF2B,EAG3B4F,KAH2B,EAI3B1D,SAJ2B,EAK3B2H,aAL2B,EAM3B9D,aAN2B,EAOhB;QANT1C,SAMS,SANTA,SAMS;QANExE,MAMF,SANEA,MAMF;QANUiH,QAMV,SANUA,QAMV;QACLsH,aAAa,GAAGvI,IAAI,CAACC,GAAL,CACpB,CADoB,EAEpBgB,QAAQ,GAAKzC,SAAb,GAAwCxE,MAFpB,CAAtB;QAIMmM,SAAS,GAAGnG,IAAI,CAACE,GAAL,CAChBqI,aADgB,EAEhBpN,QAAQ,GAAKqD,SAFG,CAAlB;QAIM4H,SAAS,GAAGpG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhB9E,QAAQ,GAAKqD,SAAb,GACExE,MADF,GAEEkH,aAFF,GAGI1C,SALY,CAAlB;;QAQIuC,KAAK,KAAK,OAAd,EAAuB;UACjB1D,SAAS,IAAI+I,SAAS,GAAGpM,MAAzB,IAAmCqD,SAAS,IAAI8I,SAAS,GAAGnM,MAAhE,EAAwE;QACtE+G,KAAK,GAAG,MAAR;OADF,MAEO;QACLA,KAAK,GAAG,QAAR;;;;YAIIA,KAAR;WACO,OAAL;eACSoF,SAAP;;WACG,KAAL;eACSC,SAAP;;WACG,QAAL;;;YAGQiC,YAAY,GAAGrI,IAAI,CAACqG,KAAL,CACnBD,SAAS,GAAG,CAACD,SAAS,GAAGC,SAAb,IAA0B,CADnB,CAArB;;YAGIiC,YAAY,GAAGrI,IAAI,CAACsI,IAAL,CAAUtO,MAAM,GAAG,CAAnB,CAAnB,EAA0C;iBACjC,CAAP,CADwC;SAA1C,MAEO,IAAIqO,YAAY,GAAGE,aAAa,GAAGvI,IAAI,CAAC4F,KAAL,CAAW5L,MAAM,GAAG,CAApB,CAAnC,EAA2D;iBACzDuO,aAAP,CADgE;SAA3D,MAEA;iBACEF,YAAP;;;WAEC,MAAL;;YAEMhL,SAAS,IAAI+I,SAAb,IAA0B/I,SAAS,IAAI8I,SAA3C,EAAsD;iBAC7C9I,SAAP;SADF,MAEO,IAAI+I,SAAS,GAAGD,SAAhB,EAA2B;;;iBAGzBC,SAAP;SAHK,MAIA,IAAI/I,SAAS,GAAG+I,SAAhB,EAA2B;iBACzBA,SAAP;SADK,MAEA;iBACED,SAAP;;;;GAlJgC;EAuJxCrK,4BAA4B,EAAE,6CAE5BhB,UAF4B;QAC1ByD,WAD0B,SAC1BA,WAD0B;QACbyC,WADa,SACbA,WADa;WAI5BhB,IAAI,CAACC,GAAL,CACE,CADF,EAEED,IAAI,CAACE,GAAL,CACEc,WAAW,GAAG,CADhB,EAEEhB,IAAI,CAAC4F,KAAL,CAAW9K,UAAU,GAAKyD,WAA1B,CAFF,CAFF,CAJ4B;GAvJU;EAmKxCxC,+BAA+B,EAAE,iDAE/BsH,UAF+B,EAG/BvI,UAH+B,EAIpB;QAHTyD,WAGS,UAHTA,WAGS;QAHIyC,WAGJ,UAHIA,WAGJ;QAHiBjH,KAGjB,UAHiBA,KAGjB;QACLiF,IAAI,GAAGqE,UAAU,GAAK9E,WAA5B;QACMiK,iBAAiB,GAAGxI,IAAI,CAACsI,IAAL,CACxB,CAACvO,KAAK,GAAGe,UAAR,GAAqBkE,IAAtB,IAAgCT,WADR,CAA1B;WAGOyB,IAAI,CAACC,GAAL,CACL,CADK,EAELD,IAAI,CAACE,GAAL,CACEc,WAAW,GAAG,CADhB,EAEEqC,UAAU,GAAGmF,iBAAb,GAAiC,CAFnC;KAFK,CAAP;GA5KsC;EAqLxCjM,yBAAyB,EAAE,2CAEzBc,SAFyB;QACvBmB,SADuB,UACvBA,SADuB;QACZyC,QADY,UACZA,QADY;WAIzBjB,IAAI,CAACC,GAAL,CACE,CADF,EAEED,IAAI,CAACE,GAAL,CAASe,QAAQ,GAAG,CAApB,EAAuBjB,IAAI,CAAC4F,KAAL,CAAWvI,SAAS,GAAKmB,SAAzB,CAAvB,CAFF,CAJyB;GArLa;EA8LxChC,4BAA4B,EAAE,8CAE5B6G,UAF4B,EAG5BhG,SAH4B,EAIjB;QAHTmB,SAGS,UAHTA,SAGS;QAHEyC,QAGF,UAHEA,QAGF;QAHYjH,MAGZ,UAHYA,MAGZ;QACLmF,GAAG,GAAGkE,UAAU,GAAK7E,SAA3B;QACMiK,cAAc,GAAGzI,IAAI,CAACsI,IAAL,CACrB,CAACtO,MAAM,GAAGqD,SAAT,GAAqB8B,GAAtB,IAA+BX,SADV,CAAvB;WAGOwB,IAAI,CAACC,GAAL,CACL,CADK,EAELD,IAAI,CAACE,GAAL,CACEe,QAAQ,GAAG,CADb,EAEEoC,UAAU,GAAGoF,cAAb,GAA8B,CAFhC;KAFK,CAAP;GAvMsC;EAgNxChM,iBAhNwC,6BAgNtBG,KAhNsB,EAgNE;GAhNF;EAoNxCF,qCAAqC,EAAE,IApNC;EAsNxCC,aAAa,EAAE,+BAAkD;QAA/C4B,WAA+C,UAA/CA,WAA+C;QAAlCC,SAAkC,UAAlCA,SAAkC;;QAC3DjD,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;UACrC,OAAO8C,WAAP,KAAuB,QAA3B,EAAqC;cAC7B0F,KAAK,CACT,uDACE,4BADF,YAGI1F,WAAW,KAAK,IAAhB,GAAuB,MAAvB,GAAgC,OAAOA,WAH3C,wBADS,CAAX;;;UASE,OAAOC,SAAP,KAAqB,QAAzB,EAAmC;cAC3ByF,KAAK,CACT,qDACE,4BADF,YAEMzF,SAAS,KAAK,IAAd,GAAqB,MAArB,GAA8B,OAAOA,SAF3C,wBADS,CAAX;;;;CAnOiC,CAAzC;;ACAA,IAAMkK,aAAa;;AAAG7B,mBAAmB,CAAC;EACxCC,aAAa,EAAE,6BAA2B/B,KAA3B;QAAGG,QAAH,QAAGA,QAAH;WACbH,KAAK,GAAKG,QADG;GADyB;EAIxC8B,WAAW,EAAE,4BAA2BjC,KAA3B;QAAGG,QAAH,SAAGA,QAAH;WACTA,QADS;GAJ2B;EAOxC6B,qBAAqB,EAAE;QAAGjB,SAAH,SAAGA,SAAH;QAAcZ,QAAd,SAAcA,QAAd;WACnBA,QAAF,GAA4BY,SADP;GAPiB;EAUxCE,6BAA6B,EAAE,8CAE7BjB,KAF6B,EAG7BhE,KAH6B,EAI7BkF,YAJ6B,EAKlB;QAJTtL,SAIS,SAJTA,SAIS;QAJEX,MAIF,SAJEA,MAIF;QAJU8L,SAIV,SAJUA,SAIV;QAJqBZ,QAIrB,SAJqBA,QAIrB;QAJ+BuC,MAI/B,SAJ+BA,MAI/B;QAJuC1N,KAIvC,SAJuCA,KAIvC;;QAEL2N,YAAY,GAAG/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA9D;QACMjO,IAAI,GAAMkO,YAAY,GAAG3N,KAAH,GAAWC,MAAvC;QACM2O,cAAc,GAAG3I,IAAI,CAACC,GAAL,CACrB,CADqB,EAErB6F,SAAS,GAAKZ,QAAd,GAAwC1L,IAFnB,CAAvB;QAIM2M,SAAS,GAAGnG,IAAI,CAACE,GAAL,CAChByI,cADgB,EAEhB5D,KAAK,GAAKG,QAFM,CAAlB;QAIMkB,SAAS,GAAGpG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhB8E,KAAK,GAAKG,QAAV,GAAoC1L,IAApC,GAA6C0L,QAF7B,CAAlB;;QAKInE,KAAK,KAAK,OAAd,EAAuB;UAEnBkF,YAAY,IAAIG,SAAS,GAAG5M,IAA5B,IACAyM,YAAY,IAAIE,SAAS,GAAG3M,IAF9B,EAGE;QACAuH,KAAK,GAAG,MAAR;OAJF,MAKO;QACLA,KAAK,GAAG,QAAR;;;;YAIIA,KAAR;WACO,OAAL;eACSoF,SAAP;;WACG,KAAL;eACSC,SAAP;;WACG,QAAL;;;;cAGQiC,YAAY,GAAGrI,IAAI,CAACqG,KAAL,CACnBD,SAAS,GAAG,CAACD,SAAS,GAAGC,SAAb,IAA0B,CADnB,CAArB;;cAGIiC,YAAY,GAAGrI,IAAI,CAACsI,IAAL,CAAU9O,IAAI,GAAG,CAAjB,CAAnB,EAAwC;mBAC/B,CAAP,CADsC;WAAxC,MAEO,IAAI6O,YAAY,GAAGM,cAAc,GAAG3I,IAAI,CAAC4F,KAAL,CAAWpM,IAAI,GAAG,CAAlB,CAApC,EAA0D;mBACxDmP,cAAP,CAD+D;WAA1D,MAEA;mBACEN,YAAP;;;;WAGC,MAAL;;YAEMpC,YAAY,IAAIG,SAAhB,IAA6BH,YAAY,IAAIE,SAAjD,EAA4D;iBACnDF,YAAP;SADF,MAEO,IAAIA,YAAY,GAAGG,SAAnB,EAA8B;iBAC5BA,SAAP;SADK,MAEA;iBACED,SAAP;;;;GArEgC;EA0ExCc,sBAAsB,EAAE,uCAEtBpI,MAFsB;QACpBiH,SADoB,SACpBA,SADoB;QACTZ,QADS,SACTA,QADS;WAItBlF,IAAI,CAACC,GAAL,CACE,CADF,EAEED,IAAI,CAACE,GAAL,CAAS4F,SAAS,GAAG,CAArB,EAAwB9F,IAAI,CAAC4F,KAAL,CAAW/G,MAAM,GAAKqG,QAAtB,CAAxB,CAFF,CAJsB;GA1EgB;EAmFxCgC,yBAAyB,EAAE,0CAEzB7D,UAFyB,EAGzB4C,YAHyB,EAId;QAHTtL,SAGS,SAHTA,SAGS;QAHEX,MAGF,SAHEA,MAGF;QAHU8L,SAGV,SAHUA,SAGV;QAHqBZ,QAGrB,SAHqBA,QAGrB;QAH+BuC,MAG/B,SAH+BA,MAG/B;QAHuC1N,KAGvC,SAHuCA,KAGvC;;QAEL2N,YAAY,GAAG/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA9D;QACM5I,MAAM,GAAGwE,UAAU,GAAK6B,QAA9B;QACM1L,IAAI,GAAMkO,YAAY,GAAG3N,KAAH,GAAWC,MAAvC;QACM4O,eAAe,GAAG5I,IAAI,CAACsI,IAAL,CACtB,CAAC9O,IAAI,GAAGyM,YAAP,GAAsBpH,MAAvB,IAAmCqG,QADb,CAAxB;WAGOlF,IAAI,CAACC,GAAL,CACL,CADK,EAELD,IAAI,CAACE,GAAL,CACE4F,SAAS,GAAG,CADd,EAEEzC,UAAU,GAAGuF,eAAb,GAA+B,CAFjC;KAFK,CAAP;GA/FsC;EAwGxCnM,iBAxGwC,6BAwGtBG,KAxGsB,EAwGE;GAxGF;EA4GxCF,qCAAqC,EAAE,IA5GC;EA8GxCC,aAAa,EAAE,8BAAoC;QAAjCuI,QAAiC,SAAjCA,QAAiC;;QAC7C3J,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;UACrC,OAAOyJ,QAAP,KAAoB,QAAxB,EAAkC;cAC1BjB,KAAK,CACT,oDACE,4BADF,YAEMiB,QAAQ,KAAK,IAAb,GAAoB,MAApB,GAA6B,OAAOA,QAF1C,wBADS,CAAX;;;;CAjHiC,CAAzC;;ACJA;;AAEA,AAAe,SAAS2D,cAAT,CAAwBC,IAAxB,EAAsCC,IAAtC,EAA6D;OACrE,IAAIC,SAAT,IAAsBF,IAAtB,EAA4B;QACtB,EAAEE,SAAS,IAAID,IAAf,CAAJ,EAA0B;aACjB,IAAP;;;;OAGC,IAAIC,UAAT,IAAsBD,IAAtB,EAA4B;QACtBD,IAAI,CAACE,UAAD,CAAJ,KAAoBD,IAAI,CAACC,UAAD,CAA5B,EAAyC;aAChC,IAAP;;;;SAGG,KAAP;;;ACVF;;;AAEA,AAAe,SAASC,QAAT,CACbC,SADa,EAEbvI,SAFa,EAGJ;MACMwI,SADN,GACiCD,SADjC,CACDpP,KADC;MACoBsP,QADpB,iCACiCF,SADjC;;MAEMG,SAFN,GAEiC1I,SAFjC,CAED7G,KAFC;MAEoBwP,QAFpB,iCAEiC3I,SAFjC;;SAKP,CAACkI,cAAc,CAACM,SAAD,EAAYE,SAAZ,CAAf,IAAyC,CAACR,cAAc,CAACO,QAAD,EAAWE,QAAX,CAD1D;;;ACRF;;;AAEA,AAAe,SAASC,qBAAT,CACb5I,SADa,EAEb6I,SAFa,EAGJ;SAEP,CAACP,QAAQ,CAAC,KAAKrM,KAAN,EAAa+D,SAAb,CAAT,IAAoCkI,cAAc,CAAC,KAAK7L,KAAN,EAAawM,SAAb,CADpD;;;;;;;;;;"}