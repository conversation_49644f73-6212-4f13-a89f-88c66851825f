import { NewControl } from './NewControl';
import DocumentContentBase from '../DocumentContentBase';
import { INewControlProperty, CodeValueItem, NewControlDefaultSetting, ResultType, IStructParamJson, AlignType,
     } from '../../../common/commonDefines';
import { ChangeNewControlPrefix, ChangeNewControlSeparator, ChangeNewControlRetrieve,
    ChangeNewControlShowValue,
    ChangeNewControlItemValue,
    ChangeNewControlItemCode,
    ChangeNewControlItemSelect,
    ChangeNewControlContentFixedLength,
    ChangeNewControlContentHideHasTitle,
    ChangeNewControlPrintChecked} from './NewControlChange';

/**
 * 单选框/ 多选框 / 下拉框
 */
export class NewComboBox extends NewControl {
    private items: CodeValueItem[];
    private prefixContent: string;
    private selectPrefixContent: string;
    private separator: string;
    private bRetrieve: boolean;
    private bShowValue: boolean;
    private bShowCodeAndValue: boolean;
    private bSetText: boolean;
    private fixedLength: number;
    private bTextBorder: boolean;
    private alignments: AlignType;
    private hideHasTitle: boolean;
    private valueLabel: string;
    private codeLabel: string;
    private bPrintSelected: boolean;

    constructor(parent: DocumentContentBase, name: string, property: INewControlProperty, sText?: string ) {
        super(parent, name, property, sText);
        this.initItems(property.newControlItems);
        this.prefixContent = property.prefixContent ? property.prefixContent : '';
        this.selectPrefixContent = property.selectPrefixContent ? property.selectPrefixContent : '';
        this.separator = property.separator ? property.separator : '';
        this.bRetrieve = (true === property.retrieve ? true : false);
        this.bShowValue = (true === property.isShowValue ? true : false);
        this.fixedLength = property.newControlFixedLength;
        this.bTextBorder = property.bTextBorder;
        this.alignments = property.alignments;
        this.codeLabel = property.codeLabel;
        this.valueLabel = property.valueLabel;
        this.bShowCodeAndValue = (true === property.bShowCodeAndValue ? true : false);
        this.bPrintSelected = property.printSelected == null ? false : property.printSelected;
        if (property && property.hideHasTitle != null) {
            this.hideHasTitle = property.hideHasTitle;
        } else {
            this.hideHasTitle = false;
        }
    }

    public setShowCodeAndValue(bShowCodeAndValue: boolean): number {
        if (bShowCodeAndValue == null || bShowCodeAndValue === this.bShowCodeAndValue) {
            return ResultType.UnEdited;
        }

        this.bShowCodeAndValue = bShowCodeAndValue;
        return ResultType.Success;
    }

    public isShowCodeAndValue(): boolean {
        return this.bShowCodeAndValue;
    }

    public getHideHasTitle(): boolean {
        return this.hideHasTitle;
    }

    public setHideHasTitle(flag: boolean): number {
        if (flag == null || flag === this.hideHasTitle) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlContentHideHasTitle(this, this.hideHasTitle, flag));
        }
        this.setDirty();
        this.hideHasTitle = flag;
        return ResultType.Success;
    }

    public setValueLabel(value: string): number {
        if ( value == null || value === this.valueLabel) {
            return ResultType.UnEdited;
        }

        this.valueLabel = value;
        return ResultType.Success;
    }

    public getValueLabel(): string {
        return this.valueLabel;
    }

    public setLabelCode(code: string): number {
        if ( code == null || code === this.codeLabel) {
            return ResultType.UnEdited;
        }
        this.codeLabel = code;
        return ResultType.Success;
    }

    public getLabelCode(): string {
        return this.codeLabel;
    }

    // public addContent(): void {
    //     if (typeof this.fixedLength !== 'number' || this.fixedLength <= 0) {
    //         return;
    //     }

    //     this.recalculate();
    // }

    public setAlignments(type: AlignType): number {
        if (type === undefined || type === this.alignments) {
            return ResultType.UnEdited;
        }
        this.alignments = type;
        return ResultType.Success;
    }

    public getAlignments(): AlignType {
        return this.alignments;
    }

    public addItems(codes: string[], values: string[]): boolean {
        if (codes.length === 0 || values.length === 0) {
            return false;
        }

        let flag = false;
        const items = this.items;
        codes.forEach((code, index) => {
            if (!code) {
                return;
            }
            const obj = items.find((item) => item.code === code);
            if (obj) {
                obj.setCodeValue(code, values[index]);
            } else {
                items.push(new CodeValueItem(code, values[index]));
            }

            flag = true;
        });
        this.setDirty();
        return flag;
    }

    /**
     * 添加新的选项
     * @param pos 项位置
     * @param code 名称
     * @param value 值
     */
    public addItem(pos: number, code: string, value: string): boolean {
        if ( false === this.isItemValid(code) ) {
            return false;
        }

        const item = new CodeValueItem(code, value);
        if ( this.items.length <= pos ) {
            this.items.push(item);
        } else {
            this.items.splice(pos, 0, item);
        }

        return true;
    }

    /**
     * 删除选项
     * @param pos 删除项位置
     */
    public removeItem( pos: number ): void {
        if ( this.items.length <= pos || null == this.items[pos] ) {
            return ;
        }

        this.items.splice(pos, 1);
    }

    public removeAllItems(): number {
        if ( 0 === this.items.length ) {
            return ;
        }

        // const history = this.getDocumentParent()
        //                 .getHistory();
        // history.addChange(new ChangeNewControlRemoveItems(this, this.items));
        // this.setDirty();
        this.items = [];
        return ;
    }

    /**
     * 修改选项code，value
     * @param pos 项位置
     * @param code
     * @param value
     */
    public modifyItem( pos: number, code: string, value: string ): boolean {
        if ( false === this.isItemValid(code) || null == this.items[pos] ) {
            return false;
        }

        return this.items[pos].setCodeValue(code, value);
    }

    /**
     * 移动选项的位置
     * @param pos 项位置
     * @param bUp 是否上移；true： 上移，false：下移
     */
    public moveItem( pos: number, bUp: boolean ): boolean {
        if ( this.items.length <= pos || null == this.items[pos] ) {
            return false;
        }

        if ( ( 0 === pos && true === bUp )
            || ( this.items.length - 1 === pos && false === bUp )) {
            return false;
        }

        const item = this.items.splice(pos, 1);
        const newPos = true === bUp ? pos - 1 : pos + 1;
        this.items.splice(newPos, 0, item[0]);

        return true;
    }

    /**
     * 设置前缀字符
     * @param bSelect 是否选中项
     * @param content 前缀字符
     */
    public setItemPrefix( bSelected: boolean, content: string ): number {
        if (content == null) {
            return ResultType.UnEdited;
        }
        const oldPrefixContent = (true === bSelected ? this.selectPrefixContent : this.prefixContent);

        if ( true === bSelected ) {
            if ( content === this.selectPrefixContent ) {
                return ResultType.UnEdited;
            }

            this.selectPrefixContent = content;
        } else {
            if ( content === this.prefixContent ) {
                return ResultType.UnEdited;
            }

            this.prefixContent = content;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlPrefix(this, oldPrefixContent, content, bSelected));
        }
        this.setDirty();
        return ResultType.Success;
    }

    public getFixedLength(): number {
        return this.fixedLength;
    }

    public setFixedLength( fixedLength: number ): number {
        if ( (fixedLength == null && fixedLength === this.fixedLength)
            || fixedLength === this.fixedLength ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlContentFixedLength(this, this.fixedLength, fixedLength));
        }
        this.setDirty();
        this.fixedLength = fixedLength;
        return ResultType.Success;
    }

    public setTextBorder(flag: boolean): number {
        if (flag == null || flag === this.bTextBorder) {
            return ResultType.UnEdited;
        }

        this.bTextBorder = flag;

        return ResultType.Success;
    }

    public isTextBorder(): boolean {
        return this.bTextBorder;
    }

    public setSeparator( separator: string ): number {
        if ( null == separator || separator === this.separator ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlSeparator(this, this.separator, separator));
        }
        this.setDirty();
        this.separator = separator;
        return ResultType.Success;
    }

    public setRetrieve(bRetrieve: boolean): number {
        if (bRetrieve === undefined || bRetrieve === this.bRetrieve) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlRetrieve(this, this.bRetrieve, bRetrieve));
        }

        this.bRetrieve = bRetrieve;
        this.setDirty();
        return ResultType.Success;
    }

    /**
     * 设置newcontrol属性
     * @param property
     */
    public setProperty(property: INewControlProperty): number {
        const result = this.compare(property.newControlItems);

        let res = super.setProperty(property);
        this.initItems(property.newControlItems);

        res = this.setItemPrefix(true, property.selectPrefixContent) && res;
        res = this.setItemPrefix(false, property.prefixContent) && res;
        res = this.setSeparator(property.separator) && res;
        res = this.setShowValue(property.isShowValue) && res;
        res = this.setRetrieve(property.retrieve) && res;
        res = this.setShowCodeAndValue(property.bShowCodeAndValue) && res;
        res = this.setFixedLength(property.newControlFixedLength) && res;
        res = this.setTextBorder(property.bTextBorder) && res;
        res = this.setHideHasTitle(property.hideHasTitle) && res;
        res = this.setAlignments(property.alignments) && res;
        res = this.setValueLabel(property.valueLabel) && res;
        res = this.setLabelCode(property.codeLabel) && res;
        res = this.setPrintSelected(property.printSelected) && res;

        if ( result && false === this.isPlaceHolderContent()) {
            this.updateNewControlText();
            res = ResultType.Success;
        }
        return res;
    }

    public getProperty(): INewControlProperty {
        const newControlProperty = super.getProperty();
        newControlProperty.selectPrefixContent = this.selectPrefixContent;
        newControlProperty.prefixContent = this.prefixContent;
        newControlProperty.separator = this.separator;
        newControlProperty.isShowValue = this.bShowValue;
        newControlProperty.retrieve = this.bRetrieve;
        newControlProperty.newControlItems = this.items;
        newControlProperty.bTextBorder = this.isTextBorder();
        newControlProperty.alignments = this.alignments;
        newControlProperty.hideHasTitle = this.hideHasTitle;
        newControlProperty.valueLabel = this.valueLabel;
        newControlProperty.codeLabel = this.codeLabel;
        newControlProperty.printSelected = this.bPrintSelected;

        return newControlProperty;
    }

    public updateNewControlText(): number {
        if ( this.items && 0 < this.items.length ) {
            let selectText = '';
            let unselectText = '';
            let selectNumber = 0;

            this.items.forEach((item) => {
                const content = (true === this.bShowValue ? item.value : item.code);
                if ( item.bSelect ) {
                    selectText += ( '' === selectText ? content : this.separator + content);
                    selectNumber++;
                } else {
                    unselectText += ('' === unselectText ? content : this.separator + content);
                }
            });

            let text = ( '' === selectText ? selectText : this.getSelectPrefixContent() + selectText);

            if ( '' !== this.getPrefixContent() && this.items.length !== selectNumber ) {
                text += ( '' === text ? this.getPrefixContent() + unselectText :
                        NewControlDefaultSetting.DefaultPrefixSeparator + this.getPrefixContent() + unselectText);
            }
            this.bSetText = true;
            this.setNewControlText(text);
        }
        return ResultType.Success;
    }

    public updateSelectItemByText(): boolean {
        const items = this.items;
        if (!items || items.length === 0) {
            return;
        }

        let text = this.getNewControlText2();
        if (!text) {
            return this.resetItems();
        }
        const selectPrefixContent = this.selectPrefixContent;
        let bSelected = false;
        if (selectPrefixContent) {
            // if (text.indexOf(selectPrefixContent) !== 0) {
            //     return this.resetItems();
            // }
            this.items.forEach((item) => {
                if (item.bSelect) {
                    bSelected = true;
                    return ;
                }
            });

            if (!bSelected) {
                if (this.prefixContent) {
                    const index1 = text.indexOf(selectPrefixContent);
                    const index2 = text.indexOf(this.prefixContent);

                    // 排除：selectPrefixContent是prefixContent的子集
                    // 额外条件：元素内容要确保选中的内容是排在未选中内容之前
                    if (-1 !== index1 && -1 !== index2
                        && -1 !== this.prefixContent.indexOf(selectPrefixContent)
                        && index1 >= index2 ) {
                        bSelected = false;
                    } else {
                        bSelected = true;
                    }
                } else {
                    bSelected = true;
                }
            }

            if (bSelected) {
                text = text.replace(selectPrefixContent, '');
            }
        } else {
            bSelected = true;
        }

        const prefixContent = this.prefixContent;
        if (prefixContent) {
            const values = text.split(new RegExp(`[${NewControlDefaultSetting.DefaultPrefixSeparator}]{0,1}${prefixContent}`));
            if (values.length > 1) {
                text = values[0];
            }
        }

        let key = 'code';
        if (this.bShowValue) {
            key = 'value';
        }

        const selectedIndexs = [];
        let bResult = false;
        if (this.isNewMultiCombox() || this.isNewMultiList()) {
            const separator = this.getSeparator();
            const texts = text.split(separator);
            items.forEach((item, itemIndex) => {
                if (bSelected && texts.includes(item[key])) {
                    selectedIndexs.push(itemIndex);
                }
            });
            if (selectedIndexs.length > 0) {
                this.selectItems(selectedIndexs);
                bResult = true;
            }
        } else {
            const actIndex = items.findIndex((item) => item[key] === text);
            if (actIndex !== -1) {
                selectedIndexs.push(actIndex);
                if (items[actIndex].bSelect !== true) {
                    this.selectItems(selectedIndexs);
                }
                bResult = true;
            }
        }

        if (bResult) {
            return bResult;
        } else {
            bResult = this.resetItems();
        }

        return bResult;

    }

    public isContainSelected(text: string): boolean {
        if (!text) {
            return false;
        }
        if (!(this.isNewMultiList() || this.isNewMultiCombox())) {
            return false;
        }
        const texts = text.split(',');
        const items = this.items;
        let key = 'code';
        if (this.bShowValue) {
            key = 'value';
        }
        for (let index = 0, len = items.length; index < len; index++) {
            const item = items[index];
            if (item.bSelect === false) {
                continue;
            }
            const code = item[key];
            const actIndex = texts.findIndex((act) => act === code);
            if (actIndex !== -1) {
                texts.splice(actIndex, 1);
            }
            if (texts.length === 0) {
                break;
            }
        }

        return texts.length === 0;
    }

    public getRetrieve(): boolean {
        return this.bRetrieve;
    }

    public getItemList(): CodeValueItem[] {
        return this.items;
    }

    public getSelectPrefixContent(): string {
        return this.selectPrefixContent;
    }

    public getPrefixContent(): string {
        return this.prefixContent;
    }

    public getSeparator(): string {
        return this.separator;
    }

    public setNewControlListItems(selectItemsPos: number[], bRefresh?: boolean): number {
        let text = '';
        const length = this.items.length;
        if ( 0 === length ) {
            return ResultType.UnEdited;
        }

        let bResetSelect = true;
        if ( !selectItemsPos && this.items ) {
            selectItemsPos = [];
            bResetSelect = false;
            this.items.forEach((valueItem, index) => {
                if ( valueItem.bSelect ) {
                    selectItemsPos.push(index);
                }
            });
        }

        for (let index = 0; index < length; index++) {
            const item = this.items[index];
            if ( true === selectItemsPos.includes(index) ) {
                text += (true !== this.bShowValue ? item.code : item.value);
                text += this.separator;
                if ( bResetSelect ) {
                    this.setItemSelectUndoRedo(true, item);
                }
            } else {
                if ( bResetSelect ) {
                    this.setItemSelectUndoRedo(false, item);
                }
            }
        }

        const selectNumber = selectItemsPos.length;
        if ( 0 !== selectNumber ) {
            if (this.separator) {
                text = text.slice(0, text.length - this.separator.length);
            }

            if ( null != this.selectPrefixContent ) {
                text = this.selectPrefixContent + text;
            }
        }

        if ( selectNumber < length && null != this.prefixContent && '' !== this.prefixContent ) {
            if ( 0 < selectNumber ) {
                text += NewControlDefaultSetting.DefaultPrefixSeparator;
            }

            text += this.prefixContent;

            for (let index = 0; index < length; index++) {
                const item = this.items[index];
                if ( false === selectItemsPos.includes(index) ) {
                    text += (true !== this.bShowValue ? item.code : item.value);
                    text += this.separator;
                    if ( bResetSelect ) {
                        this.setItemSelectUndoRedo(false, item);
                    }
                }
            }
            if (this.separator) {
                text = text.slice(0, text.length - this.separator.length);
            }
        }
        // this.bSetText = true;
        return super.setNewControlText(text, bRefresh);
    }

    public setNewControlText( sText: string, bRefresh?: boolean): number {
        const res = super.setNewControlText(sText, bRefresh);
        this.setItemSelected(sText, res);
        return res;
    }

    public setNewControlTextByJson(json: IStructParamJson): number {
        const text = json.code_text;
        if (!text) {
            return ResultType.UnEdited;
        }

        const items = this.items;
        if (!items || items.length === 0) {
            return ResultType.UnEdited;
        }

        const key = 'value';
        const texts = text.split(',');
        const indexs = [];
        // let bSelected = false;

        items.forEach((item, index) => {
            const curText = item[key];
            const curIndex = texts.findIndex((cur) => cur === curText);
            if (curIndex !== -1) {
                // if (item.bSelect !== true) {
                //     bSelected = true;
                // }
                item.bSelect = true;
                texts.splice(curIndex, 1);
                indexs.push(index);
            } else {
                // if (item.bSelect === true) {
                //     bSelected = true;
                // }
                item.bSelect = false;
            }
        });

        // if (bSelected === false) {
        //     return ResultType.UnEdited;
        // }

        return this.setNewControlListItems(indexs, false);
    }

    public setItemSelected(text: string, result: number): void {
        if (this.bSetText === true) {
            this.bSetText = false;
            return;
        }
        if (result !== ResultType.Success) {
            return;
        }

        const items = this.items;
        if (!items || items.length === 0) {
            return;
        }
        if (!text) {
            if (0 < items.length ) {
                items.forEach((item) => {
                    item.bSelect = false;
                });
            }
            return;
        }

        const prefixContent = this.prefixContent;
        const selectPrefixContent = this.selectPrefixContent;
        // 先去掉修饰符,假如有的话
        if (selectPrefixContent) {
            text = text.replace(new RegExp('^' + selectPrefixContent), '');
        }

        if (prefixContent) {
            const curIndex = text.indexOf(NewControlDefaultSetting.DefaultPrefixSeparator + prefixContent);
            if (curIndex !== -1) {
                text = text.slice(0, curIndex);
            }
        }

        let texts: string[];
        if (this.separator) {
            texts = text.split(this.separator);
        } else {
            texts = [text];
        }

        let key = 'code';
        if (this.bShowValue) {
            key = 'value';
        }

        items.forEach((item) => {
            const curText = item[key];
            const curIndex = texts.findIndex((cur) => cur === curText);
            if (curIndex !== -1) {
                item.bSelect = true;
                texts.splice(curIndex, 1);
            } else {
                item.bSelect = false;
            }
        });
    }

    /**
     * 复位选择项
     */
    public resetSelectItems(): boolean {
        if ( null != this.items && 0 < this.items.length ) {
            this.items.forEach((item) => {
                item.bSelect = false;
            });
        }

        let res = false;
        if ( !this.isPlaceHolderContent() ) {
            this.addPlaceHolderContent();
            this.recalculate();
            this.setTextFlag(true);
            this.setDirty();
            res = true;
        }

        return res;
    }

    public isShowValue(): boolean {
        return this.bShowValue;
    }

    public setShowValue(bShowValue: boolean): number {
        if ( bShowValue === this.bShowValue || null == bShowValue ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlShowValue(this, this.bShowValue, bShowValue));
        }
        this.bShowValue = bShowValue;

        let bChange = false;

        if ( this.items ) {
            this.items.forEach((valueItem) => {
                if ( valueItem.bSelect ) {
                    bChange = true;
                    return ;
                }
            });
        }
        this.setTextFlag(true);
        this.setDirty();
        if ( bChange ) {
            this.setNewControlListItems(null);
            return ResultType.Success;
        }

        return ResultType.Success;
    }

    public isPrintSelected(): boolean {
        return this.bPrintSelected;
    }

    public getPrintSelected(): boolean {
        return this.bPrintSelected;
    }

    public setPrintSelected(bPrintSelected: boolean): number {
        if (this.bPrintSelected === bPrintSelected || bPrintSelected == null) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlPrintChecked(this, this.bPrintSelected, bPrintSelected));
        }

        this.bPrintSelected = bPrintSelected;
        this.setDirty();
        return ResultType.Success;
    }

    public toPrint(): void {
        super.toPrint();
        if (this.isHidden()) {
            return;
        }

        if (this.bPrintSelected) {
            this.setHidden(true, false);
        }
    }

    private resetItems(): boolean {
        const items = this.items;
        let bChange = false;
        items.forEach((item) => {
            if (item.bSelect) {
                item.bSelect = false;
                bChange = true;
            }
        });
        return bChange;
    }

    private selectItems(indexs: number[]): boolean {
        const items = this.items;
        items.forEach((item, index) => {
            if (indexs.includes(index)) {
                item.bSelect = true;
            } else {
                item.bSelect = false;
            }
        });
        return true;
    }

    /**
     * 设置是否显示value值
     * @param bShow 是否显示value值
     */
    // public setShowValue( bShow: boolean ): boolean {
    //     if ( bShow === this.bShowValue ) {
    //         return false;
    //     }

    //     this.bShowValue = bShow;
    //     return true;
    // }

    private initItems(items: CodeValueItem[]): void {
        if (items === undefined) {
            if (!this.items) {
                this.items = [];
            }
            return;
        }
        this.items = [];
        if ( null != items && items.length > 0) {
            if ( items[0].copy ) {
                this.items = items.slice(0);
            } else {
                items.forEach((item) => {
                    this.items.push(new CodeValueItem(item.code, item.value, item.bSelect));
                });
            }
        }
    }

    private isItemValid( code: string ): boolean {
        if ( null == code || '' === code ) {
            return false;
        }

        for (let index = 0, length = this.items.length; index < length; index++) {
            const item = this.items[index];

            if ( item.code === code ) {
                return false;
            }
        }

        return true;
    }

    private compare(items: CodeValueItem[]): boolean {
        if ( !items || this.items.length !== items.length ) {
            return true;
        }

        for (let index = 0, length = this.items.length; index < length; index++) {
            const oldItem = this.items[index];
            const newItem = items[index];
            if ( !oldItem || !newItem || oldItem.code !== newItem.code || oldItem.value !== newItem.value ) {
                return true;
            }
        }

        return false;
    }

    private setItemSelectUndoRedo(bSelect: boolean, item: CodeValueItem): void {
        if ( bSelect === item.bSelect ) {
            return ;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlItemSelect(item, item.bSelect, bSelect));
        }
        item.bSelect = bSelect;
    }

    private setItemCodeValueUndoRedo(code: string, value: string, item: CodeValueItem): void {
        if ( code === item.code && value === item.value ) {
            return ;
        }

        const history = this.getHistory();
        if ( item.code !== code ) {
            if ( history ) {
                history.addChange(new ChangeNewControlItemCode(item, item.code, code));
            }
            item.code = code;
        }

        if ( item.value !== value ) {
            if ( history ) {
                history.addChange(new ChangeNewControlItemValue(item, item.value, value));
            }
            item.value = value;
        }
    }
}
