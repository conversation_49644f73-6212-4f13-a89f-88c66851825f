import {VNode} from './ParseNode';
import HtmlParse from './ParseHtml';
import {convertBytesToBase64, convertHexStringToBytes} from './DataConver';

export default class WordParse {
    public images: VNode[];
    private html: string;

    public parse(html: string): VNode {
        this.html = html;
        this.filterWord();
        const color = this.getDefaultColor();
        let alinkDefatulAttrs: {color: string, textDecorationLine: string};
        if (color) {
            alinkDefatulAttrs = {color, textDecorationLine: 'underline'};
        }
        const htmlParse = new HtmlParse();
        const node = htmlParse.parse(this.html, undefined, alinkDefatulAttrs);
        this.images = htmlParse.images;
        return node;
    }

    public filterWord(): void {
        const matchs = this.html.match(/<(body)[^\x01]+<\/\1>/);
        if (matchs) {
            this.html = matchs[0];
        }
        this.html = this.html.replace(/([\t\r\n]+)/g, (str, str1, str2) => {
                const len = str2 + str1.length;
                const match = this.html.slice(len, len + 1);
                if (match.search(/[^<>\s]/) > -1) {
                    return ' ';
                }
                return '';
            })
            .replace(/<!--[\s\S]*?-->/ig, '')
                // 转换图片
            .replace(/<v:shape [^>]*>[\s\S]*?.<\/v:shape>/gi, (str: string) => {
                    // opera能自己解析出image所这里直接返回空
                    // if(browser.opera){
                    //     return '';
                    // }
                    try {
                        // 有可能是bitmap占为图，无用，直接过滤掉，主要体现在粘贴excel表格中
                        if (/Bitmap/i.test(str)) {
                            return '';
                        }
                        const width = str.match(/width:([ \d.]*p[tx])/i)[1];
                        const height = str.match(/height:([ \d.]*p[tx])/i)[1];
                        const src =  str.match(/src=\s*"([^"]*)"/i)[1];
                        return '<img style=\'width:' + this.transUnit(width) + '; height:"'
                            + this.transUnit(height) + '\' src="' + src + '" />';
                    } catch (e) {
                        return '';
                    }
                })
                // 针对wps添加的多余标签处理
            .replace(/<\/?div[^>]*>/g, '')
                // 去掉多余的属性
                .replace(/v:\w+=(["']?)[^'"]+\1/g, '')
                // tslint:disable-next-line: max-line-length
                .replace(/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|xml|meta|link|style|\w+:\w+)(?=[\s\/>]))[^>]*>/gi, '')
                .replace(/<p [^>]*class="?MsoHeading"?[^>]*>(.*?)<\/p>/gi, '<p><strong>$1</strong></p>')
                // 去掉多余的属性
                .replace(/\s+(class|lang|align)\s*=\s*(['"]?)([\w-]+)\2/ig, (str, name, marks, val) => {
                    // 保留list的标示
                    return name === 'class' && val === 'MsoListParagraph' ? str : '';
                })
                // 清除多余的font/span不能匹配&nbsp;有可能是空格
                .replace(/<(font|span)[^>]*>(\s*)<\/\1>/gi, (a, b, c) => {
                    return c.replace(/[\t\r\n ]+/g, ' ');
                })
                // 处理style的问题
                //.replace(/(<[a-z][^>]*)\sstyle=(["'])([^\2]*?)\2/gi, (str, tag, tmp, style) => {
                .replace(/(<[a-z][^>]*)\sstyle=(["'])(.*?)\2/gi, (str, tag, quote, style) => {

                    const n = [];
                    const s = style.replace(/^\s+|\s+$/, '')
                            .replace(/&#39;/g, '\'')
                            .replace(/&quot;/gi, '\'')
                            .replace(/[\d.]+(cm|pt)/g, (match: string) => {
                                return this.transUnit(match);
                            })
                            .split(/;\s*/g);

                    // tslint:disable-next-line: no-conditional-assignment
                    for (let i = 0, v; v = s[i]; i++) {
                        let name;
                        let value;
                        const parts = v.split(':');

                        if (parts.length === 2) {
                            name = parts[0].toLowerCase();
                            value = parts[1].toLowerCase();
                            if (/^(background)\w*/.test(name) && value.replace(/(initial|\s)/g, '').length === 0 ||
                                /^(margin)\w*/.test(name) && /^0\w+$/.test(value)) {
                                continue;
                            }

                            switch (name) {
                                case 'mso-padding-alt':
                                case 'mso-padding-top-alt':
                                case 'mso-padding-right-alt':
                                case 'mso-padding-bottom-alt':
                                case 'mso-padding-left-alt':
                                case 'mso-margin-alt':
                                case 'mso-margin-top-alt':
                                case 'mso-margin-right-alt':
                                case 'mso-margin-bottom-alt':
                                case 'mso-margin-left-alt':
                                // ie下会出现挤到一起的情况
                               // case "mso-table-layout-alt":
                                case 'mso-height':
                                case 'mso-width':
                                case 'mso-vertical-align-alt':
                                    // trace:1819 ff下会解析出padding在table上
                                    if (!/<table/.test(tag)) {
                                        n[i] = name.replace(/^mso-|-alt$/g, '') + ':' + this.transUnit(value);
                                    }
                                    continue;
                                case 'horiz-align':
                                    n[i] = 'text-align:' + value;
                                    continue;

                                case 'vert-align':
                                    n[i] = 'vertical-align:' + value;
                                    continue;

                                case 'font-color':
                                case 'mso-foreground':
                                    n[i] = 'color:' + value;
                                    continue;

                                case 'mso-background':
                                case 'mso-highlight':
                                    n[i] = 'background:' + value;
                                    continue;

                                case 'mso-default-height':
                                    n[i] = 'min-height:' + this.transUnit(value);
                                    continue;

                                case 'mso-default-width':
                                    n[i] = 'min-width:' + this.transUnit(value);
                                    continue;

                                case 'mso-padding-between-alt':
                                    n[i] = 'border-collapse:separate;border-spacing:' + this.transUnit(value);
                                    continue;

                                case 'text-line-through':
                                    if ((value === 'single') || (value === 'double')) {
                                        n[i] = 'text-decoration:line-through';
                                    }
                                    continue;
                                case 'mso-zero-height':
                                    if (value === 'yes') {
                                        n[i] = 'display:none';
                                    }
                                    continue;
//                                case 'background':
//                                    break;
                                case 'margin':
                                    if (!/[1-9]/.test(value)) {
                                        continue;
                                    }

                            }

                            // tslint:disable-next-line: max-line-length
                            if (/^(mso|column|font-emph|lang|layout|line-break|list-image|nav|panose|punct|row|ruby|sep|size|src|tab-|table-border|text-(?:decor|trans)|top-bar|version|vnd|word-break)/.test(name)
                                ||
                                /text\-indent|padding|margin/.test(name) && /\-[\d.]+/.test(value)
                            ) {
                                continue;
                            }

                            n[i] = name + ':' + parts[1];
                        }
                    }
                    return tag + (n.length ? ' style=\'' + n.join(';')
                    .replace(/;{2,}/g, ';') + '\'' : '');
                })

                //.replace(/<[\w]*?[^>]*?(face)=(["'])([^\2]*?)\2([^>])*?>/gi, (a, b, c, d, f) => {
                .replace(/<(\w*?)[^>]*?(face)=["'](.*?)["'][^>]*?>/gi, (a, b, c, d) => {

                    if (!d) {
                        return;
                    }
                    let match: RegExpExecArray;
                    const font = `font-family: ${d};`;
                    // tslint:disable-next-line: no-conditional-assignment

                   // if (match = /(style)=(["'])([^\2]*?)\2/i.exec(a)) {
                    if (match = /(style)=(["'])(.*?)\2/i.exec(a)) {

                        const res = `${match[3]};${font}`;
                        return a.replace(match[3], res);
                    } else {
                        return a.replace(b, 'style')
                        .replace(d, font);
                    }
                });
    }

    public getImagesData(a: string): Array<{hex: string, type: string}> {
        const images = [];
        const reg = /\{\\pict[\s\S]+?\\bliptag\-?\d+(\\blipupi\-?\d+)?(\{\\\*\\blipuid\s?[\da-fA-F]+)?[\s\}]*?/;
        let type: string;
        const arrs = a.match(new RegExp('(?:(' + reg.source + '))([\\da-fA-F\\s]+)\\}', 'g'));
        if (!arrs) { return images; }
        // tslint:disable-next-line: prefer-for-of
        for (let e = 0; e < arrs.length; e++) {
            if (reg.test(arrs[e])) {
                if (-1 !== arrs[e].indexOf('\\pngblip')) {
                    type = 'image/png';
                } else if (-1 !== arrs[e].indexOf('\\jpegblip')) {
                    type = 'image/jpeg';
                } else { continue; }
                images.push({ hex: type ? arrs[e].replace(reg, '')
                    .replace(/[^\da-fA-F]/g, '') : null, type });
            }
        }

        return images;
    }

    public transImage(content: string): void {
        const images = this.images;
        if (!content || images.length === 0) {
            return;
        }

        const datas = this.getImagesData(content);
        datas.forEach((data, index) => {
            const image = images[index];
            if (!image) {
                return;
            }
            const path = 'data:' + data.type + ';base64,' +  convertBytesToBase64(convertHexStringToBytes(data.hex));
            // let attr: VNodeAttr;
            const attrs = image.attrs as any;
            if (attrs) {
                attrs.src = path;
            } else {
                image.attrs = {src: path};
            }
        });
    }

    // 去掉小数
    public transUnit(v: string): string {
        v = v.replace(/[\d.]+\w+/g, (m: string) => {
            return this.transUnitToPx(m);
        });

        return v;
    }

    public transUnitToPx(val: string): string {
        if (!/(pt|cm)/.test(val)) {
            return val;
        }

        let unit: string;
        val.replace(/([\d.]+)(\w+)/, (str, v: string, u: string): string => {
            val = v;
            unit = u;

            return undefined;
        });

        switch (unit) {
            case 'cm':
                val = parseFloat(val) * 25 + '';
                break;
            case 'pt':
                val = Math.round(parseFloat(val) * 96 / 72) + '';
        }

        return val + (val ? 'px' : '');
    }

    private getDefaultColor(): string {
        const matchs = this.html.match(/<body\s+link="(#\w+)"[^>]+?>/);
        if (matchs) {
            return matchs[1];
        }
        return;
    }
}
