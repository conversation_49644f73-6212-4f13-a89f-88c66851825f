import { Blob } from './Blob';
import { WMFJSError } from './Helper';
export class BitmapBase {
    public getWidth(): void {
        throw new WMFJSError('getWidth not implemented');
    }

    public getHeight(): void {
        throw new WMFJSError('getHeight not implemented');
    }
}

export class BitmapCoreHeader {
    public width: number;
    public height: number;
    public planes: number;
    public bitcount: number;

    constructor(reader: Blob, skipsize: boolean) {
        if (skipsize) {
            reader.skip(4);
        }
        this.width = reader.readUint16();
        this.height = reader.readUint16();
        this.planes = reader.readUint16();
        this.bitcount = reader.readUint16();
    }

    public colors(): number {
        // tslint:disable-next-line: no-bitwise
        return this.bitcount <= 8 ? 1 << this.bitcount : 0;
    }
}
