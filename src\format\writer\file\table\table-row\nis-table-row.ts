import { XmlComponent } from '../../xml-components';

import { TableCell } from '../table-cell';
import { TableRowProperties } from './table-row-properties';
import { TableRowHeight } from '../../../../../model/core/Table/TableRowProperty';
import { NISTableRowProperties } from './nis-table-row-properties';
import { NISTableCell } from '../table-cell/nis-table-cell';
import { INISRowProperty } from '../../../../../common/commonDefines';

export class NISTableRow extends XmlComponent {
    private properties: NISTableRowProperties;

    constructor(private readonly cells?: NISTableCell[]) {
        super('w:tr');

        this.initializeProperties();

        if (cells != null) {
            cells.forEach((c) => this.root.push(c));
        }

    }

    // custom method, may be useful
    public setCell(cols: number): NISTableRow {
        // const cells: NISTableCell[] = [];
        for (let j = 0; j < cols; j++) {
            const cell = new NISTableCell();
            // cells.push(cell);
            this.root.push(cell);
        }
        // if (cells != null) {
        //     cells.forEach((c) => this.root.push(c));
        // }
        return this;
    }

    public getCell(ix: number): NISTableCell {
        const cell = this.cells[ix];

        if (!cell) {
            throw Error('Index out of bounds when trying to get cell on row');
        }

        return cell;
    }

    // public addGridSpan(index: number, cellSpan: number): TableCell {
    //     const remainCell = this.cells[index];
    //     remainCell.CellProperties.addGridSpan(cellSpan);
    //     this.cells.splice(index + 1, cellSpan - 1);
    //     this.root.splice(index + 2, cellSpan - 1);

    //     return remainCell;
    // }

    // public mergeCells(startIndex: number, endIndex: number): TableCell {
    //     const cellSpan = endIndex - startIndex + 1;

    //     return this.addGridSpan(startIndex, cellSpan);
    // }

    public addRowHeight(tableRowHeight: TableRowHeight): NISTableRow {
        this.properties.addRowHeight(tableRowHeight);

        return this;
    }

    public addIsTableHeader(tblHeader: string, backcolor?: string): NISTableRow {
        this.properties.addIsTableHeader(tblHeader, backcolor);
        return this;
    }

    public addReadOnly(val: string): NISTableRow {
        this.properties.addReadOnly(val);
        return this;
    }

    public addTrNis(nisProperty: INISRowProperty): NISTableRow {
        this.properties.addTrNis(nisProperty);
        return this;
    }

    /**
     * initialize properties if not
     */
    private initializeProperties(): void {
        if (!this.properties) {
            this.properties = new NISTableRowProperties();

            // should be the first child element
            // this.root.push(this.properties);
            this.root.unshift(this.properties);
        }
    }
}
