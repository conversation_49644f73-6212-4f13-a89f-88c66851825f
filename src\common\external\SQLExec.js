import Enumerable from './linq';

export function jsql(table, cmd) {
    // var myList = [
    // { Name: "<PERSON>", Age: 20.2 },
    // { Name: "<PERSON>", Age: 21.1 },
    // { Name: "<PERSON><PERSON>", Age: 18 },
    // { Name: "<PERSON>", Age: 14 },
    // { Name: "LinTao", Age: 25 }
    // ];

    let arrRes;
    if ( cmd.get('where') ) {
        arrRes = Enumerable.from(table).where(new Function('$', cmd.get('where')));
    }

    if ( cmd.get('select') ) {
        arrRes = arrRes ? arrRes.select(new Function('$', cmd.get('select'))) : Enumerable.from(table).select(new Function('$', cmd.get('select')));
    }
    // const arrRes = Enumerable.from(myList).where(new Function('$', "return $.Name.length <= 4 && $.Age >= 21.0")).toArray();//.select(new Function('$', "return $.Age >= 21 ")).toArray();//Enumerable.from(myList).where(myList => myList.Name.length <= 3).toArray();
    // const arrRes = myList.asEnumerable().where(myList => myList.Name.length <= 3).toArray();// Enumerable.From(myList).Where("x=>x.Name=='Jim'").ToArray();

    console.log(arrRes)
    return ( arrRes ? arrRes.toArray() : '');
}