import { XmlComponent } from '../xml-components';
// tslint:disable-next-line: max-line-length
import { DeleteProtect, EditProtect, EditReverse, Hidden, Title, ShowTitle, SerialNumber, Max<PERSON>ength, TipsContent, Identifier, Placeholder } from '../paragraph/contentControl/contentControlElements';
import { ICustomProps } from '../../../../common/commonDefines';
import { CustomProperty } from '../paragraph/contentControl/customProperty';

export class RegionProperties extends XmlComponent {

  private readonly customProperty: CustomProperty;
  constructor() {
    super('rgPr');
    this.customProperty = new CustomProperty();
  }

  // public push(item: XmlComponent): void {
  //   this.root.push(item);
  // }
  public addHidden(flag: string): RegionProperties {
    this.root.push(new Hidden(flag));
    return this;
  }

  public addDeleteProtect(flag: string): RegionProperties {
    this.root.push(new DeleteProtect(flag));
    return this;
  }

  public addEditProtect(flag: string): RegionProperties {
    this.root.push(new EditProtect(flag));
    return this;
  }

  public addEditReverse(flag: string): RegionProperties {
    this.root.push(new EditReverse(flag));
    return this;
  }

  public addTitle(text: string): RegionProperties {
    this.root.push(new Title(text));
    return this;
  }

  public addTipContent(text: string): RegionProperties {
    this.root.push(new TipsContent(text));
    return this;
  }

  public addIdentifier(text: string): RegionProperties {
    this.root.push(new Identifier(text));
    return this;
  }

  public addSerialNumber(text: string): RegionProperties {
    this.root.push(new SerialNumber(text));
    return this;
  }

  public addMaxLength(text: string): RegionProperties {
    this.root.push(new MaxLength(text));
    return this;
  }

  public addPlaceholder(text: string): RegionProperties {
    this.root.push(new Placeholder(text));
    return this;
  }

  public addShowTitle(flag: string): RegionProperties {
    this.root.push(new ShowTitle(flag));
    return this;
  }

  public addBeginPos(text: string): RegionProperties {
    this.root.push(new BeginPos(text));
    return this;
  }

  public addEndPos(text: string): RegionProperties {
    this.root.push(new EndPos(text));
    return this;
  }

  public addCustomProperty(properties: Map<string, ICustomProps>): RegionProperties {
    this.root.push(this.customProperty);

    this.customProperty.addCustomProperties(properties);
    return this;
  }

}

export class BeginPos extends XmlComponent {
  constructor(text: string) {
      super('beginPos');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class EndPos extends XmlComponent {
  constructor(text: string) {
      super('endPos');
      if (text) {
        this.root.push(text);
      }
  }
}
