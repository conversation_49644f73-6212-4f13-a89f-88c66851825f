@import './global.less';
.hz-editor-container {
    .line-active-btn {
        width: 100%;
        height: 28px;
        color:@activeColor;
        font-size: 14px;
        font-family: 'PingFang SC', 'Microsoft YaHei', 'Microsoft Jhenghei', sans-serif;
        text-align: center;
        cursor: pointer;
        background:rgb(234, 237, 238);
        border-radius: 2px;
        border: 1px solid #099FFF;
    }

    .format-list {
        & > li {
            display: block;
            height: 28px;
            padding-left: 5px;
            line-height: 28px;
            cursor: pointer;

            &.active, &:hover {
                background-color: @activeBgColor;
            }
        }
    }
}

@page-header-height: 64px;

.image-editor-wrap-hz0525 {
  padding-top: @page-header-height;
  z-index: 999;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(122, 123, 124, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;

  .image-color {
    position: relative;
    .select-color {
      position: absolute;
      z-index: 9999;
    }
  }
}

.image-editor-box-hz0525 {
  width: 900px;
  background: #ffffff;
  box-shadow: 0px 6px 20px 0px rgba(100, 145, 172, 0.17);
  border-radius: 4px;
  font-size: 14px;
  color: #293750;
}

.image-editor-header-hz0525 {
  height: 48px;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-editor-tools-hz0525 {
  display: flex;
  height: 46px;
  background: #F6F7F9;
  padding: 0 16px;
  align-items: center;
}
.image-editor-canvas-hz0525 {
  margin: 0 50px;
  background: rgba(0,0,0,0.5) repeat url('../../../../plugins/image-editor/assets/canvas-bg.png');
  display: flex;
  justify-content: center;
}
.image-editor-footer-hz0525 {
  height: 48px;
  line-height: 48px;
  text-align: center;
  border-top: 1px solid #DFE2E5;
}

.close-icon-hz0525 {
  cursor: pointer;
  width: 12px;
  height: 12px;
  padding: 1px;
  position: relative;
  transform: rotate(45deg);
  display: inline-block;
  color: #ACB4C1;
  &:before {
    display: block;
    content: '';
    height: 2px;
    width: 100%;
    background: currentColor;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(0, -50%);
  }
  &:after {
    display: block;
    content: '';
    height: 100%;
    width: 2px;
    background: currentColor;
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%,0);
  }
}

.tool-icon-mixin(){
  display: inline-block;
  position: relative;
  cursor: pointer;
  width:24px;
  height: 24px;
  border: none;
  margin-right: 2px;
  margin-left: 2px;
  border-radius: 2px;
  background-color: transparent;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  &:before {
    display: block;
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    box-sizing: border-box;
    border: 1px dashed currentColor;
  }
  &:focus {
    outline: none;
  }
  &:hover {
    outline: #DDDDE3 auto 1px;
    background-color: #ffffff;
  }
  &.current-action {
    outline: none;
    background-color:  #ACB4C1;
  }
}

.move-icon-hz0525 {
  .tool-icon-mixin();;
  background-image: url('../../../../plugins/image-editor/assets/select-icon.png');
}

.pencil-icon-hz0525 {
  position: relative;
  .tool-icon-mixin();
  background-image: url('../../../../plugins/image-editor/assets/pencil-icon.png');
  .pencil-color-point-hz0525 {
    position: absolute;
    left: 4px;
    bottom: 4px;
    height: 3px;
    width: 3px;
  }
}

.circle-icon-hz0525 {
  .tool-icon-mixin();
  background-image: url('../../../../plugins/image-editor/assets/circle-icon.png');
}

.rect-icon-hz0525 {
  background-image: url('../../../../plugins/image-editor/assets/rect-icon.png');
  .tool-icon-mixin();
}

.text-icon-hz0525 {
  .tool-icon-mixin();
  background-image: url('../../../../plugins/image-editor/assets/text-icon.png');
}

.line-icon-hz0525 {
  .tool-icon-mixin();
  background-image: url('../../../../plugins/image-editor/assets/line-icon.png');
}

.arrow-icon-hz0525 {
  .tool-icon-mixin();
  background-image: url('../../../../plugins/image-editor/assets/arrow-icon.png');
}

.delete-icon-hz0525 {
  .tool-icon-mixin();
  background-image: url('../../../../plugins/image-editor/assets/delete-icon.png');
}

.fill-icon-hz0525 {
  .tool-icon-mixin();
  background-image: url('../../../../plugins/image-editor/assets/fill-icon.png');
}

.quicktext-icon-hz0525 {
  .tool-icon-mixin();
  background-image: url('../../../../plugins/image-editor/assets/quick-text-icon.png');
}

.line-thickness-icon-hz0525 {
  .tool-icon-mixin  ;
  background-image: url('../../../../plugins/image-editor/assets/line-thickness-icon.png');
}

.color-icon-hz0525 {
  border: none;
  width: 20px;
  padding: 0;
  margin-left: 8px;
  background: transparent;
  cursor: pointer;
  box-shadow: none;
}

.icon-divider-hz0525 {
  display: inline-block;
  height: 20px;
  width: 1px;
  background-color: #E4E5E7;
  margin: 0 10px;
}

.tooltip-hz0525 {
  display: inline-block;
  position: relative;
  .tooltip-box-hz0525 {
    display: none;
    z-index: 100;
    position: absolute;
    top: calc(100% + 2px);
    left: 0;
    margin: 0;
    padding: 8px;
    min-width: 100%;
    background: #FAFAFA;
    box-shadow: 0px 2px 20px 0px rgba(39, 53, 70, 0.2);
    border-radius: 4px;
    border: 1px solid #DDDDE3;
    white-space: nowrap;
    .tooltip-title-hz0525, .tooltip-description-hz0525 {
      margin: 0;
    }
    .tooltip-title-hz0525 {
      font-weight: 600;
    }
  }
  &:hover .tooltip-box-hz0525 {
    display: inline-block;
  }
}

.select-hz0525 {
  display: inline-flex;
  align-items: center;
  .select-icon-hz0525 {
    width: 24px;
    height: 24px;
  }
  .select-content-hz0525 {
    box-sizing: border-box;
    position: relative;
    padding: 0 4px;
    width: 120px;
    height: 24px;
    line-height: 24px;
    background: #FFFFFF;
    border-radius: 2px;
    border: 1px solid #DDDDE3;

    .placeholder-hz0525 {
      color: #DDDDE3;
    }
    .select-text-hz0525 {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
      width: 100%;
    }

    .select-list-hz0525 {
      z-index: 1;
      position: absolute;
      top: calc(100% + 2px);
      left: 0;
      margin: 0;
      padding: 0;
      min-width: 100%;
      background: #FAFAFA;
      box-shadow: 0px 2px 20px 0px rgba(39, 53, 70, 0.2);
      border-radius: 4px;
      border: 1px solid #DDDDE3;
      white-space: nowrap;
      .select-option-hz0525 {
        cursor: pointer;
        list-style: none;
        height: 20px;
        line-height: 20px;
        padding: 0 24px;
        position: relative;
        &:hover {
          background: #CCEEFF;
        }
        &.current-option:before {
          position: absolute;
          display: inline-block;
          width: 8px;
          height: 8px;
          content: '';
          background: url('../../../../plugins/image-editor/assets/check-icon.png') no-repeat 50% 50%;
          border: 1px dashed currentColor;
          top:50%;
          left: 6px;
          transform: translateY(-50%);
        }
      }
    }
  }
}

.cancel-button-hz0525 {
  cursor: pointer;
  box-sizing: border-box;
  display: inline-block;
  width: 120px;
  height: 32px;
  line-height: 30px;
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #DDDDE3;
}
.submit-button-hz0525 {
  cursor: pointer;
  margin-left: 16px;
  box-sizing: border-box;
  width: 120px;
  height: 32px;
  line-height: 30px;
  background: #009FFF;
  border-radius: 2px;
  border: 1px solid #009FFF;
  color: #fff;
}

.hz0525-draggable {
  cursor: move;
}