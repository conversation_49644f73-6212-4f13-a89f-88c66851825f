// http://officeopenxml.com/WPheaders.php
import { InitializableXmlComponent, XmlComponent } from '../xml-components';
// import { Paragraph } from "../paragraph";
// import { Table } from "../table";
import { HeaderAttributes } from './header-attributes';
import { Paragraph } from '../paragraph';
import { Table } from './../table';
import { BaseXmlComponent, IXmlResult } from '../xml-components/base';

export class Header extends InitializableXmlComponent {
    // private readonly refId: number;

    constructor(initContent?: XmlComponent) {
        super('w:hdr', initContent);

        // this.refId = referenceNumber;

        this.root.push(
            new HeaderAttributes({
                w: 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
            }),
        );
    }

    // public get ReferenceId(): number {
    //     return this.refId;
    // }

    public addParagraph(paragraph: Paragraph): void {
        this.root.push(paragraph);
    }

    public createParagraph(text?: string): Paragraph {
        const para = new Paragraph(text);
        this.addParagraph(para);
        return para;
    }

    public get Root(): (BaseXmlComponent | string)[] {
        return this.root;
    }

    public addTable(table: Table): void {
        this.root.push(table);
    }

    public prepForXml(): IXmlResult {
        const result = super.prepForXml();
        const key = this.rootKey;
        const text = `<${key}${result.attrs}>${result.text}</${key}>`;
        return {
          text,
          attrs: null
        };
    }

    // public createTable(rows: number, cols: number): Table {
    //     const table = new Table(rows, cols);
    //     this.addTable(table);
    //     return table;
    // }
}
