import * as React from 'react';

interface IState {
    bRefresh: boolean;
}

interface IPropKey {
    name: string;
    value: string;
    disabled: string;
}

export default class SelectList extends React.Component<{}, IState> {
    private prop: IPropKey;
    private data: object[];
    private value: number;
    private disabled: boolean;
    private readonly: boolean;
    private x: number;
    private y: number;
    private width: number;
    private visible: boolean;
    private bActive: boolean;
    private id: number;
    // private label: string;
    // private onUpdate: (label: string) => void;
    private onChange: (value: any, item?: any, e?: any) => void;
    private onClose: (id: number) => void;
    private _ref: any;
    private _ulRef: any;

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this._ref = React.createRef();
    }

    public render(): any {
        if (this.visible === false) {
            return null;
        }

        return (
            <div className='editor-select-list' ref={this._ref} style={{width: this.width, left: this.x, top: this.y}}>
                <ul style={{position: 'relative'}}>{this.renderList()}</ul>
            </div>
        );
    }

    public componentDidMount(): void {
        this.addEvent();
    }

    public componentDidUpdate(prevProps: Readonly<{}>, prevState: Readonly<IState>, snapshot?: any): void {
        const div = (this._ref.current as HTMLDivElement);
        const li = div?.querySelector('li.active') as HTMLLIElement;
        if (!div || !li) return;
        if (li.offsetTop + li.offsetHeight > div.scrollTop + div.offsetHeight || li.offsetTop < div.scrollTop) {
            div.scrollTop = li.offsetTop;
        }
    }

    public componentWillUnmount(): void {
        if (!this._ref.current) {
            return;
        }
        this._ref.current?.ownerDocument?.removeEventListener('click', this.docClickEvent);
    }

    public setData(data: object[], value: any, disabled: boolean, readonly: boolean, options: object): void {
        this.data = data;
        this.value = value;
        this.disabled = disabled;
        this.readonly = readonly;
        const obj: any = options || {};
        this.x = obj.x;
        this.y = obj.y;
        this.width = obj.width;
        this.prop = obj.prop;
        this.id = obj.id;
        this.onClose = obj.onClose;
        this.onChange = obj.onChange;
        this.visible = true;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    public isDisplay(id: number): boolean {
        return this.visible === true && id === this.id;
    }

    public setActive(flag: boolean): void {
        this.bActive = flag;
    }

    public setValue(value: any): void {
        this.value = value;
    }

    private itemClick = (value: any, index: number, e: any): void => {
        if (this.value === value) {
            this.bActive = false;
            return;
        }
        const item = this.data[index];
        if (this.readonly === true || item[this.prop.disabled]) {
            this.bActive = true;
            return;
        }
        this.value = value;

        if (typeof this.onChange === 'function') {
            this.onChange(value, item, e);
        }
    }

    // public setId(id: number): void {
    //     this.id = id;
    // }

    private addEvent(): void {
        this._ref.current.ownerDocument.addEventListener('click', this.docClickEvent);
    }

    private docClickEvent = (): void => {
        if (this.visible === false) {
            return;
        }

        if (this.bActive === true) {
            this.bActive = false;
            return;
        }

        this.visible = false;
        if (typeof this.onClose === 'function') {
            this.onClose(this.id);
        }

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private renderList(): any {
        const datas = this.data;
        if (!datas || datas.length === 0) {
            return (<li className='no-data'>暂无数据</li>);
        }

        const prop = this.prop;
        const key = prop.name;
        const valueKey = prop.value;
        const disabledKey = prop.disabled;
        const activeValue = this.value;
        return datas.map((data, index) => {
            const value = data[valueKey];
            let className: string = '';
            if (activeValue === value) {
                className = 'active';
            }
            if (data[disabledKey] === true) {
                className += ' disabled';
            }

            return (
                <li
                    key={index}
                    className={className}
                    onClick={this.itemClick.bind(this, value, index)}
                >
                        <label>{data[key]}</label>
                </li>
            );
        });
    }
}
