import { DocumentCore } from '../../model/DocumentCore';
import { ErrorMessages } from '../commonDefines';
export  class ExternalAction {
    private bReadOnly: boolean;
    private _document: DocumentCore;
    private _bRefresh: boolean;
    constructor(documentCore: DocumentCore) {
        this._document = documentCore;
    }

    protected resetAdminMode(): void {
        this._document.resetAdminMode();
    }

    protected isEmptyDocument(): boolean {
        const contents = this._document.getAllContents();
        if (!contents || contents.length === 0) {
            this.resetAdminMode();
            throw new Error(ErrorMessages.ThrowCustomErrorMsg);
            return true;
        }
        return false;
    }

    protected set bRefresh(bRefresh: boolean) {
        this._bRefresh = bRefresh;
    }

    protected get bRefresh(): boolean {
        return this._bRefresh;
    }

    protected isRefresh(): boolean {
        const bRefresh = this._bRefresh;
        this._bRefresh = undefined;
        return bRefresh;
    }
}
