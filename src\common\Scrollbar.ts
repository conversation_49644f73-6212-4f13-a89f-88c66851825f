import { ViewModeType } from './commonDefines';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from './GlobalEvent';
// import {logger} from './log/Logger';

interface IPosition {
    x: number;
    y: number;
}

export class Scrollbar {
    private host: any;
    private documentCore: any;
    private scrollDom: any;
    private subheight: number;
    private _pageProperty: {width: number, height: number};
    private _scrollToTop: number;
    private _timeout: any;
    private _prevY: number;
    private _activeY: number;
    private _isMousedown: boolean = false;
    private _maxClientHeight: number;
    private _isActiveScroll: boolean;
    private _intervalout: any;
    private _speed: number = 40;
    private _time: number = 100;
    private _clientHeight: number;
    private _lineHeight: number;
    private _pageIndex: number = 0;
    private _bPageUp: boolean;
    private _docId: number;
    private _scale: number;
    private _position: IPosition;
    private _parentPosition: any;
    private _parentPosition2: any;
    private _pageHeight: number;
    private _bSelectAll: boolean;
    private _paddingTop: number;
    // 记录前一个inline状态
    private _prevInlineMode: boolean;
    constructor(host: any) {
        this.host = host;
        this._docId = host.docId;
        this.documentCore = host.documentCore;
        this._position = {} as any;
        this.init();
    }

    /**
     * 光标上下跳动
     * @param y1 光标顶部坐标
     * @param y2 光标的底部坐标
     */
    public scrollToYByCursor(y1: number, y2: number): void {
        if (y2 === this._prevY) {
            return;
        }
        setTimeout(() => {
            this._prevY = y2;
        }, 56);

        if (this._isMousedown !== false) {
            return;
        }

        this.cursorMove(y1, y2);
    }

    /** 跳转新的一页
     * @param position 游标的上下标位置
     */
    public scrollToNewPage(position: {y1: number, y2: number}): void {
        this._prevY = position.y2;
        if (this.bScroll(position.y1, position.y2)) {
            this.scrollTo();
            this.host.handleRefresh(10);
            setTimeout(() => { // ？？？不使用多次滚动，会出现滚动条滚动不准确的问题
                this.scrollTo();
            }, 1);
        }
        this.clearProperty();
    }

    /**
     * 跳转到上下一页
     * @param code 上一页还是下一页
     */
    public jumpNewPage(code: number): void {
        const pageIndex = this.getPageIndex();
        const clientY = this.getClientHeight();
        this.documentCore.moveCursorToNewPage(code, pageIndex, clientY);
        // this.host.handleRefresh();
        this._bPageUp = code === 33;
        if (this.host.bSelected) {
            this.host.clearSection();
        }
    }

    public setPageFlag(flag: boolean = false): void {
        this._bPageUp = flag;
    }

    private cursorMove(y1: number, y2: number): void {
        clearTimeout(this._timeout);
        this._timeout = setTimeout(() => {
            if (this.bScroll(y1, y2)) {
                this.scrollTo();
            }
            this.clearProperty();
        }, 50);
    }

    private getScale(): number {
        if (this._scale !== undefined) {
            return this._scale;
        }

        return this._scale = this.host.getScale();
    }

    private init(): void {
        this.scrollDom = this.host.getContainer();
        this.addEvent();
    }

    private addEvent(): void {
        let dom = this.scrollDom;
        if (!dom) {
            return;
        }
        this.scrollDom = dom = dom.parentNode;
        dom.addEventListener('mousedown', this.mousedown);
        dom.addEventListener('mouseup', this.mouseup);
        dom.ownerDocument.addEventListener('keydown', this.keydown);
        dom.addEventListener('mouseleave', this.mouseleave);
        dom.addEventListener('mouseenter', this.mouseenter);
        // dom.addEventListener('keydown', this.preventDefaultEvent);
        document.addEventListener('mouseup', this.docMouseUp);
        gEvent.addEvent(this._docId, gEventName.UnMounted, this.deleteEvent);
        gEvent.addEvent(this._docId, gEventName.MoveCursor, this.moveCursor);
    }

    private moveCursor = (position: any): void => {
        if (position) {
            setTimeout(() => {
                this.scrollToYByCursor(position.y1, position.y2);
            }, 2);
        }
    }

    private mousedown = (e: any) => {
        this._isMousedown = true;
        this._position = {
            x: e.clientX,
            y: e.clientY,
        };
    }

    private mouseup = (e: any) => {
        setTimeout(() => {
            this._isMousedown = false;
        }, 0);
    }

    private docMouseUp = (): void => {
        this._parentPosition = null;
        this._parentPosition2 = null;
        if (this._isActiveScroll) {
            this.clearInterTimeout();
        }
    }

    private keydown = (e: any): void => {
        if (e.keyCode === 33 || e.keyCode === 34) {
            // console.log('被禁止了')
            e.preventDefault();
        } else if (e.ctrlKey === true && e.keyCode === 65) {
                this._bSelectAll = true;
        }
    }

    // private preventDefaultEvent(e: any): void {
    //     if (e.keyCode === 33 || e.keyCode === 34) {
    //         // console.log('被禁止了')
    //         e.preventDefault();
    //     }
    // }

    private deleteEvent = (): void => {
        const dom = this.scrollDom;
        // dom.removeEventListener('mouseleave', this.mouseleave);
        // dom.removeEventListener('mouseenter', this.mouseenter);
        // dom.removeEventListener('keydown', this.preventDefaultEvent);
        if (!dom || !dom.ownerDocument) {
            console.log('undelete event Scrollbar');
            return;
        }
        dom.removeEventListener('mousedown', this.mousedown);
        dom.removeEventListener('mouseup', this.mouseup);
        document.removeEventListener('mouseup', this.docMouseUp);
        dom.ownerDocument.removeEventListener('keydown', this.keydown);
        dom.removeEventListener('mouseleave', this.mouseleave);
        dom.removeEventListener('mouseenter', this.mouseenter);
        // dom.removeEventListener('keydown', this.preventDefaultEvent);
        gEvent.deleteEvent(this._docId, gEventName.MoveCursor, this.moveCursor);
        this.clearInterTimeout();
        this.scrollDom = null;
        gEvent.deleteEvent(this._docId, gEventName.UnMounted, this.deleteEvent);
    }

    private mouseleave = (e: any): void => {
        // const date = new Date();
        if (this._isMousedown === false) {
            return;
        }

        if (this.host.isSelected() !== true) {
            return;
        }

        const position = this._position;
        let bChanged = false;
        if (position.y > e.clientY) {
            // this._isActiveScroll = true;
            bChanged = this.scrollToYByMove(-1, e.clientY);
        } else {
            // this._isActiveScroll = true;
            this._activeY = e.clientY;
            bChanged = this.scrollToYByMove(1, e.clientY);
        }
        if (!bChanged) {
            if (position.x > e.clientX) {
                this.scrollToXByMove(-1, e.clientX);
            } else {
                // this._activeX = e.clientY;
                bChanged = this.scrollToXByMove(1, e.clientX);
            }
        }
    }

    private getParentPosition(): any {
        if (this._parentPosition) {
            return this._parentPosition;
        }

        return this._parentPosition = this.scrollDom.parentNode.getBoundingClientRect();
    }

    private getParentPosition2(): any {
        if (this._parentPosition2) {
            return this._parentPosition2;
        }

        return this._parentPosition2 = this.scrollDom.ownerDocument.defaultView?.frameElement?.getBoundingClientRect();
    }

    private scrollToYByMove(direction: number, y: number): boolean {
        const date = new Date();
        const rectPostion = this.getParentPosition();
        const parentY = rectPostion.y;
        // logger.devlog({id: this.host.docId, name: 'scrollToYByMove', startTime: date,
        //                 args: {y: parentY, clientY: y, height: rectPostion.height}, result: undefined});
        // 没达到上滚条件
        if (direction === -1 && parentY < y) {
            return false;
        }

        const maxClientY = rectPostion.height + parentY;
        // 没达到下滚条件
        if (direction === 1 && maxClientY > y) {
            return false;
        }
        this._isActiveScroll = true;
        const speed = this._speed;
        let oldScrollTop: number;
        const dom = this.scrollDom;
        this._intervalout = setInterval(() => {
            const scrollTop = dom.scrollTop;

            if (direction === -1) {
                if (scrollTop > 0) {
                    this._scrollToTop = scrollTop - speed;
                    this.scrollTo();
                } else {
                    clearInterval(this._intervalout);
                }
                return;
            }

            if (scrollTop === oldScrollTop) {
                this.clearInterTimeout();
            } else {
                oldScrollTop = scrollTop;
                this._scrollToTop = scrollTop + speed;
                this.scrollTo();
            }
        }, this._time);
        return true;
    }

    private scrollToXByMove(direction: number, x: number): boolean {
        // const date = new Date();
        const rectPostion = this.getParentPosition2() || {x: 0, y: 0};
        const parentX = rectPostion.x;
        // logger.devlog({id: this.host.docId, name: 'scrollToYByMove', startTime: date,
        //                 args: {y: parentY, clientY: y, height: rectPostion.height}, result: undefined});
        // 没达到左滚条件
        if (direction === -1 && parentX < x) {
            return false;
        }

        const maxClientX = rectPostion.width + parentX;
        // 没达到右滚条件
        if (direction === 1 && maxClientX < x) {
            return false;
        }
        this._isActiveScroll = true;
        const speed = this._speed;
        let oldScrollLeft: number;
        const dom = this.scrollDom;
        this._intervalout = setInterval(() => {
            const scrollLeft = dom.scrollLeft;

            if (direction === -1) {
                if (scrollLeft > 0) {
                    dom.scrollLeft = scrollLeft - speed;
                } else {
                    clearInterval(this._intervalout);
                }
                return;
            }

            if (scrollLeft === oldScrollLeft) {
                this.clearInterTimeout();
            } else {
                oldScrollLeft = scrollLeft;
                dom.scrollLeft = scrollLeft + speed;
            }
        }, this._time);
        return true;
    }

    private clearInterTimeout(): void {
        clearInterval(this._intervalout);
        this.clearProperty();
    }

    private clearProperty(): void {
        this._maxClientHeight = undefined;
        this._clientHeight = undefined;
        this._pageProperty = undefined;
        this._scale = undefined;
        this._pageHeight = undefined;
        this._paddingTop = undefined;
    }

    private mouseenter = (e: any): void => {
        this._isActiveScroll = false;
        this.clearInterTimeout();
    }

    private getPageIndex(): number {
        const bMorePage = this.host.isMorePage();
        // const page =  this.getPageProperty();
        let pageIndex = this.host.getPageIndex();
        if (bMorePage) {
            pageIndex = Math.floor(pageIndex / 2);
        }
        return this._pageIndex = pageIndex;
    }

    private getSubHeight(): number {
        if (this.subheight !== undefined && this.subheight !== -1000001) {
            return this.subheight;
        }
        const date = new Date();
        const position = this.getParentPosition();
        const top = position.top;
        if (top === 0 && this.subheight !== -1000001) {
            this.subheight = -1000001;
        } else {
            this.subheight = top;
        }

        // logger.devlog({id: this.host.docId, name: 'getSubHeight', startTime: date, result: position.top});
        return top;
    }

    private getLineHeight(): number {
        if (this._lineHeight !== undefined) {
            return this._lineHeight;
        }

        return this._lineHeight = this.documentCore.getCurLineHeight() || 0;
    }

    private getPageHeight(): number {
        const documentCore = this.documentCore;
        const isInline = documentCore.isInlineMode();
        if (this._pageHeight !== undefined && this._prevInlineMode == isInline) {
            return this._pageHeight;
        }
        if (isInline) {
            this._pageHeight = documentCore.render().pageProperty.height;
        } else  {
            const viewMode = this.documentCore.getViewMode();
            if (viewMode !== ViewModeType.WebView) {
                const page = documentCore.getPageProperty();
                this._pageHeight = page.height + 20;
            } else {
                const page = documentCore.getPagePositionInfo(0);
                const pageHeight = page.height - page.y - (page.height - page.yLimit);
                this._pageHeight = pageHeight;
            }
        }
        return this._pageHeight;
    }

    private getPageProperty(): any {
        if (this._pageProperty) {
            return this._pageProperty;
        }
        return this._pageProperty = this.documentCore.getPageProperty();
    }

    private getClientHeight(): number {
        if (this._clientHeight !== undefined) {
            return this._clientHeight;
        }
        const dom = this.scrollDom;
        if (!dom || !dom.ownerDocument) {
            return 0;
        }
        let height = dom.ownerDocument.firstElementChild.clientHeight - this.getSubHeight();
        const editorHeight = dom.parentNode.clientHeight;
        if (height > editorHeight) {
            height = editorHeight;
        }
        return this._clientHeight = height;
    }

    private getPaddingtTop(): number {
        if (this._paddingTop !== undefined) {
            return this._paddingTop;
        }
        if (this.documentCore.getViewMode() === ViewModeType.WebView) {
            const margin = this.documentCore.getPagePositionInfo(0);
            return margin.y;
        }

        return 0;
    }

    /**
     * 判断游标是否在可见的位置：1：隐藏在底部，-1：隐藏在顶部，0：可见
     * @param y 游标的上下坐标
     */
    private cursorPosition(y1: number, y2: number): number {
        const scale = this.getScale();
        let direction: number = 0; // 0: 可见，1：隐藏在底部，-1：隐藏在顶部
        // const page: {height: number} =  this.getPageProperty();
        const pageIndex: number = this.getPageIndex();
        const clientHeight = this.getClientHeight();
        const scrollTop: number = this.scrollDom.scrollTop;
        const pageHeight = this.getPageHeight() * pageIndex * scale;
        // 这几个坐标的相对dom: scrollDom
        const lineHeight = this.getLineHeight() * scale;
        const paddingTop = this.getPaddingtTop();
        y1 = (y1 - paddingTop) * scale;
        const cursorY: number = pageHeight + lineHeight - paddingTop; // 游标Y坐标
        const maxClientY = scrollTop + clientHeight - 3; // 最大可视Y坐标
        const minClientY = scrollTop + lineHeight - 10; // 最小可视Y坐标

        if (cursorY + y2 > maxClientY) {
            direction = 1;
        } else if (cursorY + y1  < minClientY) {
            direction = -1;
        }

        return direction;
    }

    /**
     * 由于up、down键盘键的移动，然后对它的位置进行判断，是否需要对页面进行滚动
     * @param y1 游标的顶部y
     * @param y2 游标的底部y
     */
    private bScroll(y1: number, y2: number): boolean {
        if (this._bSelectAll === true) {
            this._bSelectAll = false;
            return false;
        }
        const scale = this.getScale();
        // y1 *= scale;
        // y2 *= scale;
        const type = this.cursorPosition(y1, y2 * scale);
        const bPageUp = this._bPageUp;
        if (bPageUp !== undefined) {
            this._bPageUp = undefined;
        }
        if (type === 0) {
            return false;
        }

        // const page =  this.getPageProperty();
        const pageIndex = this.getPageIndex();

        const clientHeight = this.getClientHeight();
        const height = this.getPageHeight();
        const paddingTop = this.getPaddingtTop();
        const pageHeight = height * pageIndex * scale - paddingTop;

        let scrollY: number;

        if (bPageUp !== undefined) {
            scrollY = height * scale - this.getPaddingtTop();
        } else if (type === -1) {
            this._lineHeight = undefined;
            const height2 = this.getLineHeight() * scale;
            scrollY = pageHeight + (y2 - paddingTop) * scale - height2; // 游标Y坐标
        } else {
            scrollY = pageHeight + y2 * scale - clientHeight + 20;
        }

        this._scrollToTop = scrollY;

        return true;
    }

    private scrollTo(): void {
        if (!this.scrollDom) {
            return;
        }
        this.scrollDom.scrollTop = this._scrollToTop;
    }
}
