import { XmlComponent, XmlAttributeComponent } from '../../xml-components';
import { TableRowHeight, TableRowLineRule } from '../../../../../model/core/Table/TableRowProperty';

export class TableRowProperties extends XmlComponent {
    constructor() {
        super('w:trPr');
    }

    public addRowHeight(height: TableRowHeight): TableRowProperties {
        const attrs: ITableRowPropertiesProperties = {
            value: +height.value.toFixed(2),
            type: height.getRule()
        };
        this.root.push(new TrHeight(attrs));
        return this;
    }

    public addIsTableHeader(val: string): TableRowProperties {
        this.root.push(new TblHeader(val));
        return this;
    }
}

export interface ITableRowPropertiesProperties {
    value: number;
    type: TableRowLineRule;
}

class TableRowPropertiesAttributes extends XmlAttributeComponent<ITableRowPropertiesProperties> {
    protected xmlKeys: any = {
        value: 'w:val',
        type: 'w:type',
    };
}

// tslint:disable-next-line: max-classes-per-file
export class TrHeight extends XmlComponent {
    constructor(attrs: ITableRowPropertiesProperties) {
        super('w:trHeight');
        this.root.push(new TableRowPropertiesAttributes(attrs));

    }
}

// tslint:disable-next-line: max-classes-per-file
export class TblHeader extends XmlComponent {
    constructor(flag: string) {
        super('w:tblHeader');
        this.root.push(flag);
    }
}
