export enum HistroyItemType {
    Document = 0,
    DocumentAddItem,
    DocumentRemoveItem,
    ParagraphAddItem,
    ParagraphRemoveItem,
    ParagraphProperty,
    ParagraphPropertyChange,
    ParagraphAlign,
    ParagraphLineSpacing,
    ParagraphLineSpacingRule,
    ParagraphIndFisrt,
    ParagraphIndLeft,
    ParagraphIndRight,
    ParagraphWordWrap,
    ParagraphHidden,
    ParaPortionAddItem,
    ParaPortionRemoveItem,
    ParaPortionPropertyChange,
    ParaPortionTextProperty,
    ParaPortionStartSplit,
    ParaPortionEndSplit,
    ParaPortionFont,
    ParaPortionFontSize,
    ParaPortionBold,
    ParaPortionDecorationLine,
    ParaPortionItalic,
    ParaPortionBackgroundColor,
    ParaPortionColor,
    // ParaPortionSubscript,
    // ParaPortionSuperscript,
    ParaPortionVertAlign,
    ParaPortionHidden,
    ParaPortionContentReviewInfo,
    ParaPortionReviewType,
    ParaPortionPrReviewInfo,
    ParaPortionReplaceText,
    ParaElementViewSecret,
    ParaElementVisible,
    ParaAddNewControlItem,
    ParaTextReplaceText,
    CommentAddItem,
    CommentRemoveItem,
    NewControlAddItem,
    NewControlAddItems,
    NewControlMapAddItem,
    NewControlMapAddItems,
    NewControlRemoveItem,
    NewControlRemoveLeafItems,
    NewControlRemoveLeafItems2,
    NewControlBorder,
    NewControlName,
    NewControlSerialNumber,
    NewControlTips,
    NewControlPlaceHolderPortion,
    NewControlPlaceHolder,
    NewControlPlaceHolderContent,
    NewControlCanntEdit,
    NewControlMustInput,
    NewControlShowBorder,
    NewControlShowBackgroundColor,
    NewControlCanntDelete,
    NewControlCanntCopy,
    NewControlHiddenBackground,
    NewControlReverseEdit,
    NewControlHidden,
    NewControlViewSecretType,
    NewControlContentMaxLength,
    NewControlContentFixedLength,
    NewControlContentHideHasTitle,
    NewControlNumMaxValue,
    NewControlNumMinValue,
    NewControlForceValidate,
    NewControlDateBoxFormat,
    NewControlDateBoxTime,
    NewControlDateBoxText,
    NewControlNumPrecision,
    NewControlNumUnit,
    NewControlTitle,
    NewControlPrefix,
    NewControlShowValue,
    NewControlShowRight,
    NewControlRadioShowType,
    NewControlRadioChecked,
    NewControlCheckBoxChecked,
    NewControlPrintChecked,
    NewControlRadioPortions,
    NewControlRadioPortions2,
    NewControlRadioItemSelected,
    NewControlGroup,
    NewControlSeparator,
    NewControlRetrieve,
    NewControlItemSelect,
    NewControlItemCode,
    NewControlItemValue,
    NewControlStartDate,
    NewControlEndDate,
    NewControlParent,
    NewControlParentControl,
    NewControlLabelCode,
    TableIdDescription,
    ParagraphPageBreak,
    TableAddTableName,
    TableRemoveTableNames,
    TableBorderTop,
    TableBorderBottom,
    TableBorderLeft,
    TableBorderRight,
    TableBorderInsideH,
    TableBorderInsideV,
    TableCellBorderTop,
    TableCellBorderBottom,
    TableCellBorderLeft,
    TableCellBorderRight,
    TableCellMargins,
    TableCellGridSpan,
    TableCellProtected,
    TableCellVertAlign,
    TableCellVerticalMerge,
    TableRowHeight,
    TableRowAddCell,
    TableRowRemoveCell,
    TableRowProperty,
    TableRowTableHeader,
    TableRowAddRow,
    TableRowRemoveRow,
    TableFixedRowHeight,
    TableFixedColWidth,
    TableReadOnly,
    TableCanAddRow,
    TableCanDeleteRow,
    TableDeleteProtect,
    TableTableGrid,
    TableContentReviewType,
    GraphicObjectName,
    GraphicObjectsAddParaImage,
    GraphicObjectsRemoveParaImage,
    GraphicObjectSrc,
    GraphicObjectWidth,
    GraphicObjectHeight,
    GraphicObjectSizeLocked,
    GraphicObjectCopyProtect,
    GraphicObjectDeleteLocked,
    GraphicObjectPreserveAspectRatio,
    GraphicObjectSvgElem,
    ImageSelectionState,
    RegionName,
    RegionTitle,
    RegionTitleVisible,
    RegionHidden,
    RegionReverseEdit,
    RegionDeleteProtect,
    RegionEditProtect,
    RegionReviewType,
    ManagerAddRegions,
    ManagerAddRegionIndexs,
    ManagerRemoveRegions,
    ManagerRemoveRegionIndexs,
    SectionPageSize,
    SectionPageMargins,
    SectionPageMarginsHeader,
    SectionPageMarginsFooter,
    SectionTitlePage,
    SectionFirstPageDiff,
    SectionHeaderDefault,
    SectionFooterDefault,
    SectionHeaderFirst,
    SectionFooterFirst,
    TableCellSlashChange,
    NumberingAddNum,
    NumberingRemoveNum,
    NumberingAddLvl,
    NumberingRemoveLvl,
    NumberingChangeParaNumTr,

    DocumentContentAddItem,
    DocumentContentRemoveItem,
}

export enum HistoryDescriptionType {
    DocumentCompositeInput = 0,
    DocumentCompositeInputReplace,
    DocumentAddComment,
    DocumentDeleteComment,
    DocumentAddLetterUnion,
    DocumentSetParagraphProperty,
    DocumentSetTextProperty,
    DocumentEnterButton,
    DocumentBackspaceButton,
    DocumentDeleteButton,
    DocumentAddTab,
    DocumentSetNewControlProperty,
    DocumentSetNewControlText,
    DocumentSetNewControlPropertyAndText,
    DocumentPasteContent,
    DocumentAddNewTable,
    DocumentAddNewControl,
    DocumentAddImage,
    DocumentInsertFile,
    DocumentInsertText,
    DocumentSetGraphicObject,
    DocumentAcceptAllRevisions,
    DocumentRejectAllRevisions,
    DocumentAddRegion,
    DocumentAddHeader,
    DocumentAddFooter,
    DocumentSetTableProperty,
    DocumentSetRegionProperty,
    DocumentAddTableRow,
    DocumentAddTableCol,
    DocumentMoveTableBorder,
    DocumentAddPageBreak,
    DocumentCutEvent,
    DocumentAddPageNum,
    DocumentAddPageNumByText,
    DocumentSetPageProperty,
    DocumentSetSectionFirstPage,
    DocumentSetSectionFirstPageDiff,
    DocumentSetSectionHdrFtrDistance,
    TableCellProtected,
    TableCalcFormula,
    TableCalcFormula2,
    DocumentDeleteAllRadioItem,
    DocumentSetNewControlDateTime,
    DocumentSetRadioSelectedValueByXY,
    DocumentSetRadioSelectedByCode,
    DocumentSetRadioSelectedValue,
    DocumentSetRadioSelectedByIndex,
    DocumentSetRadioCodeAndValueByArray,
    DocumentSetCheckboxCaption,
    DocumentSetCheckboxStatus,
    DocumentDeleteRegion,
    DocumentContentDeleteRegion,
    DocumentMergeTableCells,
    DocumentRemoveTableRow,
    DocumentRemoveTableColumn,
    DocumentSplitTableCells,
    DocumentDeleteNewControl,
    DocumentTableCellProperty,
    DocumentParagrahText,
}

export const changeObjectFactory: any[] = [];

/**
 * 内容改变类型：增加、删除
 */
export enum ContentChangeType {
    ContentChangeAdd = 1,
    ContentChangeRemove,
}

// 排版类型
export enum HistroyItemRecalType {
    HistroyItemRecal_Inline = 0,
    HistroyItemRecal_HdrFtr,
    HistroyItemRecal_Drawing,
}
