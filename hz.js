const fs = require('fs-extra');
const archiver = require('archiver');
const path = require('path');

// 定义绝对路径
const projectRoot = process.cwd();
const targetRoot = path.join(path.dirname(projectRoot), 'hzeditor-encryption-server-go');

const staticFilesPath = path.join(projectRoot, 'staticfiles');
const dictIframePath = path.join(projectRoot, 'dist', 'iframe');
const zipFilePath = path.join(projectRoot, 'static.zip');
const targetZipFilePath = path.join(targetRoot, 'static.zip');

const apidocPath = path.join(projectRoot, 'apidoc');
const indexFilePath = path.join(apidocPath, 'index.html');
const renamedFilePath = path.join(apidocPath, 'helper.html');
const helperZipFilePath = path.join(projectRoot, 'helper.zip');
const targetHelperZipFilePath = path.join(targetRoot, 'helper.zip');

// 清空目录内容
async function clearDirectory(directoryPath) {
    try {
        await fs.emptyDir(directoryPath);
        console.log(`Cleared directory: ${directoryPath}`);
    } catch (err) {
        console.error(`Error clearing directory ${directoryPath}:`, err);
    }
}

// 复制目录内容
async function copyDirectory(srcPath, destPath) {
    try {
        await fs.copy(srcPath, destPath);
        console.log(`Copied contents from ${srcPath} to ${destPath}`);
    } catch (err) {
        console.error(`Error copying directory from ${srcPath} to ${destPath}:`, err);
    }
}

// 删除文件
async function removeFile(filePath) {
    try {
        if (await fs.pathExists(filePath)) {
            await fs.remove(filePath);
            console.log(`Removed file: ${filePath}`);
        } else {
            console.log(`File does not exist: ${filePath}`);
        }
    } catch (err) {
        console.error(`Error removing file ${filePath}:`, err);
    }
}

// 重命名文件
async function renameFile(oldPath, newPath) {
    try {
        if (await fs.pathExists(oldPath)) {
            if (await fs.pathExists(newPath)) {
                await removeFile(newPath);
            }
            await fs.rename(oldPath, newPath);
            console.log(`Renamed file from ${oldPath} to ${newPath}`);
        } else {
            console.log(`File does not exist: ${oldPath}`);
        }
    } catch (err) {
        console.error(`Error renaming file ${oldPath} to ${newPath}:`, err);
    }
}

// 压缩目录（可选保留目录结构）
async function zipDirectory(sourceDir, outPath, keepDirectory = false) {
    const output = fs.createWriteStream(outPath);
    const archive = archiver('zip', { zlib: { level: 9 } });
    return new Promise((resolve, reject) => {
        output.on('close', () => {
            console.log(`Zipped ${archive.pointer()} total bytes to ${outPath}`);
            resolve();
        });
        archive.on('error', (err) => reject(err));
        archive.pipe(output);
        archive.directory(sourceDir, keepDirectory ? path.basename(sourceDir) : false);
        archive.finalize();
    });
}

// 复制文件
async function copyFile(srcPath, destPath) {
    try {
        await fs.copy(srcPath, destPath);
        console.log(`Copied file from ${srcPath} to ${destPath}`);
    } catch (err) {
        console.error(`Error copying file from ${srcPath} to ${destPath}:`, err);
    }
}

// 主函数，执行所有任务
async function main() {
    try {
        await clearDirectory(staticFilesPath);
        await removeFile(zipFilePath);
        await copyDirectory(dictIframePath, staticFilesPath);
        await zipDirectory(staticFilesPath, zipFilePath, true);
        await copyFile(zipFilePath, targetZipFilePath);

        if (await fs.pathExists(indexFilePath)) {
            if (await fs.pathExists(renamedFilePath)) {
                await removeFile(renamedFilePath);
            }
            await renameFile(indexFilePath, renamedFilePath);
        }

        await zipDirectory(apidocPath, helperZipFilePath, false);
        await copyFile(helperZipFilePath, targetHelperZipFilePath);
    } catch (err) {
        console.error('Error in main function:', err);
    }
}

main();
