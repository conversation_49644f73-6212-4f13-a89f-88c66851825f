const gulp = require('gulp');
const less = require('gulp-less');
const minCss = require('gulp-csso');
const concat = require('gulp-concat');
const fs = require('fs');
const base64 = require('gulp-base64');

const makeCssStr = 'menu|toolbar|watermark';
gulp.task('makeCss2', function() {
    return gulp.src('./src/components/editor/style/!(' + makeCssStr + ').less')
        .pipe(less())
        .pipe(base64())
        .pipe(concat('style.css'))
        .pipe(minCss())
        .pipe(gulp.dest('./dist/'));
});

gulp.task('makeCss', function() {
    return gulp.src('./src/components/editor/style/+(common|' + makeCssStr + '|radio|checkbox).less')
        .pipe(less())
        .pipe(base64())
        .pipe(concat('main.css'))
        .pipe(minCss())
        .pipe(gulp.dest('./dist/'));
});

gulp.task('iconfont', function() {
    return gulp.src('./src/components/editor/style/iconfont/iconfont.css')
        .pipe(minCss())
        .pipe(gulp.dest('./dist/'));
});

gulp.task('pack', gulp.series(['makeCss2', 'makeCss', 'iconfont'], function (a1, a2) {
    fs.readFile('./dist/iconfont.css', function(err, res) {
        if (err || !res) {
            return;
        }
        const files = fs.readdirSync('./dist/fonts');
        const obj = {};
        files.forEach((file) => {
            const arrs = file.split('.');
            obj[arrs[arrs.length - 1]] = file;
        });
        const path = 'eela/fonts/';
        let icon = res.toString().replace(/\.\/iconfont\.(eot|ttf|woff)[^)]+?\)/g, (all, ext) => {
            const current = obj[ext];
            if (current) {
                return path + current + ')';
            }
            return all;
        });
        icon = icon.replace(/content:"\\e/g, 'content:"\\\\e');
        fs.readFile('./dist/main.css', function(err, content) {
            if (err || !content) {
                return;
            }
            let text = 'export default `' + icon + ';' + content.toString() + '`';
            writeFile(text);
        });
    });

    // fs.readFile('./dist/main.css', function(err, content) {
    //     if (err || !content) {
    //         return;
    //     }
    //     var text = 'export default `' + content.toString() + '`';
    //     writeFile(text);
    // });

    function writeFile(content) {
        fs.writeFile('./dist/style.js', content, { flag: 'wx' },function(err) {

            if (err) {
                return;
            }
            console.log('Saved success!');
        });
    }

    return gulp.src('./dist/style.js',{ allowEmpty: true });

}));
