import { DocumentCore } from '../../model/DocumentCore';
import {GlobalEvent as gEvent} from '../GlobalEvent';
import { APO_XMLS_ARRAY, ResultType, ViewModeType } from '../commonDefines';
import { EmrEditor } from '../../components/editor/Main';
import { ExternalAction } from './ExternalAction';
import md5 from 'js-md5';
import {PasteBtn} from '../menu/PasteBtn';
import { FormatWriter } from '../../format/writer/writer';

export default class EditDocument extends ExternalAction {
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    private _docId: number;
    private copyPaste: PasteBtn;
    constructor(host: EmrEditor) {
        super(host.state.documentCore);
        this._host = host;
        this._docId = host.docId;
        this._documentCore = host.state.documentCore;
    }

    public getSelectText(): string {
        return this._documentCore.getSelectText();
    }

    /**
     * 设置剪贴板格式，外部剪贴板与内部剪贴板是否带格式
     * @param bOut 外部粘贴内容
     * @param bIn Apollo格式内容
     */
    public setClipboardFormat(bOut: boolean, bIn: boolean): number {
        if (typeof bIn !== 'boolean' || typeof bOut !== 'boolean') {
            return ResultType.ParamError;
        }
        this._host.setCopyOptions({bInnerFormat: bIn, bOuterFormat: bOut});
        return ResultType.Success;
    }

    public setExtraCopyInformation(sCopyInformation: string): number {
        if (typeof sCopyInformation !== 'string') {
            return ResultType.ParamError;
        }

        // if (this._documentCore.isProtectedMode()) {
        //     return ResultType.ProtectedMode;
        // }

        gEvent.setEvent(this._docId, 'setExtraCopyInformation', sCopyInformation);
        this._host.setCopyOptions({sCopyInformation});
        return ResultType.Success;
    }

    public enableCopyFromExternal(bEnable: boolean): number {
        if (bEnable === undefined || typeof bEnable !== 'boolean') {
            return ResultType.ParamError;
        }

        // if (this._documentCore.isProtectedMode()) {
        //     return ResultType.ProtectedMode;
        // }

        gEvent.setEvent(this._docId, 'enableCopyFromExternal', bEnable);
        this._host.setCopyOptions({bEnable});

        return ResultType.Success;
    }

    public deleteRedundantEx(bBlankLine: boolean, bSpace: boolean, bTag: boolean, bDelPageBreak: boolean): number {
        if (!bBlankLine && !bSpace && !bTag && !bDelPageBreak) {
            return ResultType.ParamError;
        }
        
        const result = this._documentCore.deleteRedundantEx(bBlankLine, bSpace, bTag, bDelPageBreak);
        
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public isDocModified(): boolean {
        return this._documentCore.isDocModified();
    }

    public setDocModified2(bModify: boolean): number {
        if (typeof bModify !== 'boolean') {
            return ResultType.ParamError;
        }

        this._documentCore.setDocModified2(bModify);

        return ResultType.Success;
    }

    public updateEditor(bAll?: boolean): void {
        this._host.mainRefresh(true);

        const document = this._host.contentRef.current.getContainer();
        const svgs = !!document ?
                document.getElementsByClassName('page-container') : null;
        for (let index = 0; svgs && index < svgs.length; index++) {
            const element = svgs[index];
            if (element) {
                element.setAttribute('transform', 'rotate(0)');
            }
        }
    }

    public protectDoc(bProtect: boolean): number {
        if (typeof bProtect !== 'boolean') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.protectDoc(bProtect);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    public setViewMode(nType: number): number {
        // if (![1, 2].includes(nType)) {
        //     return ResultType.ParamError;
        // }

        /* IFTRUE_WATER */
        if (nType === 2) {
            return ResultType.ParamError;
        }
        /* FITRUE_WATER */

        let type: number;
        switch (nType) {
            case 1: {
                type = ViewModeType.BreakPageView;
                break;
            }
            case 2: {
                type = ViewModeType.WebView;
                break;
            }
            case 3: {
                type = ViewModeType.CompactView;
                break;
            }

            case 4: {
                type = ViewModeType.MorePage;
                break;
            }

            default: {
                return ResultType.ParamError;
            }
        }
        const res = this._documentCore.setViewMode(type);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public setEditMode(nType: number): number {
        let res: number;
        this._documentCore.resetAdminMode();
        switch (nType) {
            case 1: {
                res = this._documentCore.protectDoc(false);
                break;
            }
            case 2: {
                res = this._documentCore.protectDoc(true);
                break;
            }
            case 3: {
                if ( this._documentCore.isStrictMode() ) {
                    res = ResultType.UnEdited;
                } else {
                    this._documentCore.protectDoc(false);
                    res = this._documentCore.setEditMode(nType);
                }
                break;
            }
            case 4: {
                this._documentCore.deletePrevProtectedMode();
                res = this._documentCore.setEditMode(nType);
                break;
            }
            default: {
                return ResultType.ParamError;
            }
        }

        if (ResultType.Success === res) {
            this._host.handleRefresh();
        }
        return res;
    }

    public enableAdministratorMode(bFlag: boolean): number {
        if (typeof bFlag !== 'boolean') {
            return ResultType.ParamError;
        }

        return this._documentCore.setAdminMode(bFlag);
    }

    public isProtectedMode(): boolean {
        this._documentCore.resetAdminMode();
        return this._documentCore.isProtectedMode();
    }

    public designTemplet(bFlag: boolean): number {
        if (typeof bFlag !== 'boolean') {
            return ResultType.ParamError;
        }
        const res = this._documentCore.designTemplet(bFlag);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    public browseTemplet(nType: number, nProtected: number): number {
        if (nType !== 1 && nType !== 2) { // 2 hack
            return ResultType.ParamError;
        }
        const flag = this._documentCore.isProtectedMode();
        if (flag === true) {
            return ResultType.ProtectedMode;
        }

        const res = this._documentCore.browseTemplet(nType, nProtected);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    public undo(): void {
        this._documentCore.undo();
        this._host.handleRefresh();
    }

    public redo(): void {
        this._documentCore.redo();
        this._host.handleRefresh();
    }

    public insertNewLine(): number {
        const res = this._documentCore.insertNewLine();
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public insertSpecialCharacter(): number {
        const res = this._documentCore.insertSpecialCharacter();
        return 0;
    }


    public copy(): number {
        if (!this._host.isSelected()) {
            return ResultType.Failure;
        }

        this.getCopyPaste()
                  .copyEvent();
        return ResultType.Success;
    }

    public cut(): number {
        if (!this._host.isSelected()) {
            return ResultType.Failure;
        }

        return this.getCopyPaste()
                  .cutEvent();
        // return ResultType.Success;
    }

    public async paste(): Promise<number> {
        const flag = await this.getCopyPaste()
            .pasteText();
        return flag ? ResultType.Success : ResultType.Failure;
    }

    public delete(): number {
        if (!this._host.isSelected()) {
            return ResultType.Failure;
        }

        const res = this._documentCore.removeSelectedContent();
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return ResultType.Success;
    }

    public getTextFromDocument(): string {
        return this._documentCore.getAllText();
    }

    // public async getFileMd5(): Promise<string> {
    //     const xmlProps: any = {
    //         documentCore: this._documentCore,
    //         properties: this.getCustomPropertiesInSettings(),
    //         selectedArea: [],
    //     };
    //     const res = await new FormatWriter().generateXmls(xmlProps, APO_XMLS_ARRAY.slice(0, 4));
    //     return md5(res.join(''));
    // }

    public async getFileMd5(): Promise<string> {
        // 获取文档所有文本
        const allText = this._documentCore.getAllText();
        const sHeader = this._documentCore.getHeader();
        const sFooter = this._documentCore.getFooter();
        
        // 获取结构化信息的JSON
        const external = this._host.getEditor();
        const structsInfo = external.getStructsXmlInfoByParament();
        
        // 将文本、页眉页脚和结构化信息组合在一起，直接计算MD5
        return md5(allText + sHeader + sFooter + structsInfo);
    }

    public enableAutoSave(): void {
      return this._documentCore.enableAutoSave();
    }

    public disableAutoSave(): void {
      return this._documentCore.disableAutoSave();
    }

     public canEditInCurrentCursor(): boolean {
        this.resetAdminMode();
        if ( this._documentCore.isProtectedMode() ) {
            return false;
        }

        return this._documentCore.canInput();
    }

    public lockRefresh(bLock: boolean): number {
        if (typeof bLock !== 'boolean') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setLockRefresh(bLock);
        if (bLock) {
            this._host.handleRefresh();
        }

        return res;
    }

    private getCustomPropertiesInSettings(): Map<string, any> {
        // TODO: some logic to get "业务属性"
        const properties = new Map();
        // example data
        // properties.set('apple', 15);
        // properties.set('soul', 'dark');
        // properties.set('fire', 'fading');
        // properties.set('lords', 'throneless');
        return properties;
    }

    private getCopyPaste(): PasteBtn {
        if (!this.copyPaste) {
            const copy = this._host.getCopyPaste();
            this.copyPaste = new PasteBtn(copy);
        }

        return this.copyPaste;
    }
}
