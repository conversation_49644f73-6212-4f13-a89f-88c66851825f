// http://officeopenxml.com/WPalignment.php
import { XmlAttributeComponent, XmlComponent } from '../../xml-components';

export enum AlignmentOptions {
    // START = "start",
    // END = "end",
    CENTER = 'center',
    BOTH = 'both',
    // DISTRIBUTE = "distribute",
    LEFT = 'left',
    RIGHT = 'right',
}

export class AlignmentAttributes extends XmlAttributeComponent<{ readonly val: AlignmentOptions }> {
    protected readonly xmlKeys: any = { val: 'w:val' };
}

export class Alignment extends XmlComponent {
    constructor(type: AlignmentOptions) {
        super('w:jc');
        this.root.push(new AlignmentAttributes({ val: type }));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class Numbered extends XmlComponent {
    constructor(numId: number, type: number) {
        super('w:numPr');
        this.root.push(new NumberedType(type));
        this.root.push(new NumberedNumId(numId));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class NumberedType extends XmlComponent {
    constructor(type: number) {
        super('numType');
        this.root.push(type + '');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class NumberedNumId extends XmlComponent {
    constructor(numId: number) {
        super('value');
        this.root.push(numId + '');
    }
}
