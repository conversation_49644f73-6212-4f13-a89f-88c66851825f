import { XmlComponent, XmlAttributeComponent } from '../xml-components';
import { IEditorBackground, DEFAULT_BACKGROUND, IWaterMarkText } from '../../../../common/commonDefines';

export class Background extends XmlComponent {

    constructor() {
        super('w:background');
    }

    public setEditorBackground(editorBackground: IEditorBackground): void {
        this.addWatermark(editorBackground);
        this.addFillColor(editorBackground);
    }

    public addWatermark(editorBackground: IEditorBackground): void {
        const {watermarkEnabled, watermarkText, watermarkType, fillTextColor} = editorBackground;
        if (watermarkEnabled !== DEFAULT_BACKGROUND.watermarkEnabled ||
            watermarkType !== DEFAULT_BACKGROUND.watermarkType ||
            JSON.stringify(watermarkText) !== JSON.stringify(DEFAULT_BACKGROUND.watermarkText)) {

            const watermarkAttr: IWatermarkProperties = {
                enabled: watermarkEnabled === true ? 1 : 0,
                type: watermarkType,
                textColor: fillTextColor,
            };
            const watermark = new Watermark(watermarkAttr, watermarkText);
            this.root.push(watermark);
        }
    }

    public addFillColor(editorBackground: IEditorBackground): void {
        const {fillColorEnabled, fillColorType} = editorBackground;
        if (fillColorEnabled !== DEFAULT_BACKGROUND.fillColorEnabled ||
            fillColorType !== DEFAULT_BACKGROUND.fillColorType) {

            const fillColorAttr: IWatermarkProperties = {
                enabled: fillColorEnabled === true ? 1 : 0,
                type: fillColorType,
            };
            const fillColor = new FillColor(fillColorAttr);
            this.root.push(fillColor);
        }
    }

}

interface IWatermarkProperties {
    readonly enabled?: number;
    readonly type?: number;
    readonly textColor?: number;
}

class WatermarkAttributes extends XmlAttributeComponent<IWatermarkProperties> {
    protected readonly xmlKeys: any = {
        enabled: 'w:enabled',
        type: 'w:type',
        textColor: 'w:textColor',
    };
}

// tslint:disable-next-line: max-classes-per-file
export class Watermark extends XmlComponent {
    constructor(watermarkAttr: IWatermarkProperties, watermarkArr: IWaterMarkText[]) {
        super('w:watermark');
        this.root.push(new WatermarkAttributes(watermarkAttr));
        if (watermarkArr != null && watermarkArr.length > 0) {
            if (watermarkArr[0].text) {
                this.root.push(new WatermarkItem(watermarkArr[0]));
            }
            if (watermarkArr[1] != null && watermarkArr[1].text) {
                this.root.push(new WatermarkItem(watermarkArr[1]));
            }
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class FillColor extends XmlComponent {
    constructor(fillColorAttr: IWatermarkProperties) {
        super('w:fillColor');
        this.root.push(new WatermarkAttributes(fillColorAttr));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class WatermarkItem extends XmlComponent {
    constructor(watermarkText: IWaterMarkText) {
        super('w:watermarkItem');
        this.root.push(new WatermarkText(watermarkText.text));
        this.root.push(new WatermarkSize(watermarkText.size));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class WatermarkText extends XmlComponent {
    constructor(text: string) {
        super('text');
        this.root.push(text);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class WatermarkSize extends XmlComponent {
    constructor(size: string) {
        super('size');
        this.root.push(size);
    }
}
