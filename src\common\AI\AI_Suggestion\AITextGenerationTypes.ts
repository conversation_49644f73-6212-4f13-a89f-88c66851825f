/**
 * AI文本生成参数接口
 * 与后端结构保持一致
 */
export interface AITextGenerationParams {
  // 患者信息（可选）
  patientInfo?: {
    age?: number;
    gender?: string;
    chief_complaint?: string;
    [key: string]: any; // 其他可能的患者信息
  };
  // 实验室数据（可选）
  labData?: {
    [key: string]: number | string; // 实验室数据，键值对形式
  };
  // 现有病史文本（必填）
  historyExisting: string;
  // 需要生成的文本长度（必填）
  needGenerateLength: number;
}

/**
 * AI文本生成响应接口
 */
export interface AITextGenerationResponse {
  status: string;
  message: string;
  data: {
    choices: string[];
  };
}
