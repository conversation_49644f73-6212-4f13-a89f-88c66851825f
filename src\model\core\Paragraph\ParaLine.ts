import { VertAlignType } from '@/common/commonDefines';
import { idCounter } from '../util';

/**
 * 行信息
 */
export enum ParaLineInfo {
    DEFAULT = 0x0000,
    BreakPage  = 0x0001,  // 该行有一个PageBreak
    Empty      = 0x0002,  // 空字符串
    End        = 0x0004,  // 该段的最后一行
    RangeY     = 0x0008,  // 该行在具有流的对象之后开始
    LeftTab    = 0x0020,  // tab
    TextOnLine = 0x0080,
    SoftLine   = 0x0100,  // 软回车
    Default    = 0x0000,  //缺省
}

/**
 * 行内的range
 */
export class ParaLineRange {
    public x: number = 0; // 当前初始水平位置
    public xEnd: number = 0; // 当前段的水平限制
    public xVisible: number = 0; // 当前可见水平位置
    public width: number = 0; // 当前range的宽度

    public spaces: number = 0;  // 段中的空格数，不包括段末尾的空格

    public startPos: number = 0; // 开始portion位置索引
    public endPos: number = 0; // 结束portion位置索引
    public widthEnd: number = 0; // para_End：结束符的宽度

    constructor(x: number, xEnd: number) {
        this.x = x;
        this.xEnd = xEnd;
    }

    public shift(shiftDx: number, shiftDy: number): void {
        this.x += shiftDx;
        this.xEnd += shiftDx;
        this.xVisible += shiftDx;
    }
}

/**
 * 段落行的高度相关信息
 */
export class ParaLineMetrics {
    public ascent: number = 0;   // 文本顶端到行顶的高度---上行间距
    public descent: number = 0;  // 文本底端到行底的高度---下行间距
    public baseline: number = 0;  // 文本绘制基线
    public textHeight: number = 0; // 文本高度
    public lineGap: number = 0;  // 行间距

    constructor() {
        //
    }

    public update(textHeight: number, ascent: number, descent: number): void {

        this.ascent = ( ascent > this.ascent ) ? ascent : this.ascent;
        this.descent = ( descent > this.descent ) ? descent : this.descent;
        this.textHeight = ( textHeight > this.textHeight ) ? textHeight : this.textHeight;
        this.baseline = this.textHeight + this.ascent;

        this.lineGap = this.descent;
    }
}

/**
 * 段落行
 */
// tslint:disable-next-line: max-classes-per-file
export default class ParaLine {
    public id: number = 0;
    public top: number = 0;
    public bottom: number = 0;
    public metrics: ParaLineMetrics = new ParaLineMetrics();
    public info: ParaLineInfo = 0;

    public ranges: ParaLineRange[] = []; // 不考虑图片环绕等，目前默认只有一个range
    public vertAlign: VertAlignType;

    constructor() {
        this.id = idCounter.getNewId();
     }

     public addRange(x: number, xEnd: number): void {
        this.ranges.push(new ParaLineRange(x, xEnd));
    }

    public reset(): void {
        this.top = 0;
        this.bottom = 0;
        this.info = 0;
        this.ranges = [];
        this.vertAlign = VertAlignType.Bottom;
    }

    public setRangeStartPos(curRange: number, startPos: number): void {
        this.ranges[curRange].startPos = startPos;
    }

    public setRangeEndPos(curRange: number, endPos: number): void {
        this.ranges[curRange].endPos = endPos;
    }

    /**
     * 获取行的首个portion索引
     */
    public getStartPos(): number {
        if ( 0 >= this.ranges.length ) {
            return 0;
        }

        return this.ranges[0].startPos;
    }

    /**
     * 获取行的最后一个portion索引
     */
    public getEndPos(): number {
        if ( 0 >= this.ranges.length ) {
            return 0;
        }

        return this.ranges[this.ranges.length - 1].endPos;
    }

    public shift(shiftDx: number, shiftDy: number): void {
        this.top += shiftDy;
        this.bottom += shiftDy;

        for (let index = 0, rangesCount = this.ranges.length; index < rangesCount; index++) {
            this.ranges[index].shift(shiftDx, shiftDy);
        }
    }
}

export interface IRanges {
    range: number;
    line: number;
}
