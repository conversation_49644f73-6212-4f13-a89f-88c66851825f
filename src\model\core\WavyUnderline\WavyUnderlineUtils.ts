import { TextDecorationLineType } from '../TextProperty';
import ParaPortion from '../Paragraph/ParaPortion';
import Document from '../Document';
import Paragraph from '../Paragraph';

/**
 * 清除文本属性中的波浪线装饰
 */
export function clearWavyUnderlineFromTextProperty(textProperty: any): void {
    if (textProperty && textProperty.textDecorationLine === TextDecorationLineType.WavyUnderline) {
        textProperty.textDecorationLine = TextDecorationLineType.None;
    }
}

/**
 * 清除ParaPortion中的波浪线属性
 * 同时清除同一段落中所有具有相同wavyUnderlineId的portion的波浪线属性
 */
export function clearWavyUnderlineFromPortion(portion: ParaPortion): void {
    if (!portion) {
        return;
    }

    //没有波浪线ID且没有波浪线样式，直接返回
    if (!portion.wavyUnderlineId && 
        (!portion.textProperty || portion.textProperty.textDecorationLine !== TextDecorationLineType.WavyUnderline)) {
        return;
    }

    // 清除文本属性中的波浪线装饰
    if (portion.textProperty && portion.textProperty.textDecorationLine === TextDecorationLineType.WavyUnderline) {
        clearWavyUnderlineFromTextProperty(portion.textProperty);
    }

    // 清除ID（如果有）
    if (portion.wavyUnderlineId) {
        portion.wavyUnderlineId = undefined;
    }
}

/**
 * 清除整个文档中的所有波浪线
 * @param document 文档对象
 */
export function clearAllWavyUnderlinesFromDocument(document: Document): void {
    if (!document) {
        return;
    }

    // 获取文档的所有内容元素
    const allContents = document.getAllContents();
    
    // 遍历所有内容元素
    for (const contentElement of allContents) {
        if (contentElement.isParagraph()) {
            // 清理段落中的所有波浪线
            const paragraph = contentElement as Paragraph;
            const portions = paragraph.getContent();
            
            for (const portion of portions) {
                if (portion && portion.wavyUnderlineId) {
                    portion.wavyUnderlineId = undefined;
                    if (portion.textProperty) {
                        clearWavyUnderlineFromTextProperty(portion.textProperty);
                    }
                }
            }
        }
    }
}
