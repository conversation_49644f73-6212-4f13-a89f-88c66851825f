import { PasteType } from './copy/DataType';
import { analyzeRecordContent } from './commonMethods';
import { Reader } from '../format/reader/reader';

export function pasteTest(option: any, documentCore: any): Promise<void> {
    return new Promise ((resolve, reject) => {
        if (!option || !option.content) {
            return resolve(null);
        }
        import('./copy/Paste')
        .then((module: any) => {
            const copyParse: any = module.default;
            const paste = new copyParse();
            paste.setDocument(documentCore.getDocument());
            paste.parse([{content: option.content, type: option.type} as any], PasteType.PasteCtrlV)
            .then((res) => {
                if (!res || !res.length) {
                    return resolve(null);
                }
                documentCore.insertContent(res);
                return resolve(null);
            });
        });
    });
}

export function readFile(option: any, documentCore: any): Promise<void> {
    return new Promise ((resolve, reject) => {
        // console.log(option)
        if (!option || !option.content) {
            return resolve(null);
        }
        const document = documentCore.getDocument();
        const reader = new Reader(document);
        const props = {bInsertFile: false, bNoEndPara: false, bRecalc: true, options: null,
                        documentVersion: option.content.documentVersion, type: option.content.insertType};
        const bInsertFile = option.bInsertFile;
        if (bInsertFile === true) {
            props.bInsertFile = true;
        }

        if (null != props.documentVersion) {
            document.setDocumentVersion(props.documentVersion);
        }

        analyzeRecordContent(option.content, reader, props)
            .then((res) => {
                return resolve(null);
        });
    });
}
