import * as React from 'react';
// import { DocumentCore } from 'src/model/DocumentCore';
import { DocumentCore } from '../../../../model/DocumentCore';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../../../common/GlobalEvent';
import NewControlCascadeTip from './NewControlCascadeTip';
import CascadeBackground from './cascade-manager/CascadeBackground';
import { NewControlType, ViewModeType } from 'src/common/commonDefines';
import { getPagePadding } from '@/common/commonMethods';

interface ITipPanelProps {
    host: any;
    newControls: any[];
    regions: any[];
    documentCore: DocumentCore;
    borderActiveColor?: string; // 选中时边框颜色
    borderStyle?: string;
    content?: string; // 显示文本
    backgroundColor?: string;
    backgroundActiveColor?: string;
    svgRef: any;
}

interface IState {
    bRefresh: boolean;
}

export default class NewControlCascadeTipPanel extends React.Component<ITipPanelProps, IState> {
    private host: any;
    private borderActiveColor: string;
    private backgroundColor: string;
    private backgroundActiveColor: string;
    private content: string;
    private bVisible: boolean;
    private newControlsBounds: any[];
    private newControls: any;
    private cascadeManagerBg: CascadeBackground;

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.bVisible = false;
        this.host = this.props.host;
        this.backgroundColor = this.props.backgroundColor ? this.props.backgroundColor : '#D8D8D8';
        this.backgroundActiveColor = this.props.backgroundActiveColor
                                    ? this.props.backgroundActiveColor
                                    : '#E6F9FF';
        this.borderActiveColor = this.props.borderActiveColor ? this.props.borderActiveColor : '#009FFF';
        this.content = this.props.content ? this.props.content : '有';
        const { documentCore } = this.props;
        const newControlsBounds = this.newControlsBounds = [];
        const newControls = this.newControls = {};
        this.props.newControls.map((newControl: any) => {
            if (newControl == null) {
                return null;
            }
            const name = newControl.getNewControlName();
            const bounds = documentCore.getNewControlBounds(name);
            newControlsBounds.push({bounds: bounds.bounds[0], newControl});
            newControls[name] = bounds.bounds;
        });
    }

    public render(): any {
        if (!this.bVisible) {
            return null;
        }
        const { documentCore } = this.props;
        // cascade and sum cascade
        const cascadeManager = documentCore.getAllCascadeManagers();
        // skipped NewControl types
        const skipNewControlTypes = [NewControlType.SignatureElement];
        let scale = this.props.host.getScale();
        let subLeft = 0;
        if (documentCore.getViewMode() === ViewModeType.CompactView) {
            subLeft = getPagePadding(documentCore).left;
        }
        const newControlTips = this.newControlsBounds.map((item: any) => {
            const {newControl, bounds} = item;
            const parent = newControl.getParent();
            // skip signature box
            if (newControl.getType() === NewControlType.SignatureBox) {
                return null;
            }
            // skip signature child element
            if (parent && parent.getType() === NewControlType.SignatureBox) {
                return null;
            }
            let hasCascade = false, hasSum = false;
            if (cascadeManager) {
                hasCascade = cascadeManager.cascades.includes(newControl.getNewControlName());
                hasSum = cascadeManager.events.includes(newControl.getNewControlName());
            }
            return (
                <NewControlCascadeTip
                    key={newControl.getId()}
                    host={this.host}
                    newControl={newControl}
                    left={(bounds.x  - subLeft) * scale}
                    top={bounds.y * scale}
                    backgroundActiveColor={this.backgroundActiveColor}
                    backgroundColor={this.backgroundColor}
                    borderStyle={(newControl.isHidden() || newControl.isHiddenAtParent(true)) ? 'dashed' : 'solid'}
                    borderActiveColor={this.borderActiveColor}
                    content={this.content}
                    bDbClicked={false} // 预设
                    hasCascade={hasCascade}
                    hasSum={hasSum}
                />
            );
        });
        const regionTips = this.props.regions.map((region: any) => {
            const regionBounds = documentCore.getRegionBoundLines(region);
            if (!regionBounds || regionBounds.length === 0) {
                return null;
            }
            return (
                <NewControlCascadeTip
                    key={region.getId()}
                    host={this.host}
                    newControl={null}
                    region={region}
                    left={regionBounds[0].x - 20}
                    top={regionBounds[0].y}
                    backgroundActiveColor={this.backgroundActiveColor}
                    backgroundColor={this.backgroundColor}
                    borderStyle={(region.parentHidden(true)) ? 'dashed' : 'solid'}
                    borderActiveColor={this.borderActiveColor}
                    content={this.content}
                    bDbClicked={false} // 预设
                    hasCascade={false}
                    hasSum={false}
                />
            );
        });
        return newControlTips.concat(regionTips);
    }

    public componentDidMount(): void {
        gEvent.addEvent(this.host.docId, 'CascadeTipAttrChange', this.attributeChange);
        gEvent.addEvent(this.host.docId, 'CascadeTipVisible', this.setVisible);
        this.cascadeManagerBg = new CascadeBackground(this.newControls, this.props.host, this.props.svgRef.current);
    }

    public componentWillUnmount(): void {
        gEvent.deleteEvent(this.host.docId, 'CascadeTipAttrChange', this.attributeChange);
        gEvent.deleteEvent(this.host.docId, 'CascadeTipVisible', this.setVisible);
        this.cascadeManagerBg.removeEvent();
    }

    private setVisible = (flag: boolean): void => {
        if (flag === this.bVisible) {
            return;
        }
        this.bVisible = flag;
        this.setState({});
    }

    private attributeChange(param: Partial<ITipPanelProps>): void {
        if (param.backgroundColor && typeof param.backgroundColor === 'string') {
            this.backgroundColor = param.backgroundColor;
        }
        if (param.borderActiveColor && typeof param.borderActiveColor === 'string') {
            this.borderActiveColor = param.borderActiveColor;
        }
        if (param.content && typeof param.content === 'string') {
            this.content = param.content;
        }
        this.setState({bRefresh: !this.state.bRefresh});
    }
}
