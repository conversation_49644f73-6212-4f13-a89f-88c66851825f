import { BaseXmlComponent, escapeForXML, IXmlResult } from './base';

export type AttributeMap<T> = { [P in keyof T]: string };

export abstract class XmlAttributeComponent<T> extends BaseXmlComponent {
    // tslint:disable-next-line:readonly-keyword
    protected root: T;
    protected readonly xmlKeys: AttributeMap<T>;

    constructor(properties: T) {
        super('_attr');
        this.root = properties;
    }

    public prepForXml(): IXmlResult {
        const keys = Object.keys(this.root);
        if (!keys.length) {
            return;
        }
        let attrs = '';
        keys.forEach((key) => {
            const value = this.root[key];
            if (value !== undefined) {
                const newKey = this.xmlKeys[key];
                // attrs[newKey] = value;
                attrs += ` ${escapeForXML(newKey)}="${escapeForXML(value)}"`;
            }
        });
        return {attrs, text: undefined};
    }

    public set(properties: T): void {
        this.root = properties;
    }

    // helper function
    public getRoot(): T {
        return this.root;
    }
}
