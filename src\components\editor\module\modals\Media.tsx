
import * as React from "react";
import { ImageMediaType } from "../../../../common/commonDefines";
import Button from "../../ui/Button";
import Dialog from "../../ui/Dialog";

interface IProps {
    documentCore: any;
    visible: boolean;
    id: string;
    image?: any;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}
interface MediaDom {
    name: string;
    source: string;
    mediaType: ImageMediaType;
}
export default class MediaPanel extends React.Component<IProps, IState> {
    private medias: Map<string, MediaDom>;
    private visible: boolean;
    private title: string;
    private mediaName: string;
    private mediaRef: any;

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.mediaRef = React.createRef();
        this.medias = new Map<string, MediaDom>();
        this.visible = this.props.visible;
        this.mediaName = '';
        this.title = '多媒体设置';
    }

    public componentWillUnmount(): void {
        this.mediaRef = null;
        this.medias.clear();
        this.medias = null;
    }

    public render(): any {
        const width = 500;
        return (
            <Dialog
                visible={this.visible}
                width={(width + 30)}
                top={document.body.clientHeight / 2 - 200 + ''}
                open={this.open}
                title={this.title}
                id={this.props.id}
                footer={this.renderFooter()}
            >
                <div ref={this.mediaRef}>
                    {this.renderMedia(width)}
                </div>
            </Dialog>
        );
    }
    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private open = (): void => {
        let file = this.props.image;
        if (!file) {
            const documentCore = this.props.documentCore;
            file = documentCore.getSelectedDrawing();
        }

        if (!file || !file.mediaType) {
            return;
        }
        if (this.medias.has(file.name)) {
            const media = this.medias.get(file.name);
            if (media.source !== file.mediaSrc) {
                media.source = file.mediaSrc;
            }
        } else {
            this.medias.set(file.name, {
                name: file.name,
                mediaType: file.mediaType,
                source: file.mediaSrc
            });
        }
        this.title = file.mediaSrc === ImageMediaType.Audio ? '音频设置'
                    : file.mediaSrc === ImageMediaType.Video ? '视频设置'
                    : '多媒体设置';
        this.mediaName = file.name;
        this.setState({ bRefresh: !this.state.bRefresh });
    }

    private renderMedia(width: number): any {
        const doms = Array.from(this.medias.values(), item => {
            if (item.mediaType === ImageMediaType.Audio) {
                return (<audio
                    key={item.name}
                    id={item.name}
                    src={item.source}
                    controls={true}
                    preload='metadata'
                    style={{ width: width + 'px', height: '60px' }}
                    hidden={this.mediaName !== item.name}
                >
                    您的浏览器不支持音频元素
                </audio>);
            } else if (item.mediaType === ImageMediaType.Video) {
                return (
                    <video
                        key={item.name}
                        id={item.name}
                        src={item.source}
                        controls={true}
                        preload='metadata'
                        style={{ width: width + 'px', height: '350px' }}
                        hidden={this.mediaName !== item.name}
                    >
                        您的浏览器不支持视频元素
                    </video>
                );
            }
            return null;
        })
        .filter(item => !!item); // filter undefined
        if (doms.length) {
            return doms;
        }
        return (
            <div>无匹配的多媒体资源（支持音频、视频）:{this.mediaName}</div>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>关闭</Button>
            </span>
        );
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        // pause media
        const mediaDom = this.mediaRef.current;
        if (mediaDom) {
            const tarDom = mediaDom.querySelector(`*[id="${this.mediaName}"]`);
            // tslint:disable-next-line: no-unused-expression
            tarDom && tarDom.pause();
        }
        this.setState({ bRefresh: !this.state.bRefresh });
        this.props.close(this.props.id, bRefresh);
    }

}