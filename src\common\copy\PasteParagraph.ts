import { VNode } from './ParseNode';
import DocumentContentBase from '../../model/core/DocumentContentBase';
import Document from '../../model/core/Document';
import Paragraph from '../../model/core/Paragraph';
import ParaPortion from '../../model/core/Paragraph/ParaPortion';
import ParaDrawing, { ParaEquation } from '../../model/core/Paragraph/ParaDrawing';
import TextProperty from '../../model/core/TextProperty';
import { Table } from '../../model/core/Table';
import { TableRow } from '../../model/core/Table/TableRow';
import { TableCell } from '../../model/core/Table/TableCell';
import {ImagePro, OMediaMathPro, NodeType, StructurePro, TablePro, PortionPro} from './apollo/NodeType';
import { ParaElementType } from '../../model/core/Paragraph/ParagraphContent';
import StructCore from './StructCore';
import { Region } from '../../model/core/Region';
import { RegionCore } from './RegionCore';
import { EquationType, getFontFamilyVal, GLOBAL_DEFAULT_FONTSIZE, REGION_MAX_DEPTH } from '../commonDefines';
import { TableCore } from './TableCore';
import ParaProperty from '../../model/core/Paragraph/ParaProperty';
import { clearWavyUnderlineFromPortion } from '../../model/core/WavyUnderline/WavyUnderlineUtils';

class VNodeUnit {
    public type: number;
    public nodes: VNode[];
    public attrs?: any;
}

export class PasteParagraph {
    private numCellReg: RegExp  = /(\d+(\.\d+)?)([A-Za-z%]*)/;
    private doc: Document;
    private defaultFontSize: number;
    private parts: VNode [];
    private style: string;
    private hTitleReg: RegExp = /^h(\d)$/;
    private customStyleReg: RegExp = /^(i|em)|(b|strong)|(u)$/i;
    private struct: StructCore;
    private structNames: string[];
    private regionCore: RegionCore;
    private tableCore: TableCore;
    private drawObj: any;
    private defaultTextProp: any;
    private defaultParaProps: ParaProperty;
    constructor(doc?: Document) {
        this.defaultFontSize = GLOBAL_DEFAULT_FONTSIZE;
        this.doc = doc;
        this.parts = [];
    }

    public createEmptyBlockNode(): VNode {
        const curNode = new VNode();
        curNode.tagName = 'p';
        curNode.isBlock = true;
        curNode.children = [];
        return curNode;
    }

    public createNode(curNode: VNode, bFormat: boolean): any[] {
        if (false === this.doc.canInput()) {
            return;
        }
        let maxDepth = this.doc.getSelectionStartRegionDepth();
        if (maxDepth > REGION_MAX_DEPTH) { // 暂时不能嵌套区域
            return;
        }
        // 插入的结构化不能进行编辑
        const struct = this.struct = new StructCore(this.doc, this);
        if (struct.isCanntEdit()) {
            return;
        }
        this.structNames = [];
        // 树形转为可识别的普通结构
        const contents = this.toDoubleDimensional(curNode);
        if (!contents || contents.length === 0) {
            return;
        }
        const history = this.doc.getHistory();
        const turnOn = history.isTurnOn();
        history.turnOff();
        // 非Apollo格式时，根据当前光标，设置文本样式
        if (bFormat === false) {
            const textProps = this.doc.getTextProps(struct.isPlaceholder());
            const paraProps = this.doc.getParagraphProperty();
            if (textProps) {
                const text = new TextProperty(textProps  as any);
                this.defaultTextProp = text;
            }
            if (paraProps) {
                this.defaultParaProps = paraProps;
            }

            const currents = this.getContents(contents);
            if ( turnOn ) {
                history.turnOn();
            }

            return currents;
        }

        let bases = [];
        this.drawObj = undefined;
        contents.forEach((ele) => {
            switch (ele.tagName) {
                case 'p':
                    bases.push(this.createParagraph(ele, this.doc));
                    break;
                case 'tbl':
                    bases.push(this.createTable(ele, this.doc));
                    break;
                case 'region':
                    bases.push(this.createRegion(ele, this.doc));
                    break;
            }
        });

        if (struct.checkDatas(bases) === false) {
            this.resetPaste();
            return;
        }

        const bInfNewControl = struct.isFocusInNewControl();
        const bNewSection = struct.isNewSection();
        if (true || !bInfNewControl || bNewSection) {
            const regionManager: RegionCore = this.getRegionManager();
            const currentDepth = regionManager.getCurrentMaxDepth();
            if (currentDepth > 0 && bInfNewControl) {
                this.resetPaste();
                return;
            }
            maxDepth += currentDepth;

            if (maxDepth > REGION_MAX_DEPTH) {
                this.resetPaste();
                return;
            }
        }

        if (bases.length && bInfNewControl && !bNewSection) {
            const para = new Paragraph(undefined, this.doc);
            struct.getContentsToPara(bases, para);
            bases = [para];
            this.resetPasteStruct();
        }


        this.struct.addCascadeCaches();

        this.regionCore = null;
        this.struct = null;
        // this.structs = {};
        // this.prevIndex = undefined;
        // this.newControl = null;

        if ( turnOn ) {
            history.turnOn();
        }

        return bases;
    }

    private getContents(contents: VNode[]): any[] {
        let bases = [];
        contents.forEach((ele) => {
            if ( ele.attrs && ele.attrs['bHidden'] === 'true') {
                return;
            }
            switch (ele.tagName) {
                case 'p':
                    bases.push(this.createParagraph2(ele));
                    break;
                case 'tbl':
                    bases.push(...this.createTable2(ele));
                    break;
                case 'region':
                    bases.push(...this.createRegion2(ele));
                    break;
            }
        });
        const struct = this.struct;
        if (bases.length && struct.isFocusInNewControl() && !struct.isNewSection()) {
            const para = new Paragraph(undefined, this.doc);
            struct.getContentsToPara(bases, para);
            bases = [para];
            this.resetPasteStruct();
        }
        this.struct = null;
        return bases;
    }

    private resetPasteStruct(): void {
        this.regionCore?.resetPaste();
        this.struct?.resetPaste();
        this.tableCore?.resetPaste();
    }

    private resetPaste(): void {
        this.regionCore?.resetPaste();
        this.regionCore = null;
        this.struct.resetPaste();
        this.struct = null;
        if (this.drawObj) {
            this.drawObj.resetPasteDrawing();
        }
        this.tableCore?.resetPaste();
    }

    private getRegionManager(): RegionCore {
        if (this.regionCore) {
            return this.regionCore;
        }

        return this.regionCore = new RegionCore(this);
    }

    private createRegion2(node: VNode): any[] {
        const paras = [];
        node.children.forEach((ele) => {
            if ( ele.attrs && ele.attrs['bHidden'] === 'true') {
                return;
            }
            switch (ele.tagName) {
                case 'p':
                    paras.push(this.createParagraph2(ele));
                    break;
                case 'tbl':
                    paras.push(...this.createTable2(ele));
                    break;
                case 'region':
                    paras.push(...this.createRegion2(ele));
                    break;
            }
        });

        return paras;
    }

    private createRegion(node: VNode, parent: any): Region {
        const manager = this.getRegionManager();
        const props = manager.getRegionProps(node);
        const region = new Region(parent, this.doc, props);
        const currentParent = region.getOperateContent();
        const bFlag = node.children.length !== 1;
        let parentRegion: Region;
        if (parent !== this.doc) { // 没有父区域
            parentRegion = parent.parent;
        }
        manager.addRegion(region, parentRegion);
        node.children.forEach((ele, index) => {
            let base: any;
            switch (ele.tagName) {
                case 'p':
                    base = this.createParagraph(ele, currentParent);
                    break;
                case 'tbl':
                    base = this.createTable(ele, currentParent, );
                    break;
                case 'region':
                    base = this.createRegion(ele, currentParent);
                    break;
            }
            currentParent.addToContent(index, base, bFlag);
        });
        if (props.showTitle) {
            region.addPasteTitlePortion();
        }

        return region;
    }

    private getTableCore(): TableCore {
        if (!this.tableCore) {
            this.tableCore = new TableCore(this);
        }

        return this.tableCore;
    }

    private createTable2(node: VNode): any[] {
        const paras = [];
        node.children.forEach((row, rowIndex) => {

            row.children.forEach((cell, cellIndex) => {
                cell.children.forEach((p) => {
                    paras.push(this.createParagraph2(p));
                });
            });
        });

        return paras;
    }

    private createTable(node: VNode, parent: any): Table {
        const doc = this.doc;
        const table = new Table(parent, doc, 0, 0, []);
        let maxCol: number = 0;
        const tableCore = this.getTableCore();
        node.children.forEach((row, rowIndex) => {
            const tblRow: TableRow = table.addRow(rowIndex, 0, false);
            let cols: number = 0;
            row.children.forEach((cell, cellIndex) => {
                const paras = [];
                // let width: number;
                const attrs: any = cell.attrs || {};
                // if (attrs.width && attrs.width !== '0') {
                //     width = parseInt(attrs.width, 10);
                // }

                const tblCell = new TableCell(tblRow);
                cell.children.forEach((p) => {
                    paras.push(this.createParagraph(p, tblCell.content));
                });
                tblCell.content.addContentElements(paras);
                const spanStr = attrs[TablePro.Span];
                if (spanStr) {
                    const span = parseInt(spanStr, 10);
                    tblCell.setGridSpan(span);
                    cols += span;
                }
                tableCore.parseColAttrs(tblCell, cell.attrs);
                tblRow.addCell(cellIndex, tblRow, tblCell, false);
            });
            const rowAttrs: any = row.attrs || {};
            tableCore.parseRowAttrs(rowAttrs, tblRow);
            if (cols > maxCol) {
                maxCol = cols;
            }
        });
        const attrs = node.attrs;
        tableCore.parseTableAttrs(attrs, table);
        if (attrs[TablePro.Col + maxCol] === undefined) {
            tableCore.setTableGrid3(maxCol, attrs, table);
        } else {
            tableCore.setTableGrid(maxCol, table);
        }
        tableCore.addTable(table);
        return table;
    }

    private createParagraph2(node: VNode): Paragraph {
        const childs = node.children;
        const para = new Paragraph(undefined, this.doc);
        // if (this.drawObj === undefined) {
        //     this.drawObj = this.doc.getDrawingObjects();
        // }
        let bPlaceholder: boolean;
        for (let i = 0, ii = childs.length; i < ii; i++) {
            const content = childs[i] as any;
            if (content.tagName === 'struct') {
                if (content.attrs && content.attrs['bStart'] === true) {
                    if (content.attrs['bPlaceholder'] === '1') {
                        bPlaceholder = true;
                    }
                    if (content.attrs['title']) {
                        content.content = content.attrs['title'];
                    } else {
                        continue;
                    }
                    // content.content = STD_START_DEFAULT.borderString;
                } else {
                    bPlaceholder = false;
                    continue;
                    // content.content = STD_END_DEFAULT.borderString;
                }
            } else if (bPlaceholder === true) {
                continue;
            }
            if (content.content == null || content.tagName === 'img') {
                continue;
            }
            if ( content.attrs && content.attrs['bHidden'] === 'true') {
                continue;
            }
            const textProperty: TextProperty = this.defaultTextProp && this.defaultTextProp.copy();
            const portion = new ParaPortion(para);
            
            // 清除粘贴portion的波浪线属性
            clearWavyUnderlineFromPortion(portion);
            
            switch (content.tagName) {
                case 'img':
                    this.addImage(content, portion, textProperty);
                    break;

                case 'button':
                    this.struct.insertButton(content, portion);
                    break;

                default: {
                    this.addText(content, portion, textProperty);
                }
            }

            portion.portionContentPos = portion.content.length;
            para.addToContent(para.content.length - 1, portion);
        }
        para.paraProperty = this.defaultParaProps && this.defaultParaProps.copy();
        clearWavyUnderlineFromPortion(para.content[para.content.length - 1]);

        return para;
    }

    private createParagraph(node: VNode, root: DocumentContentBase): Paragraph {
        // const paras: Paragraph[] = [];
        const childs = node.children;
        // const structs = node.structs;
        const para = new Paragraph(root, this.doc);
        const struct = this.struct;
        const structNames: string[] = this.structNames;
        let bPlaceholder: boolean = false;
        if (this.drawObj === undefined) {
            this.drawObj = this.doc.getDrawingObjects();
        }
        for (let i = 0, ii = childs.length; i < ii; i++) {
            const content = childs[i] as any;
            if (content.tagName === 'struct') {
                let name: string;
                if (structNames.length > 0) {
                    name = structNames[structNames.length - 1];
                }
                const actAttrs = content.attrs;
                const activeName = actAttrs[StructurePro.Name];

                if (actAttrs.bStart === true) {
                    struct.setParentName(name);
                    structNames.push(activeName);
                } else {
                    if (name === activeName) {
                        structNames.pop();
                    }
                }
                const obj = (struct.insertStruct(content, para) || {}) as any;
                bPlaceholder = obj.isPlaceholder;
                continue;

            } else if ('delete' === content.tagName) {
                continue;

            }
            if (bPlaceholder === true) {
                bPlaceholder = false;
                this.struct.setPortionTextProp(structNames[structNames.length - 1],
                    'placeholder', this.getTextProperty(content.attrs));
                continue;
            }
            if (content.type === 'text' && !content.content) {
                continue;
            }
            // this.insertStruct(i, structs, para);

            const portion = new ParaPortion(para);
            
            // 清除粘贴portion的波浪线属性
            clearWavyUnderlineFromPortion(portion);
            
            let attrs = content.attrs;

            if (content.tagName === 'img' && content.parentNode?.attrs) {
                attrs = this.getFontCamelCaseName(content.parentNode.attrs.style);
            }

            const textProperty: TextProperty = this.getTextProperty(attrs);

            switch (content.tagName) {
                case 'img':
                    this.addImage(content, portion, textProperty);
                    break;

                case 'button':
                    this.struct.insertButton(content, portion);
                    break;
                default:
                    this.addText(content, portion, textProperty);
            }

            portion.portionContentPos = portion.content.length;
            para.addToContent(para.content.length - 1, portion);
        }
        const paraProperty = this.setParaProps(node.attrs);
        if ( paraProperty ) {
            para.paraProperty = paraProperty;
        }

        return para;
    }

    private getTextProperty(attrs: any): TextProperty {
        let textProperty: TextProperty;
        if (attrs) {
            if (typeof attrs === 'string') {
                attrs = this.getFontCamelCaseName(attrs);
            }
            textProperty = new TextProperty();
            textProperty.fontSize = this.getFontSize(attrs.fontSize);
            textProperty.fontFamily = getFontFamilyVal(attrs.fontFamily);
            const fontWeight = attrs.fontWeight;
            const fontStyle = attrs.fontStyle;
            textProperty.fontWeight = fontWeight === 'bold' ? 1 : parseInt(fontWeight, 10) > 500 ? 1 : 0;
            textProperty.fontStyle = (fontStyle && fontStyle.indexOf('italic') > -1) ? 1 : 0;
            textProperty.color = attrs.color;
            const vertAlign = attrs[PortionPro.VertAlign];
            textProperty.vertAlign = vertAlign ? parseInt(vertAlign, 10) : undefined;
            textProperty.textDecorationLine = attrs.textDecoration === 'underline' ? 1 : 0;
            // textProperty.lineHeight = this.getLineHeight(attrs.lineHeight, textProperty);
            textProperty.backgroundColor = attrs.backgroundColor;
        }

        return textProperty;
    }

    private addImage(item: VNode, portion: ParaPortion, textProperty: TextProperty): void {
        const imgAttrs = item.attrs as any;
        const type = parseInt(imgAttrs[ImagePro.Type], 10);
        let base: any;

        switch (type) {
            case ParaElementType.ParaMedEquation:
                base = new ParaEquation(this.doc, parseFloat(imgAttrs.width), parseFloat(imgAttrs.height),
                    imgAttrs[ImagePro.Src], null, parseInt(imgAttrs[OMediaMathPro.MathType], 10),
                    decodeURIComponent(imgAttrs[NodeType.MathValue] || ''));
                break;
            default:
                if (imgAttrs[OMediaMathPro.MathType] === EquationType.EditableSvg + '') {
                    base = new ParaEquation(this.doc, parseFloat(imgAttrs.width), parseFloat(imgAttrs.height),
                    imgAttrs[ImagePro.Src], null, parseInt(imgAttrs[OMediaMathPro.MathType], 10),
                    decodeURIComponent(imgAttrs[NodeType.MathValue] || ''));
                    break;
                }
                base = new ParaDrawing(this.doc, parseFloat(imgAttrs.width), parseFloat(imgAttrs.height),
                    imgAttrs[ImagePro.Src]);
                break;
        }
        base.deleteLocked = imgAttrs.deleteProtect === '1' ? true : false;
        base.sizeLocked = imgAttrs.sizeProtect === '1' ? true : false;
        base.preserveAspectRatio = imgAttrs.preferRelativeResize === '1' ? true : false;
        base.type = type;
        base.setPreload(true);
        // copyProtect 还没有定义
        portion.addToContent(0, base);
        this.drawObj.addInsertGraphicObject(base);
        if (textProperty) {
            portion.setProperty(textProperty);
        }
    }

    private addText(item: VNode, portion: ParaPortion, textProperty: TextProperty): void {
        const contents = item.content.split(String.fromCharCode(0xFFEC));
        const content = contents.join('');
        portion.addText(content, textProperty);
    }

    /**
     * 拼接从低级到顶级的style，再从中获取相对应的属性（优先级：从低到高）
     */
    private getStyle(node: VNode): void {
        const attrs = node.attrs as any;
        if (attrs && attrs.style) {
            this.style = this.style + ';' + attrs.style;
        }

        const parent = node.parentNode;
        if (parent && parent.tagName !== 'root') {
            this.getStyle(parent);
        }
    }

    private adjustStyle(): object {
        const str = this.style;
        if (str) {
            const style = str.replace(/&quot;/g, '\'');
            const attr = {};
            // let res: RegExpExecArray;
            // while (res = reg.exec(style)) {
            //     const key = res[1] || res[5] || res[4];
            //     const val = res[3] || res[6];
            //     attr[key] = val;
            // }
            const plainAttr = {};
            const arrs = style.split(/\s*?;\s*?/);
            for (let i = 0, ii = arrs.length; i < ii; i++) {
                const curStyle = arrs[i].trim()
                    .split(/:\s*/);
                const style1 = curStyle[0];
                const style2 = curStyle[1];
                if (!style1 || !style2) {
                    continue;
                }
                // 去除重复的
                if (plainAttr[style1]) {
                    continue;
                }
                plainAttr[style1] = style2;
                // 去掉横线 font-size => fontSize
                const curKey = style1.replace(/-(\w)/g, (match, $1) => {
                    return $1.toUpperCase();
                });
                attr[curKey] = style2;
            }

            return attr;
        }
    }

    private getFontCamelCaseName(style: string): any {
        const obj = {};
        if (!style) {
            return;
        }
        const arrs = style.split(';');
        arrs.forEach((arr) => {
            const attrs = arr.split(/:\s*/);
            if (!attrs[0]) {
                return;
            }
            const curKey = attrs[0].replace(/-(\w)/g, (match, $1) => {
                return $1.toUpperCase();
            });
            obj[curKey] = attrs[1];
        });
        return obj;
    }

    private forEachTable(node: VNode): VNode {
        node.children.forEach((row) => {
            row.children.forEach((cell) => {
                // const childs = [];
                // cell.children.forEach((p) => {
                //     childs.push();
                // });
                cell.children = this.forEachNode(cell).nodes;
            });
        });
        return node;
    }

    private forEachRegion(node: VNode): VNode {
        node.children.forEach((item) => {
            switch (item.tagName) {
                case 'para':
                    item.children = this.forEachNode(item).nodes;
                    item.tagName = 'p';
                    break;
                case 'tbl':
                    this.forEachTable(item);
                    break;
                case 'region':
                    item.children = this.forEachNode(item).nodes;
                    break;
            }
        });

        return node;
    }

    private setTableStyle(node: VNode): VNode {
        node.children.forEach((row) => {
            row.children.forEach((cell) => {
                cell.children = this.setStyle(cell.children);
            });
        });
        return node;
    }

    private setRegionStyle(node: VNode): VNode {
        node.children.forEach((item) => {
            switch (item.tagName) {
                case 'p':
                    item.children = this.setStyle([item])[0].children;
                    break;
                case 'tbl':
                    this.setTableStyle(item);
                    break;
                case 'region':
                    this.setRegionStyle(item);
                    break;
            }
        });
        return node;
    }

    private setParaProps(obj: any): any {
        if (!obj || !obj.style) {
            return;
        }
        const attrs = {};
        const datas = obj.style.split(';');
        datas.forEach((data) => {
            if (!data) {
                return;
            }
            const attr = data.split(/:\s*/);
            attrs[attr[0]] = attr[1];
        });
        const prop = new ParaProperty();
        const align = this.parseFloat(attrs['text-align']);
        const textIndex = this.parseFloat(attrs['text-index']);
        const lineHeight = this.parseFloat(attrs['line-height']);
        const lineStyle = this.parseFloat(attrs['line-type']);
        if (align !== undefined) {
            prop.alignment = align;
        }
        if (textIndex !== undefined) {
            prop.paraInd.left = textIndex;
        }
        if (lineStyle !== undefined) {
            prop.paraSpacing.lineSpacing = lineHeight;
        }
        if (lineStyle !== undefined) {
            prop.paraSpacing.lineSpacingType = lineStyle;
        }

        return prop;
    }

    private parseFloat(num: string): number {
        if (!num) {
            return;
        }
        if (isNaN(num as any)) {
            return;
        }

        return parseFloat(num);
    }

    private setStyle(parts?: VNode[]): VNode [] {
        const nodes = [];
        // 对样式进行调整
        (parts || this.parts).forEach((part) => {
            switch (part.tagName) {
                case 'table':
                    break;
                case 'tbl':
                    nodes.push(this.setTableStyle(part));
                    break;
                case 'region':
                    nodes.push(this.setRegionStyle(part));
                    break;
                default:
                    const rootNode = new VNode();
                    rootNode.tagName = part.tagName;
                    rootNode.children = [];
                    nodes.push(rootNode);
                    const datas = rootNode.children;
                    const childs = part.children;

                    if (!childs || childs.length === 0) {
                        break;
                    }
                    childs.forEach((child) => {

                        if (['struct', 'img', 'button'].includes(child.tagName)) {

                            if (rootNode.attrs === undefined) {
                                rootNode.attrs = (child.parentNode && child.parentNode.parentNode) ?
                                                    child.parentNode.parentNode.attrs : undefined;
                            }
                            datas.push(child);
                            return;
                        }
                        if (child.type !== 'text') {
                            return;
                        }
                        if (rootNode.attrs === undefined) {
                            rootNode.attrs = (child.parentNode && child.parentNode.parentNode) ?
                                                child.parentNode.parentNode.attrs : undefined;
                        }
                        const obj = new VNode();
                        obj.tagName = child.tagName;
                        obj.content = child.content;
                        this.style = '';
                        // 从树形中获取style
                        this.getStyle(child);
                        const attrs = this.adjustStyle();
                        if (attrs) {
                            obj.attrs = attrs;
                            if (child.parentNode.attrs) {
                                obj.attrs['bHidden'] = child.parentNode.attrs['bHidden'];
                            }
                        }
                        datas.push(obj);
                    });
                    if (rootNode.attrs && part.attrs) {
                        rootNode.attrs['bHidden'] = part.attrs['bHidden'];
                    }
                    break;
            }
        });

        return nodes;
    }

    /**
     * 根据行内元素创建模块元素
     * @param node 当前元素
     * @param res 累积的元素的集合
     * @param inlines 收集到的行内元素集合
     */
    private getNode(node: VNode, res: VNodeUnit, inlines: VNode[]): VNodeUnit {
        let nodes = [];
        let type: number;
        type = res.type;
        if (node.isBlock) {
            // 假如上一个还存有的行内元素
            if (inlines.length > 0) {
                const prvNode = this.createEmptyBlockNode();
                prvNode.children = inlines;
                nodes.push(prvNode);
                // datas = datas.concat(inlines);
            }

            const results = res.nodes;
            if (results.length > 0) {
                if (type === 1) {  // 假如是行内元素，进行组装
                    const curNode = this.createEmptyBlockNode();
                    // if (node.structs !== undefined) {
                    //     curNode.structs = node.structs;
                    // }
                    curNode.children = results;
                    nodes.push(curNode);
                } else if (type === 2) { // 块级元素集合
                    nodes = nodes.concat(results);
                }
            }
            nodes.length > 0 ? (type = 2) : (type = 1);
        } else { // 假如不是块级元素
            type = res.type;
            if (type === 2) { // 返回上一个块级元素集合
                if (res.nodes.length > 0) {
                    nodes.push(res.nodes);
                }
            } else if (res.nodes.length > 0) { // 返回行内元素集合
                nodes = inlines.slice()
                    .concat(res.nodes);
            }
        }

        return {
            type,
            nodes,
            attrs: node.attrs,
        };
    }

    private setStyle2(node: VNode): void {
        const reg = this.hTitleReg;
        let matchData = reg.exec(node.tagName);
        let attr: string;
        if (matchData) {
            let fontSize: number;
            switch (matchData[1]) {
                case '1':
                    fontSize = 32;
                    break;
                case '2':
                    fontSize = 24;
                    break;
                case '3':
                    fontSize = 18.72;
                    break;
                case '4':
                    fontSize = 16;
                    break;
                case '5':
                    fontSize = 13.28;
                    break;
                default:
                    fontSize = 12;
                    break;
            }
            attr = `font-size: ${fontSize}px;`;
        // tslint:disable-next-line: no-conditional-assignment
        } else if (matchData = this.customStyleReg.exec(node.tagName)) {
            if (matchData[1]) {
                attr = 'font-style: italic;';
            } else if (matchData[2]) {
                attr = 'font-weight: 700;';
            } else if (matchData[3]) {
                attr = 'text-decoration: underline;';
            }
        }
        if (attr) {
            if (node.attrs) {
                // tslint:disable-next-line: no-string-literal
                node.attrs['style'] =  (node.attrs['style'] || '') + attr;
            } else {
                node.attrs = {
                    style: attr,
                };
            }
        }
    }

    private parseNode(node: VNode, childs: VNode[]): VNodeUnit {
        let type = 1;
        let datas = [];
        this.setStyle2(node);
        if (childs && childs.length > 0) {
            let parts = [];
            let inlines = [];
            for (let i = 0, len = childs.length; i < len; i++) {
                const child = childs[i];
                if (child.tagName === 'table') {
                    continue;
                }

                if (child.tagName === 'tbl') {
                    parts.push(this.forEachTable(child));
                    continue;
                }

                if (child.tagName === 'region') {
                    parts.push(this.forEachRegion(child));
                    continue;
                }
                const res = this.forEachNode(child);
                const curNode = this.getNode(child, res, inlines);
                if (curNode.nodes.length > 0) {
                    if (curNode.type === 2) {
                        parts = parts.concat(...curNode.nodes);
                        inlines = [];
                    } else {
                        inlines = curNode.nodes;
                    }
                } else if (child.isBlock && child.parentNode && child.parentNode.tagName === 'root') {
                    parts.push(this.createEmptyBlockNode());
                }
            }
            // 假如最后都没有块级元素，则整合行内元素
            if (parts.length > 0) {
                type = 2;
                if (inlines.length > 0) {
                    const newNode = this.createEmptyBlockNode();
                    const newUnit = new VNodeUnit();
                    newUnit.type = 2;
                    newUnit.nodes = [];
                    const curNode = this.getNode(newNode, newUnit, inlines);
                    if (curNode.nodes.length > 0) {
                        parts =  parts.concat(curNode.nodes);
                    }
                }
                datas = parts;
            } else if (inlines.length > 0) {
                if (node.tagName === 'root') {
                    node.isBlock = true;
                    const newUnit = new VNodeUnit();
                    newUnit.type = 1;
                    newUnit.nodes = [];
                    const curNode = this.getNode(node, newUnit, inlines);
                    datas = curNode.nodes;
                } else {
                    datas = inlines;
                }
            }

        } else if (node.tagName === 'text') {
            const text = node.content;
            if (text) {
                node.content = text;
                // parts.push(node);
                datas.push(node);
            }

        } else if (['img', 'struct', 'button'].includes(node.tagName)) {

            datas.push(node);
        } else if (node.tagName === 'br') {
            if (node.parentNode && node.parentNode.tagName === 'root') {
                node.isBlock = true;
            }
        }

        return {
            type,
            nodes: datas,
        };
    }

    /**
     * @param node 树的每个节点
     * @return 分类节点集合
     */
    private forEachNode(node: VNode): VNodeUnit {
        const childs = node.children;
        return this.parseNode(node, childs);
    }

    private toDoubleDimensional(node: VNode): VNode [] {
        if (!node.children || node.children.length === 0) {
            return;
        }
        // 树形转换为二维
        // let parts = [];
        // let hTitleReg = /^h(\d)$/;
        // let customStyleReg = /^(i|em)|(b|strong)|(u)$/i;

        const parts = this.parts = this.forEachNode(node).nodes;
        if (parts.length === 0) {
            return;
        }

        return this.setStyle();
    }

    private getFontSize(fontSize: string): number {
        if (fontSize) {
            const matchs = fontSize.match(this.numCellReg);
            if (!matchs) {
                return this.defaultFontSize;
            }
            const cell = (matchs[3] || '').toLowerCase();
            // let num = parseInt(matchs[1]);
            // let size: number;
            switch (cell) {
                case '%':
                    return parseInt((parseFloat(matchs[1]) * this.defaultFontSize / 100).toFixed(0), 10);
                case 'pt':
                    return parseInt((parseFloat(matchs[1]) * 4 / 3).toFixed(0), 10);
                case 'em':
                case 'rem':
                    return parseInt((parseFloat(matchs[1]) * this.defaultFontSize).toFixed(0), 10);
                case 'px':
                    return parseFloat(matchs[1]);
            }
        }
        return this.defaultFontSize;
    }

    // private getLineHeight(lineHeight: string, textProperty: TextProperty): number {
    //     if (lineHeight) {
    //         const matchs = lineHeight.match(this.numCellReg);
    //         const cell = (matchs[2] || '').toLowerCase();
    //         const num = parseFloat(matchs[1]);
    //         if (cell === 'px') {
    //             return parseFloat((num / (textProperty.fontSize || 14)).toFixed(1) || '1');
    //         }
    //         if (cell === '%') {
    //             return parseFloat((num / 100).toFixed(1)) || 1;
    //         }

    //         return num || 1;
    //     }
    // }

}
