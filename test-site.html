<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>编辑器集成测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .header {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .editor-container {
      border: 1px solid #ccc;
      border-radius: 5px;
      min-height: 600px;
    }
    button {
      padding: 8px 15px;
      margin-right: 10px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #45a049;
    }
    .controls {
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>编辑器集成测试页面</h1>
      <p>这个页面展示了如何将编辑器作为第三方组件集成到网页中</p>
    </div>
    
    <div class="controls">
      <button id="btn-create">创建编辑器</button>
      <button id="btn-destroy">销毁编辑器</button>
    </div>
    
    <div class="editor-container" id="editor-wrapper">
      <!-- 编辑器将被加载到这里 -->
      <iframe id="editor-iframe" style="width:100%;height:600px;border:none;" src="http://127.0.0.1:5001/"></iframe>
    </div>
  </div>

  <script>
    document.getElementById('btn-create').addEventListener('click', function() {
      const iframe = document.getElementById('editor-iframe');
      const iframeWindow = iframe.contentWindow;
      
      // 确保iframe已加载完成
      if (iframeWindow.init && iframeWindow.createEditor) {
        // 初始化编辑器
        iframeWindow.init().then(function() {
          // 创建编辑器实例
          iframeWindow.createEditor({
            bShowMenu: true,
            bShowToolbar: true
          });
        });
      }
    });

    document.getElementById('btn-destroy').addEventListener('click', function() {
      const iframe = document.getElementById('editor-iframe');
      iframe.src = iframe.src; // 重新加载iframe
    });
  </script>
</body>
</html> 