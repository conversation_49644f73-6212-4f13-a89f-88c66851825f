interface IAreaKey {
    [key: number]: IAreaKey | string;
    c: string;
    c2: string;
    n: string;
}

export interface IAddressKey {
    code: string;
    name: string;
}

export enum AddressType {
    Province,
    City,
    County
}

class City {
    private _data: IAreaKey;
    private _provinces: IAddressKey[];
    private _citys: {[key: string]: IAddressKey[]};
    private _countys: {[key: string]: IAddressKey[]};
    constructor() {
        setTimeout(() => {
            this._init();
        }, 1000);
    }

    public get(code: string, type: AddressType): IAddressKey {
        if (!code || typeof type !== 'number') {
            return;
        }

        const datas = this._data;
        if (!datas) {
            return;
        }
        let data: IAreaKey;
        switch (type) {
            case AddressType.Province: {
                const key = code.slice(0, 2);
                data = datas[key];
                if (!data) {
                    return;
                }
                return {
                    code: key + '0000',
                    name: data.n
                };
            }
            case AddressType.City: {
                const key1 = code.slice(0, 2);
                data = datas[key1];
                if (!data) {
                    return;
                }
                const key2 = data.c2 || code.slice(2, 4);
                data = data[key2];
                if (!data) {
                    return;
                }
                return {
                    code: key1 + (data.c || key2) + '00',
                    name: data.n
                };
            }
            case AddressType.County: {
                const key1 = code.slice(0, 2);
                data = datas[key1];
                if (!data) {
                    return;
                }

                const key2 = code.slice(2, 4);
                data = data[key2];
                if (!data) {
                    return;
                }

                const key3 = code.slice(4, 6);
                data = data[key3];
                if (!data) {
                    return;
                }

                return {
                    code,
                    name: (data as any)
                };
            }
            default: {
                return;
            }
        }
    }

    public getProvinces(): IAddressKey[] {
        if (this._provinces) {
            return this._provinces;
        }
        const datas = this._data;
        const keys = Object.keys(datas);
        const arrs: IAddressKey[] = [];
        keys.forEach((key) => {
            const item = this.get(key, AddressType.Province);
            arrs.push(item);
        });
        this._provinces = arrs;
        return arrs;
    }

    public getCitys(code: string): IAddressKey[] {
        if (!code) {
            return [];
        }
        if (this._citys[code]) {
            return this._citys[code];
        }
        const prevCode = code.slice(0, 2);
        const datas = this._data[prevCode]; // 城市集合
        if (!datas) {
            return [];
        }

        const keys = Object.keys(datas);
        const arrs: IAddressKey[] = [];
        const codes = {};
        // const names = ['n', 'c2', 'c'];
        keys.forEach((key) => {
            if (!/^\d{2}$/.test(key)) {
                return;
            }
            const item = this.get(prevCode + key, AddressType.City);
            if (!item || codes[item.code]) {
                return;
            }
            codes[item.code] = true;
            arrs.push(item);
        });
        this._citys[code] = arrs;
        return arrs;
    }

    /**
     * 获取城区集合
     * @param code 市区编码
     * @returns 城市集合
     */
    public getCountys(code: string): IAddressKey[] {
        if (!code) {
            return [];
        }
        if (this._countys[code]) {
            return this._countys[code];
        }
        let prevCode = code.slice(0, 2);
        let datas = this._data[prevCode]; // 城市集合
        if (!datas) {
            return [];
        }
        if (datas.c2 === undefined) {
            const code1 = code.slice(2, 4);
            prevCode += code1;
            datas = datas[code1]; // 城区集合
            if (!datas) {
                return [];
            }
        } else {
            const mykeys = Object.keys(datas);
            let arrs = [];
            mykeys.forEach((key) => {
                if (!/^\d{2}$/.test(key)) {
                    return;
                }
                const data = datas[key];
                if (!data) {
                    return;
                }

                const others = this.getCodeAndName(data, prevCode + key);
                if (others.length) {
                    arrs = arrs.concat(others);
                }
            });
            this._countys[code] = arrs;
            return arrs;
        }

        return this._countys[code] = this.getCodeAndName(datas, prevCode);
    }

    private getCodeAndName(datas: IAreaKey, prevCode: string): IAddressKey[] {
        const keys = Object.keys(datas);
        const arrs: IAddressKey[] = [];
        keys.forEach((key) => {
            if (!/^\d{2}$/.test(key)) {
                return;
            }
            const data = datas[key];
            arrs.push({code: prevCode + key, name: data});
        });
        return arrs;
    }

    private async _init(): Promise<void> {
        const obj = await import('./resources/address.js');
        if (obj && obj.CHINA_ADDRESS) {
            this._data = obj.CHINA_ADDRESS;
        }
        this._citys = {};
        this._countys = {};
        // this.test();
    }

    // 单元测试已通过
    // private async test(): Promise<void> {
    //     const datas: any = await import('./resources/china-city.json');
    //     datas.default.forEach((p) => {
    //         let obj = this.get(p.code, AddressType.Province);
    //         if (!obj || obj.name !== p.name || obj.code !== p.code) {
    //             console.log('code: ' + p.code);
    //         }
    //         p.children.forEach((city) => {
    //             obj = this.get(city.code, AddressType.City);
    //             if ((!obj || obj.name !== city.name || obj.code !== city.code)) {
    //                 console.log('code: ' + city.code);
    //             }

    //             city.children.forEach((county) => {
    //                 obj = this.get(county.code, AddressType.County);
    //                 if (!obj || obj.name !== county.name || obj.code !== county.code) {
    //                     console.log('code: ' + county.code);
    //                 }
    //             });
    //         });
    //     });
    // }
}

// tslint:disable-next-line: sy-global-const-name
export const chinaCity = new City();
